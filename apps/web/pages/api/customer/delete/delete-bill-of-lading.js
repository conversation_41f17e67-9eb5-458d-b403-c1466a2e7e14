import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

// eslint-disable-next-line consistent-return
export default async (req, res) => {
  const session = await getServerSession(req, res, authOptions);
  const { bolId } = req.query;

  if (session) {
    if (req.method === "GET") {
      return JSON.stringify({
        ready: "Ready for BOL mutation.",
      });
    }

    if (req.method === "DELETE") {
      try {
        const response = await request({
          uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_bol_creator&deploy=customdeploy_ng_cses_rl_bol_creator&bolId=${bolId}&type=delete`,
          method: "DELETE",
          oauth: {
            consumer_key: process.env.OAUTH1_CONSUMER_KEY,
            consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
            token: process.env.OAUTH1_ACCESS_TOKEN,
            token_secret: process.env.OAUTH1_TOKEN_SECRET,
            signature_method: "HMAC-SHA256",
            realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
            version: "1.0",
          },
          body: req.body,
          headers: { "Content-Type": "application/json" },
          resolveWithFullResponse: true,
          json: true,
        });

        const data = response.body;
        console.debug('RES: " ', response);
        console.debug('Body: " ', data);
        res.json(data);
      } catch (err) {
        res.status(500).send({ 
          error: `Error processing BOL request: ${err}` 
        });
      }
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
  }
};
