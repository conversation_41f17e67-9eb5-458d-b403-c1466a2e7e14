import Link from "next/link";
import { useCallback, useEffect, useRef, useState, useTransition } from "react";
import { Box, Button, MenuItem, TextField, CircularProgress } from "@mui/material";
import Cookies from "js-cookie";
import { useRouter } from "next/router";
import { SearchOutlinedIcon, SearchResultCard } from "./styled";
// import api from "utils/__api__/products";
import { useDebounce } from "../../utils/customHooks";
import { useEvent } from "../../store/eventStore";
import { useSettings } from "../../store/zSettingsStore";

const SearchInput = () => {
  const parentRef = useRef();
  const router = useRouter();
  const { event } = useEvent();
  const [_, startTransition] = useTransition();
  const [resultList, setResultList] = useState(new Set([]));
  const [searchText, setSearchText] = useState("");
  const [productSelected, setProductSelected] = useState("");
  const [loading, setLoading] = useState(false);
  const searchTextDebounced = useDebounce(searchText, 1000);
  const { settings } = useSettings();

  const queryNetsuite = useCallback(
    async (active) => {
      let numberOfSearchWords = searchText && searchText.match(/\S+/g).length;
      if (searchTextDebounced) {
        setLoading(true);
        const response = await fetch(
          `/api/product/search?search=${searchText}&eventId=${
            event?.details?.id || Cookies.get("eventId")
          }`,
        );

        const products = await response.json();

        console.log("Words: ", numberOfSearchWords);
        console.log("Products: ", products);

        if (active) {
          let newOptions = new Set();

          if (productSelected) {
            newOptions.add(productSelected);
          }

          if (products && products?.items && products?.items.length !== 0) {
            newOptions = new Set([...newOptions, ...products.items]);
          } else {
            setResultList(new Set([]));
            setLoading(false);
          }

          // setProductOptions(products);
          setResultList(newOptions);
          setLoading(false);
        }
      }
    },
    [searchTextDebounced, event?.details?.id, searchText],
  );

  const getProducts = async (searchText) => {
    const data = await queryNetsuite(searchText);
  };

  const handleSearch = (e) => {
    startTransition(() => {
      const value = e.target?.value;
      setSearchText(value);
      if (!value) setResultList([]);
    });
  };

  useEffect(() => {
    let active = false;
    let updatedResults = new Set(resultList);

    if (searchTextDebounced === "") {
      let updatedSet = productSelected
        ? updatedResults.add(productSelected)
        : updatedResults.clear();

      setResultList(updatedSet);
      return undefined;
    }

    queryNetsuite(searchTextDebounced);

    return () => {
      active = false;
    };
  }, [searchTextDebounced, event?.details?.id]);

  const handleDocumentClick = () => setResultList([]);
  useEffect(() => {
    window.addEventListener("click", handleDocumentClick);
    return () => window.removeEventListener("click", null);
  }, []);

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      // Execute your function here
      router.push(`/product/search/${encodeURIComponent(searchText)}`);
    }
  };

  return (
    <Box
      position="relative"
      flex="1 1 0"
      maxWidth="670px"
      mx="auto"
      {...{
        ref: parentRef,
      }}
    >
      <TextField
        fullWidth
        variant="outlined"
        disabled={!event?.details?.id}
        placeholder="Searching for..."
        onChange={handleSearch}
        onKeyDown={handleKeyPress}
        InputProps={{
          sx: {
            height: 44,
            paddingRight: 0,
            borderRadius: 300,
            color: "grey.700",
            overflow: "hidden",
            "&:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: "primary.main",
            },
          },
          endAdornment: (
            <>
              {loading && (
                <Box sx={{ width: 25, marginRight: "0.5rem" }}>
                  <CircularProgress size={15} sx={{ color: settings?.custrecord_ng_cs_accent_color || "#FFF555" }} />
                </Box>
              )}
              <Button
                color="primary"
                disableElevation
                variant="contained"
                disabled={!event?.details?.id}
                onClick={() =>
                  router.push(
                    `/product/search/${encodeURIComponent(searchText)}`,
                  )
                }
                sx={{
                  px: "3rem",
                  height: "100%",
                  borderRadius: "0 300px 300px 0",
                }}
              >
                Search
              </Button>
            </>
          ),
          startAdornment: <SearchOutlinedIcon fontSize="small" />,
        }}
      />

      {resultList?.size > 0 && (
        <SearchResultCard elevation={2}>
          {Array.from(resultList).map((item) => (
            <Link
              href={`/product/${encodeURIComponent(item.id)}`}
              key={item.id}
            >
              <MenuItem key={item.id}>
                {item?.values?.custitem_ng_cs_web_display_name}
              </MenuItem>
            </Link>
          ))}
        </SearchResultCard>
      )}
    </Box>
  );
};
export default SearchInput;
