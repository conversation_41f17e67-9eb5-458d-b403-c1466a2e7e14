import Head from "next/head";
import Script from "next/script";
import React from "react";
import Router from "next/router";
import nProgress from "nprogress";
import { SWRDevTools } from "swr-devtools";
import { SWRConfig } from "swr";
import { AppCacheProvider } from "@mui/material-nextjs/v14-pagesRouter";
import { appWithTranslation } from "next-i18next";
import { SessionProvider } from "next-auth/react";
import { LicenseInfo } from "@mui/x-license-pro";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { LocalizationProvider } from "@mui/x-date-pickers-pro";
import RTL from "components/RTL";
import MuiTheme from "../src/theme/MuiTheme";
import OpenGraphTags from "utils/OpenGraphTags";
import { AppProvider } from "contexts/AppContext";
import AppSettingsProvider from "contexts/AppSettingContext";
import SnackbarProvider from "components/SnackbarProvider";
import "nprogress/nprogress.css";
import "simplebar-react/dist/simplebar.min.css";
import Loader from "components/loader";
import { AuthConsumer } from "contexts/AuthContext";
import CustomerTrackingProvider from "../src/contexts/CustomerTrackingContext";
import { AuthProvider } from "../src/contexts/AuthContext";
import { Analytics } from "@vercel/analytics/react"

import "../src/__server__";
import "../src/styles/globals.css";
import "../src/styles/rich-text.css";

const fetcher = (...args) => fetch(...args).then((res) => res.json());

// Binding events.
Router.events.on("routeChangeStart", () => nProgress.start());
Router.events.on("routeChangeComplete", () => nProgress.done());
Router.events.on("routeChangeError", () => nProgress.done());
// small change
nProgress.configure({
  showSpinner: false,
});

LicenseInfo.setLicenseKey(
  "a156405a5159d87af3256abee7409310Tz0xMDc0NzUsRT0xNzcwNTA4Nzk5MDAwLFM9cHJlbWl1bSxMTT1zdWJzY3JpcHRpb24sUFY9aW5pdGlhbCxLVj0y",
);

const App = (props) => {
  const { Component, pageProps } = props;
  const getLayout = Component.getLayout ?? ((page) => page);
  return (
    <AppCacheProvider {...props}>
      <Head>
        <meta charSet="utf-8" />
        <meta name="description" content="ConventionSuite Events." />
        <meta name="viewport" content="initial-scale=1, width=device-width" />
        <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
        <OpenGraphTags />
        <title>ConventionSuite Events</title>
      </Head>
      <SessionProvider session={pageProps.session} refetchInterval={5 * 60}>
        <SWRDevTools>
          <SWRConfig
            value={{
              fetcher,
              refreshInterval: 0, // Disable automatic refresh for better performance
              revalidateOnFocus: false, // Don't refetch on window focus
              revalidateOnReconnect: false, // Don't refetch on reconnect
              shouldRetryOnError: true,
              errorRetryCount: 2,
              errorRetryInterval: 5000,
              dedupingInterval: 2000, // Dedupe requests within 2 seconds
              onError: (error, key) => {
                // Log errors in development
                if (process.env.NODE_ENV === "development") {
                  console.error(`SWR Error for ${key}:`, error);
                }
              },
            }}
          >
            <AppSettingsProvider>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <AppProvider>
                  <AuthProvider>
                    <CustomerTrackingProvider>
                      <MuiTheme>
                        <SnackbarProvider>
                          <AuthConsumer>
                            {(user) =>
                              <>
                                {!user.loading ? (
                                  <RTL>
                                    {getLayout(<Component {...pageProps} />)}
                                  </RTL>
                                ) : (
                                  <Loader />
                                )}
                                <Analytics />
                              </>
                            }
                          </AuthConsumer>
                        </SnackbarProvider>
                      </MuiTheme>
                    </CustomerTrackingProvider>
                  </AuthProvider>
                </AppProvider>
              </LocalizationProvider>
            </AppSettingsProvider>
          </SWRConfig>
        </SWRDevTools>
      </SessionProvider>
    </AppCacheProvider>
  );
};

// Only uncomment this method if you have blocking data requirements for
// every single page in your application. This disables the ability to
// perform automatic static optimization, causing every page in your app to
// be server-side rendered.
//
// App.getInitialProps = async (appContext) => {
//   // calls page's `getInitialProps` and fills `appProps.pageProps`
//   const appProps = await App.getInitialProps(appContext);

//   return { ...appProps };
// };

export default appWithTranslation(App);
