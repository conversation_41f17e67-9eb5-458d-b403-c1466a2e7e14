{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "**/.env"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_*", "OAUTH_*", "NEXTAUTH_*"], "tasks": {"dev": {"dependsOn": ["^build"], "persistent": true, "cache": false, "env": ["NODE_ENV", "PORT", "NEXT_PUBLIC_*", "OAUTH_*", "NEXTAUTH_*"]}, "dev-zones": {"dependsOn": ["^build"], "persistent": true, "cache": false, "env": ["NODE_ENV", "PORT", "NEXT_PUBLIC_*", "OAUTH_*", "NEXTAUTH_*"]}, "dev-spa": {"dependsOn": ["booking-proxy"], "persistent": true, "cache": false}, "booking-proxy": {"persistent": true, "cache": false}, "build": {"dependsOn": ["^build", "typecheck"], "inputs": ["src/**", "pages/**", "app/**", "components/**", "lib/**", "styles/**", "public/**", "next.config.*", "tailwind.config.*", "tsconfig.json", "package.json", "index.ts", "index.js", "index.d.ts"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*", "OAUTH_*", "NEXTAUTH_*"]}, "build-web": {"dependsOn": ["^build", "typecheck"], "inputs": ["src/**", "pages/**", "components/**", "lib/**", "styles/**", "public/**", "next.config.*", "tailwind.config.*", "tsconfig.json", "package.json"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*", "OAUTH_*", "NEXTAUTH_*"]}, "typecheck": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "pages/**/*.ts", "pages/**/*.tsx", "app/**/*.ts", "app/**/*.tsx", "components/**/*.ts", "components/**/*.tsx", "lib/**/*.ts", "lib/**/*.tsx", "types/**/*.ts", "tsconfig.json", "next-env.d.ts"], "outputs": []}, "lint": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "pages/**/*.ts", "pages/**/*.tsx", "pages/**/*.js", "pages/**/*.jsx", "app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "components/**/*.ts", "components/**/*.tsx", "components/**/*.js", "components/**/*.jsx", "lib/**/*.ts", "lib/**/*.tsx", "lib/**/*.js", "lib/**/*.jsx", ".eslintrc.*", "eslint.config.*", "package.json"], "outputs": []}, "lint:fix": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "pages/**/*.ts", "pages/**/*.tsx", "pages/**/*.js", "pages/**/*.jsx", "app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "components/**/*.ts", "components/**/*.tsx", "components/**/*.js", "components/**/*.jsx", "lib/**/*.ts", "lib/**/*.tsx", "lib/**/*.js", "lib/**/*.jsx", ".eslintrc.*", "eslint.config.*", "package.json"], "outputs": [], "cache": false}, "test": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "**/*.test.ts", "**/*.test.tsx", "**/*.test.js", "**/*.test.jsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*.spec.js", "**/*.spec.jsx", "jest.config.*", "vitest.config.*", "playwright.config.*", "package.json"], "outputs": ["coverage/**"]}, "test:watch": {"dependsOn": ["^build"], "persistent": true, "cache": false}, "test:coverage": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "**/*.test.ts", "**/*.test.tsx", "**/*.test.js", "**/*.test.jsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*.spec.js", "**/*.spec.jsx", "jest.config.*", "vitest.config.*", "package.json"], "outputs": ["coverage/**"]}, "clean": {"cache": false, "outputs": []}}}