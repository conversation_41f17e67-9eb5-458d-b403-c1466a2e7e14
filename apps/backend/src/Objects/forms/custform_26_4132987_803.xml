<entryForm scriptid="custform_26_4132987_803" standard="STANDARDCUSTOMRECORD_SHOW_BOOTHSFORM">
  <name>CS Show Booth Form</name>
  <recordType>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths]</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <storedWithRecord>F</storedWithRecord>
  <mainFields>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>NAME</id>
          <label>Name</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>RECORDID</id>
          <label>ID</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>OWNER</id>
          <label>Owner</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>CREATED</id>
          <label>Date Created</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>LASTMODIFIED</id>
          <label>Last Modified</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ISINACTIVE</id>
          <label>Inactive</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>PARENT</id>
          <label>Parent</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_number]</id>
          <label>Booth Number</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_show_table]</id>
          <label>CS Event</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_exhibitor]</id>
          <label>Booth Billing Party</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_actual_exhibitor]</id>
          <label>Booth Exhibitor</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_contact]</id>
          <label>Booth Contact</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_contact_email]</id>
          <label>Booth Contact Email</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_boothsize]</id>
          <label>Booth Size</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_length]</id>
          <label>Booth Depth</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_width]</id>
          <label>Booth Width</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_addl_booths]</id>
          <label>Additional Booths</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_sortboothnum]</id>
          <label>Sortable Booth Number</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.custrecord_booth_venue]</id>
          <label>Booth Venue</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.tab_22_4132987_329]</id>
      <label>Transactions</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=custbody_booth]</id>
          <label>Transactions</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDMERGE</id>
      <label>Mail Merge</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDMESSAGES</id>
          <label>Messages</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.tab_20_4132987_329]</id>
      <label>Uploaded Files</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_ng_cses_upload_attachment.custrecord_booth_upload]</id>
          <label>Upload Documents</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.tab_84_t1516212_420]</id>
      <label>Shipments</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_ng_cs_shipment.custrecord_ng_cs_ship_booth]</id>
          <label>CS Shipment</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDNOTES</id>
      <label>Notes</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDUSERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDWORKFLOW</id>
      <label>Workflow</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDCUSTOM</id>
      <label>Custom</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.tab_117_t1516212_155]</id>
      <label>Bill of Lading</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_bol.custrecord_ng_bol_exhib_booth]</id>
          <label>CS Bill of Lading</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.tab_116_t1516212_315]</id>
      <label>Booth360</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.sublist_18_t1516212_560]</id>
          <label>Credit Cards</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show_booths.sublist_19_t1516212_223]</id>
          <label>Contacts</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
  </tabs>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITCOPY</id>
        <label>Save &amp; Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEXT</id>
        <label>Save &amp; Next</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITAS</id>
        <label>Save As</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
