import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

export default async (req, res) => {
  const session = await getServerSession(req, res, authOptions);

  if (session) {
    try {
      const currRequest = {
        url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_pgen_web_handler&deploy=customdeploy_ng_cses_rl_pgen_web_handler&action=A`,
        method: "GET",
      };
      
      const response = await request({
        uri: currRequest.url,
        method: "GET",
        oauth: {
          consumer_key: process.env.OAUTH1_CONSUMER_KEY,
          consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
          token: process.env.OAUTH1_ACCESS_TOKEN,
          token_secret: process.env.OAUTH1_TOKEN_SECRET,
          signature_method: "HMAC-SHA256",
          realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
          version: "1.0",
        },
        resolveWithFullResponse: true,
        json: true,
      });

      const data = response.body;
      console.debug('RES: " ', response);
      console.debug('Body: " ', data);
      console.debug(data);
      
      if (Object.keys(data).length !== 0) {
        let key = data.key;
        console.debug("Key: \n", key);
        res.end(key);
      } else {
        res.status(405).end(JSON.stringify(data));
      }
    } catch (err) {
      res.json(err);
      res.status(405).end();
    }
    // https://tstdrv1516212.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=390&deploy=1&compid=TSTDRV1516212&h=fc210accc9dd582b4ff0
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
    // res.redirect('/auth/login')
  }
};
