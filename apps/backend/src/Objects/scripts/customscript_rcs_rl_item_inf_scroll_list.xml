<restlet scriptid="customscript_rcs_rl_item_inf_scroll_list">
  <description>Infinite item scroll list REST communication.</description>
  <isinactive>F</isinactive>
  <name>RCS Get Item List Infinite Scroll</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/React ConventionSuite/rcs_rl_get_items_infinite_scroll.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_rcs_rl_item_inf_scroll_list">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>RELEASED</status>
      <title>RCS Get Item List Infinite Scroll</title>
    </scriptdeployment>
  </scriptdeployments>
</restlet>