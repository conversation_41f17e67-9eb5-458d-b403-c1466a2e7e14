/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 */
define(["N/query"] /**
 * @param query
 */, function (query) {
  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(scriptContext) {
    console.log(
      "Initialized event order confirmation, scriptContext:",
      scriptContext,
    );
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(scriptContext) {
    // Update the sales orders options when the event changes
    if (scriptContext.fieldId === "custpage_event") {
      const eventId = scriptContext.currentRecord.getValue("custpage_event");

      const estimatesField =
        scriptContext.currentRecord.getField("custpage_estimates");

      const options = estimatesField.getSelectOptions();
      for (let i = options.length - 1; i >= 0; i--) {
        estimatesField.removeSelectOption({
          value: options[i].value,
        });
      }

      // Add open events as options to events multiselect
      const estimates = query
        .runSuiteQL({
          query: `
            SELECT
              transaction.id AS "value",
              'Estimate ' || transaction.tranid || ' ' || entity.altName  AS "text"
            FROM
              transaction
              JOIN entity ON
                transaction.entity = entity.id
            WHERE
              transaction.custbody_show_table = '${eventId}'
              AND transaction.type = 'Estimate'
            ORDER BY
              transaction.tranid ASC
          `,
        })
        .asMappedResults();

      estimatesField.insertSelectOption({
        value: "",
        text: "",
        isSelected: true,
      });

      estimates.forEach((option) => estimatesField.insertSelectOption(option));
    }
  }

  function printPage() {
    require(["N/url", "N/currentRecord"], (url, currentRecord) => {
      generateLink({
        url,
        currentRecord,
      });
    });
  }

  function generateLink(params) {
    const { url, currentRecord } = params;
    // Load the current record
    const currRec = currentRecord.get();
    // Redirect to the print page
    const mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    const printSuitelet = url.resolveScript({
      scriptId: "customscript_ng_cs_sl_booking_acknowlg",
      deploymentId: "customdeploy_ng_cs_sl_booking_acknowlg",
      returnExternalUrl: false,
      params: {
        print: "T",
        eventId: currRec.getValue({ fieldId: "custpage_event" }),
        estimateId: currRec.getValue({ fieldId: "custpage_estimates" }),
        title: currRec.getValue({ fieldId: "custpage_report_title" }),
        functionTypes: encodeURIComponent(
          currRec.getValue({ fieldId: "custpage_function_types" }).join(","),
        ),
        showSalesDescription: currRec.getValue({
          fieldId: "custpage_sales_description",
        }),
        showSignatureLines: currRec.getValue({
          fieldId: "custpage_signature_lines",
        }),
        showBookings: currRec.getValue({
          fieldId: "custpage_bookings",
        }),
        showFunctions: currRec.getValue({
          fieldId: "custpage_functions",
        }),
        showItems: currRec.getValue({
          fieldId: "custpage_items",
        }),
        showPricing: currRec.getValue({
          fieldId: "custpage_pricing",
        }),
        sortByDate: currRec.getValue({
          fieldId: "custpage_sort_by_date",
        }),
      },
    });

    window.open(`https://${mainUrl}${printSuitelet}`);
  }

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,

    /* CUSTOM FUNCTIONS */

    printPage: printPage,
  };
});
