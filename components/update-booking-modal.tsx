"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useCalendar } from "@/context/calendar-context"
import { useBookingForm } from "@/hooks/use-booking-form"
import { BookingFormFields } from "./booking-form-fields"
import { useBookingCart } from "@/store/booking-cart"
import { v4 as uuidv4 } from "uuid"
import type { Booking, GhostBooking } from "@/context/calendar-context"

interface UpdateBookingModalProps {
  isOpen: boolean
  onClose: () => void
  booking: Booking | GhostBooking
}

export function UpdateBookingModal({ isOpen, onClose, booking }: UpdateBookingModalProps) {
  const { updateBooking, deleteBooking, convertGhostBookingToReal } = useCalendar()
  const { addItem } = useBookingCart()
  const [isDeleting, setIsDeleting] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  // Check if this is a ghost booking
  const isGhost = "status" in booking
  const ghostBooking = isGhost ? (booking as GhostBooking) : null

  const {
    title,
    setTitle,
    description,
    setDescription,
    venueId,
    setVenueId,
    roomId,
    setRoomId,
    start,
    setStart,
    end,
    setEnd,
    isAllDay,
    setIsAllDay,
    createGhost,
    setCreateGhost,
    venueRooms,
    venues,
    handleVenueChange,
    handleGhostToggle,
    handleDateChange,
    handleTimeChange,
    handleSubmit,
    handleCancel,
    validateForm,
    ghostId,
    isEditingGhost,
  } = useBookingForm({
    booking,
    isOpen,
    onClose,
    createGhostByDefault: isGhost, // Only create ghost by default if editing a ghost
  })

  // Handle booking deletion
  const handleDelete = () => {
    setIsDeleting(true)

    try {
      if (isGhost && ghostId) {
        // Ghost bookings are cleaned up automatically in useBookingForm
        onClose()
      } else {
        // Delete real booking
        deleteBooking(booking.id)
        onClose()
      }
    } catch (error) {
      console.error("Error deleting booking:", error)
    } finally {
      setIsDeleting(false)
    }
  }

  // Handle adding to cart
  const handleAddToCart = () => {
    if (!validateForm()) return

    setIsAddingToCart(true)

    try {
      // Find the room object
      const room = venueRooms.find((r) => r.id === roomId)

      if (!room) {
        throw new Error("Room not found")
      }

      // Generate a unique ID for the cart item
      const cartItemId = uuidv4()

      // Create the booking object
      const newBooking = {
        id: cartItemId,
        title,
        description,
        venueId,
        roomId,
        start,
        end,
        isAllDay,
      }

      // Add to cart
      addItem({
        id: cartItemId,
        booking: newBooking,
        room,
        addedAt: new Date(),
      })

      // If we're editing a ghost event, delete it since the cart will create its own ghost
      if (isGhost && ghostId) {
        // The ghost will be deleted in the useBookingForm cleanup
      } else if (!isGhost) {
        // If it's a real booking, ask if they want to delete the original
        if (confirm("Do you want to delete the original booking?")) {
          deleteBooking(booking.id)
        }
      }

      onClose()
    } catch (error) {
      console.error("Error adding to cart:", error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  // Handle booking update
  const handleUpdate = () => {
    if (!validateForm()) return

    if (isEditingGhost && ghostId) {
      // Convert the ghost booking to a real booking
      convertGhostBookingToReal(ghostId)
      onClose()
    } else {
      // Update real booking
      const updatedBooking: Booking = {
        id: booking.id,
        title,
        description,
        venueId,
        roomId,
        start,
        end,
        isAllDay,
      }

      updateBooking(updatedBooking)
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isGhost ? "Confirm Booking" : "Update Booking"}</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            handleUpdate()
          }}
        >
          <BookingFormFields
            title={title}
            setTitle={setTitle}
            description={description}
            setDescription={setDescription}
            venueId={venueId}
            venues={venues}
            handleVenueChange={handleVenueChange}
            roomId={roomId}
            setRoomId={setRoomId}
            venueRooms={venueRooms}
            start={start}
            end={end}
            handleDateChange={handleDateChange}
            handleTimeChange={handleTimeChange}
            isAllDay={isAllDay}
            setIsAllDay={setIsAllDay}
            createGhost={createGhost}
            handleGhostToggle={handleGhostToggle}
          />
          <div className="flex justify-between mt-6">
            <Button type="button" variant="destructive" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleAddToCart} disabled={isAddingToCart}>
                {isAddingToCart ? "Adding..." : "Add to Cart"}
              </Button>
              <Button type="submit">{isGhost ? "Confirm Booking" : "Update"}</Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

