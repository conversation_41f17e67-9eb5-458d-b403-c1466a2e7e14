/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NAMDConfig ./amdRestConfig.json
 */
define([
  "N/https",
  "N/cache",
  "N/config",
  "N/email",
  "N/file",
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "N/auth",
  "settings",
  "lodash",
] /**
 * @param{https} https
 * @param{cache} cache
 * @param{config} config
 * @param{email} email
 * @param{file} file
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{auth} auth
 * @param{Object} settings
 * @param {() => CS_Settings} settings.getSettings
 * @param {lodash} lodash
 */, (
  https,
  cache,
  config,
  email,
  file,
  query,
  record,
  runtime,
  search,
  url,
  auth,
  settings,
  _,
) => {
  /**
   * Defines the function that is executed when a GET request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const get = (requestParams) => {
    const { email: userEmail } = requestParams;
    const CS_SETTINGS = settings.useSettings();

    const contactQuery = query
      .runSuiteQL({
        query: `SELECT *
                FROM Entity
                WHERE Entity.Isinactive = 'F'
                AND LOWER(Entity.Email) = LOWER('${userEmail}')
                ORDER BY Entity.Email`,
      })
      .asMappedResults();

    if (!contactQuery.length) {
      return JSON.stringify({
        message: "Email not found",
        type: "EMAIL_NOT_FOUND",
        status: 404,
      });
    } else {
      return JSON.stringify({
        message: "Email found",
        type: "EMAIL_FOUND",
        status: 200,
      });
    }
  };

  /**
   * Defines the function that is executed when a PUT request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body are passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const put = (requestBody) => {};

  /**
   * Defines the function that is executed when a POST request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body is passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const post = (requestBody) => {
    log.debug({ title: "📥 Incoming payload", details: requestBody });
    const companyPreferences = config.load({
      type: config.Type.COMPANY_PREFERENCES,
    });
    const dateFormat = companyPreferences.getValue("dateformat");
    const mainUrl = url.resolveDomain({
      hostType: url.HostType.APPLICATION,
    });
    let { email: userEmail, redirect: redirectTo } = requestBody;

    const CS_SETTINGS = settings.useSettings();

    const notificationAuthor =
      CS_SETTINGS.custrecord_web_notification_author || -5;

    const replyToEmail = CS_SETTINGS.custrecord_admin_portal_access_email;

    // Search for the userEmail in the ENTITY table
    const customerRoleName = "Customer With OAuth & REST";
    let customerRoleId = null;
    const returnData = {
      message: "",
      status: 200,
    };

    const customerOauthRoles = query
      .runSuiteQL({
        query: `SELECT
            Role.ID,
            Role.Name,
            Role.IsInactive,
            (SELECT MAX(LoginAudit.Date) FROM LoginAudit WHERE LoginAudit.Role = Role. ID) AS LastUsed
            FROM
            Role
            WHERE
            (Role.IsInactive = 'F')
            AND
            Role.Name = '${customerRoleName}'
            ORDER BY
            Role.Name`,
      })
      .asMappedResults();

    log.audit({
      title: "🟡 Customer roles found:",
      details: customerOauthRoles,
    });

    if (customerOauthRoles.length !== 0) {
      customerRoleId = customerOauthRoles[0].id;
    }

    const contactQuery = query
      .runSuiteQL({
        query: `SELECT *
                FROM Entity
                WHERE Entity.Isinactive = 'F'
                AND LOWER(Entity.Email) = LOWER('${userEmail}')
                ORDER BY Entity.Email`,
      })
      .asMappedResults();

    if (!contactQuery.length) {
      if (notificationAuthor && replyToEmail) {
        email.send({
          author: notificationAuthor,
          replyTo: userEmail,
          recipients: replyToEmail,
          subject: "📥 New User Registration: Email not found",
          body: `Hi admin,\n A new user has attempted to register to the exhibitor portal with the email address: ${userEmail}. They are not in the system or a part of an associated customer contact. Please check the system for more details or email the customer back directly.`,
          relatedRecords: {
            entityId: notificationAuthor,
          },
        });
      }

      return JSON.stringify({
        message: "Email not found",
        type: "EMAIL_NOT_FOUND",
        status: 404,
      });
    }

    if (CS_SETTINGS.custrecord_enable_sign_up_validation === "T") {
      log.audit({ title: "🟡 Contact query", details: contactQuery });

      const contactResult = contactQuery[0];
      const entityId = contactResult.id;
      const companyId = contactResult.parent;

      const customerRecord =
        companyId &&
        record.load({
          type: record.Type.CUSTOMER,
          id: companyId,
        });

      if (!customerRecord) {
        if (notificationAuthor && replyToEmail) {
          email.send({
            author: notificationAuthor,
            replyTo: userEmail,
            recipients: replyToEmail,
            subject:
              "📥 New User Registration: Email found no related customer",
            body: `Hi admin,\n A new user has attempted to register to the exhibitor portal with the email address: ${userEmail}. They are not a part of an associated customer contact. Please check the system for more details or email the customer back directly.`,
            relatedRecords: {
              entityId: notificationAuthor,
            },
          });
        }

        return JSON.stringify({
          message: "Customer record not found",
          type: "CUSTOMER_NOT_FOUND",
          status: 404,
        });
      }

      log.debug({
        title: "✅ Customer record loaded:",
        details: `customer:${companyId}`,
      });

      log.debug({
        title: "📝 Contact object interpretation for liquid:",
        details: contactResult,
      });

      const existingRoles = customerRecord.getLineCount({
        sublistId: "contactroles",
      });

      log.debug({ title: "🙅 Line count gathered:", details: existingRoles });

      let hasCustomerCenterRole = false;
      let hasAccess = false;
      let alreadyHasAccess = false;
      let customerSaveId = null;
      let newPassword = null;
      const passwordTemplate = CS_SETTINGS.custrecord_sign_up_password;

      log.debug({ title: "🚧 Password Template:", details: passwordTemplate });

      // Loop through the contact roles and check if the contact has the customer center role and access to portal
      for (let i = 0; i < existingRoles; i++) {
        const contactId = customerRecord.getSublistValue({
          sublistId: "contactroles",
          fieldId: "contact",
          line: i,
        });
        const roleId = customerRecord.getSublistValue({
          sublistId: "contactroles",
          fieldId: "role",
          line: i,
        });
        const accessAlready = customerRecord.getSublistValue({
          sublistId: "contactroles",
          fieldId: "giveaccess",
          line: i,
        });

        const accessLine = {
          contactId: Number(contactId),
          entityFound: Number(entityId) === Number(contactId),
          roleId,
          oauthRole: Number(customerRoleId),
        };

        log.debug({ title: "🟡 Current contact:", details: accessLine });

        if (accessLine.entityFound && Number(roleId)) {
          if (Number(roleId) === Number(customerRoleId)) {
            hasCustomerCenterRole = true;
            log.audit({
              title: "🟨 Role set on contact is good!",
              details: hasCustomerCenterRole,
            });
          } else {
            customerRecord.setSublistValue({
              sublistId: "contactroles",
              fieldId: "role",
              line: i,
              value: customerRoleId,
            });
          }

          newPassword = interpretLiquid(passwordTemplate, contactResult);

          log.audit({ title: "📬 New Password Set:", details: newPassword });

          log.debug({
            title: "🟡 Auto access grant enabled?",
            details: CS_SETTINGS.custrecord_enable_sign_up_auto_access,
          });

          if (CS_SETTINGS.custrecord_enable_sign_up_auto_access === "T") {
            if (!accessAlready) {
              log.audit({
                title: "🟨 User has no access:",
                details: "Granting access",
              });

              customerRecord.setSublistValue({
                sublistId: "contactroles",
                fieldId: "giveaccess",
                line: i,
                value: true,
              });

              customerRecord.setSublistValue({
                sublistId: "contactroles",
                fieldId: "fillpassword",
                value: true,
                line: i,
              });
              customerRecord.setSublistValue({
                sublistId: "contactroles",
                fieldId: "password",
                value: newPassword,
                line: i,
              });
              customerRecord.setSublistValue({
                sublistId: "contactroles",
                fieldId: "password1",
                value: newPassword,
                line: i,
              });
              customerRecord.setSublistValue({
                sublistId: "contactroles",
                fieldId: "passwordconfirm",
                value: newPassword,
                line: i,
              });
              hasAccess = true;
            } else {
              log.audit({
                title: "🟨 User already has access:",
                details: `No need to grant access for ${userEmail}`,
              });
              alreadyHasAccess = true;
            }
          } else {
            log.audit({
              title: "🙅 Auto access disabled",
              details: `No access granted for ${userEmail}`,
            });
          }
          break;
        }
      }

      if (hasAccess) {
        returnData.message = "Email found and role updated with access";
        returnData.type = "EMAIL_FOUND_AND_ACCOUNT_STARTED";
        returnData.status = 200;
        customerSaveId = customerRecord.save();
        sendRegistrationEmail(
          contactResult,
          newPassword,
          customerSaveId,
          redirectTo,
        );
      } else if (alreadyHasAccess) {
        returnData.type = "EMAIL_FOUND_ACCOUNT_GRANTED";
        returnData.message =
          "Email found and role updated, access already granted. No password reset needed.";
        returnData.status = 200;
        customerSaveId = customerRecord.save();
      } else {
        returnData.message = "Email found but no role found";
        returnData.type = "EMAIL_FOUND_NO_ROLE";
        returnData.status = 200;
      }

      if (customerSaveId) {
        log.debug({
          title: "✅ Customer record saved:",
          details: `customer:${customerSaveId}`,
        });
      } else {
        log.error({
          title: "❌ Customer record save failed:",
          details: `customer:${customerSaveId}`,
        });
      }
    } else {
      returnData.message = "Email found but sign up validation disabled";
      returnData.type = "EMAIL_FOUND_SIGN_UP_DISABLED";
      returnData.status = 200;

      if (returnData.status === 200) {
        log.debug({
          title:
            "🟢 Email found and sign up validation disabled sending NetSuite password reset email:",
          details: returnData,
        });

        const accountId = runtime.accountId;
        // Send normal password reset email
        const pwResetUrl = `https://${mainUrl}/app/login/preparepwdreset.nl?c=${String(
          accountId,
        ).toUpperCase()}`;

        log.audit({ title: "🚀 Sending reset to URL:", details: pwResetUrl });

        const formBody = new URLSearchParams({
          email: userEmail,
          compid: String(accountId).toLowerCase(),
          Submit: "Continue",
          private: "T",
          agreement: "true",
        }).toString();

        log.debug({ title: "📝 Form Body:", details: formBody });

        const pwResponse = https.post({
          url: pwResetUrl,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: formBody,
        });

        log.debug({ title: "📬 PW Reset Response:", details: pwResponse });

        log.debug({ title: "🟨 PW Response Body:", details: pwResponse?.body });

        if (pwResponse.code === 200) {
          returnData.message = "Email found and password reset email sent";
          returnData.type = "EMAIL_FOUND_PASSWORD_RESET";
          returnData.status = 200;
          returnData.body = pwResponse?.body;

          log.audit({ title: "✅ Email found & reset:", details: returnData });
        } else {
          returnData.message = "Email found but password reset email failed";
          returnData.type = "EMAIL_FOUND_PASSWORD_RESET_FAILED";
          returnData.status = 400;
          returnData.body = pwResponse?.body;

          log.emergency({
            title: "❗ Email found but reset failed:",
            details: { returnData, userEmail },
          });
        }
      }
    }

    return JSON.stringify(returnData);
  };

  /**
   * Defines the function that is executed when a DELETE request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters are passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const doDelete = (requestParams) => {};

  function interpretLiquid(liquidString, data) {
    const liquidRegex = /{{\s*(.*?)\s*}}/g;
    const matches = liquidString.match(liquidRegex);

    if (!matches) {
      return liquidString; // If there are no liquid tags, return the original string
    }

    let interpretedString = liquidString;

    _.forEach(matches, (match) => {
      const liquidTag = match.substring(2, match.length - 2).trim(); // Remove the liquid tag delimiters
      const liquidValue = _.get(data, liquidTag, ""); // Get the value from the data using lodash

      interpretedString = interpretedString.replace(match, liquidValue);
    });

    return interpretedString;
  }

  const sendRegistrationEmail = (user, password, customerId, redirectUri) => {
    try {
      const CS_SETTINGS = settings.useSettings();
      const companyPreferences = config.load({
        type: config.Type.COMPANY_PREFERENCES,
      });
      const dateFormat = companyPreferences.getValue("dateformat");

      const emailTemplateId = CS_SETTINGS.custrecord_sign_up_email_template;
      const emailTemplate = record.load({
        type: record.Type.EMAIL_TEMPLATE,
        id: emailTemplateId,
      });

      const emailRecipient = user.email;
      const notificationAuthor =
        CS_SETTINGS.custrecord_web_notification_author || -5;
      const emailSubject = emailTemplate.getValue("subject");
      const emailBody = emailTemplate.getValue("content");

      const emailBodyInterpreted = interpretLiquid(emailBody, {
        userEmail: user.email,
        newPassword: password,
        emailRedirect: redirectUri,
        dateFormat,
      });

      email.send({
        author: notificationAuthor,
        replyTo: CS_SETTINGS.custrecord_admin_portal_access_email,
        recipients: emailRecipient,
        subject: `New Customer Portal Access for ${user.entitytitle}`,
        body: emailBodyInterpreted,
        relatedRecords: {
          entityId: customerId,
        },
      });

      log.debug({
        title: "📧 Email sent to:",
        details: emailRecipient,
      });
    } catch (err) {
      log.error({
        title: "❌ Email send failed:",
        details: err,
      });
    }
  };

  // Create a mock URLSearchParams object
  const URLSearchParams = function (params) {
    // Parse the query string parameters
    let parsedParams = {};

    if (_.isArray(params)) {
      // If params is an array, parse each parameter
      _.forEach(params, function (param) {
        let parts = _.split(param, "=");
        let name = parts[0];
        let value = parts[1];

        if (_.has(parsedParams, name)) {
          // If the parameter already exists, convert its value to an array
          if (_.isArray(parsedParams[name])) {
            parsedParams[name].push(value);
          } else {
            parsedParams[name] = [parsedParams[name], value];
          }
        } else {
          parsedParams[name] = value;
        }
      });
    } else if (_.isPlainObject(params)) {
      // If params is a JavaScript object, use it directly
      parsedParams = params;
    } else {
      // If params is a string, parse it as a query string
      parsedParams = _.chain(params)
        .split("&")
        .map(function (param) {
          return _.split(param, "=");
        })
        .fromPairs()
        .value();
    }

    // Get the value of a parameter
    this.get = function (name) {
      return parsedParams[name];
    };

    // Set the value of a parameter
    this.set = function (name, value) {
      parsedParams[name] = value;
    };

    // Convert the parameters to a query string
    this.toString = function () {
      return _.chain(parsedParams)
        .map(function (value, name) {
          if (_.isArray(value)) {
            // If the value is an array, repeat the parameter with each value
            return _.map(value, function (item) {
              return name + "=" + item;
            }).join("&");
          } else {
            return name + "=" + value;
          }
        })
        .join("&")
        .value();
    };
  };

  return { get, put, post, delete: doDelete };
});
