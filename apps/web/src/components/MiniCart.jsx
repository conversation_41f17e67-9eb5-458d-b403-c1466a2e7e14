import React, { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/router";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Collapse,
  Divider,
  IconButton,
  Typography,
  useTheme,
} from "@mui/material";
import { Clear, ErrorOutline } from "@mui/icons-material";
import { FlexBetween, FlexBox } from "components/flex-box";
import { Paragraph } from "components/Typography";
import CartBag from "components/icons/CartBag";
import { useAppContext } from "contexts/AppContext";
import { currency } from "lib";
import Lottie from "react-lottie";
import { AnimatePresence, motion } from "motion/react";
import { useCart, useCartQuantity } from "../store/zustandCartStore";
import { ProductCartItem } from "./product-cards/ProductCartItem";
import { useEvent } from "../store/eventStore";
import { useSettings } from "../store/zSettingsStore";
import * as animationData from "../../public/assets/lottie/cart-with-items.json";

const MiniCart = ({ toggleSidenav }) => {
  const { push } = useRouter();
  const { palette } = useTheme();
  const { cart, setCart, setItemQuantity } = useCart();
  const { cartQuantity } = useCartQuantity();
  const [cartSubTotalString, setCartSubTotalString] = useState(0);
  const { event } = useEvent();
  const { settings } = useSettings();
  const { state, dispatch } = useAppContext();
  const [allRequiredItemsMet, setAllRequiredItemsMet] = useState(false);

  const calcCartTotal = useCallback(() => {
    let sum = 0;

    if (event) {
      if (Array.isArray(cart) && cart.length !== 0) {
        cart.forEach((item) => {
          sum += item.price * item.quantity;
        });
        let convenience_percent =
          parseFloat(settings.custrecord_ng_cs_cc_conv_fee_rate) / 100;
        let tax_percent = parseFloat(event.details.rates.tax.percent) / 100.0;
        let tax = sum * tax_percent;
        let total = tax + sum;
        let convenience_fee = total * convenience_percent;
        let grand_total = total + convenience_fee;
        setCartSubTotalString(currency(sum));
      }
    }
  }, [cart, event, settings]);

  useEffect(() => {
    if (cart) {
      calcCartTotal();
    }
  }, [calcCartTotal, cart]);

  useEffect(() => {
    const checkRequiredItems = () => {
      for (const item of cart) {
        if (item.requiredProducts && item.requiredProducts.length > 0) {
          for (const requiredProduct of item.requiredProducts) {
            const requiredItemInCart = cart.find(
              (cartItem) =>
                cartItem.internalid === requiredProduct.id ||
                cartItem.parentProductId === requiredProduct.id,
            );

            if (
              !requiredItemInCart ||
              requiredItemInCart.quantity <
                requiredProduct.minimumQuantity * item.quantity
            ) {
              return false;
            }
          }
        }
      }
      return true;
    };

    setAllRequiredItemsMet(checkRequiredItems());
  }, [cart]);

  const defaultLottieOptions = {
    loop: true,
    autoplay: true,
    animationData,
    rendererSettings: {
      backgroundColor: "transparent",
      display: "flex",
    },
  };

  const handleNavigate = (path) => () => {
    toggleSidenav();
    push(path);
  };

  return (
    <Box width={cart.length === 0 ? 400 : "100%"} maxWidth={400}>
      <Box
        overflow="auto"
        height={`calc(100vh - ${cart.length !== 0 ? "80px - 3.25rem" : "0px"})`}
      >
        <FlexBetween mx={3} height={74}>
          <FlexBox gap={1} alignItems="center" color="secondary.main">
            <CartBag color="inherit" />

            <Paragraph lineHeight={0} fontWeight={600}>
              {cart.length === 0
                ? "Nothing here yet..."
                : `${cart.length} item${cart.length > 1 ? "s" : ""}`}
            </Paragraph>
          </FlexBox>

          <IconButton onClick={toggleSidenav}>
            <Clear />
          </IconButton>
        </FlexBetween>

        <Divider />

        {cart.length <= 0 && (
          <FlexBox
            alignItems="center"
            flexDirection="column"
            justifyContent="center"
            height="calc(100% - 74px)"
          >
            <Lottie
              height="18rem"
              width="18rem"
              options={defaultLottieOptions}
            />
            <Box
              component="p"
              mt={2}
              color="grey.600"
              textAlign="center"
              maxWidth="200px"
            >
              Your shopping bag is empty. Start adding items to it!
            </Box>
          </FlexBox>
        )}

        {Array.isArray(cart) &&
          cart.map((product, i) => (
            <ProductCartItem
              item={product}
              key={product.id}
              index={i}
              image={product.image}
              name={product.name}
              quantity={product.quantity}
              itemId={product.id}
              internalId={product.internalid}
              showDate={product.showDate}
              price={product.price}
              color={product.variant.color}
              size={product.variant.sizes}
              orientation={product.variant.orientations}
              materials={product.variant.materials}
              link={product.link}
              isLabor={product.isLabor}
              isSquareFt={product.isSquareFt}
              squareFtWidth={product.squareFtWidth}
              squareFtLength={product.squareFtLength}
              isFreight={product.isFreight}
              isEstimated={product.isEstimated}
              saleUnit={product.saleUnit}
              amount={product.amount}
              isShowDuration={product.isShowDuration}
              recordedFreightWeight={product.recordedFreightWeight}
              supervisionItem={product?.supervisionItem}
              supervision={product.supervision}
              laborHours={product.laborHours}
              laborDate={product.laborDate}
              laborStart={product.laborStart}
              laborEnd={product.laborEnd}
              isMemoItem={product.isMemoItem}
              memoText={product.memoText}
              minimumQuantity={product.minQuantity}
              maxQuantity={product.maxQuantity}
              enforceMinQty={product.enforceMinQuantity}
              isUpload={product.isUpload}
              fileAttachments={product?.fileAttachments}
              requiredProducts={product.requiredProducts}
            />
          ))}
      </Box>

      {cart.length > 0 && (
        <Box p={2.5}>
          <Button
            fullWidth
            color="primary"
            variant="contained"
            sx={{
              mb: "0.75rem",
              height: "40px",
            }}
            onClick={handleNavigate("/checkout")}
            disabled={!allRequiredItemsMet}
          >
            Checkout Now ({cartSubTotalString})
          </Button>

          <AnimatePresence>
            {!allRequiredItemsMet && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Collapse in={!allRequiredItemsMet}>
                  <Alert
                    severity="warning"
                    icon={<ErrorOutline />}
                    sx={{
                      mb: 2,
                      "& .MuiAlert-icon": {
                        fontSize: "1.5rem",
                      },
                    }}
                  >
                    <Typography variant="body2">
                      Please add all required items before checking out
                    </Typography>
                  </Alert>
                </Collapse>
              </motion.div>
            )}
          </AnimatePresence>

          <Button
            fullWidth
            color="primary"
            variant="outlined"
            sx={{
              height: 40,
            }}
            onClick={handleNavigate("/cart")}
          >
            View Cart
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default MiniCart;
