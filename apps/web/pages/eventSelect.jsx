import React, { useEffect, useMemo, useState } from "react";
import { getServerSession } from "next-auth/next";
import {
  Box,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
  Paper,
  Card,
  CardContent,
  useTheme,
  alpha,
} from "@mui/material";
import { motion } from "motion/react";
import useSWR from "swr";
import Head from "next/head";
import Cookies from "js-cookie";
import { useRouter } from "next/router";
import { Montserrat } from "next/font/google";
import { LoadingButton } from "@mui/lab";
import { useSession } from "next-auth/react";
import ReactHtmlParser, { convertNodeToElement } from "react-html-parser";
import { useCart } from "../src/store/zustandCartStore";
import { usePrevious } from "../src/utils/customHooks";
import { useSettings, useSettingsUpdate } from "../src/store/zSettingsStore";
import { useEventData, useEventSelection } from "../src/store/eventStore";
import useAnalytics from "../src/hooks/useAnalytics";
import { AuthGuard } from "../src/guards/AuthGuard";
import ShopLayout1 from "../src/components/layouts/ShopLayout1";
import { H1, H5 } from "../src/components/Typography";
import { authOptions } from "@event-services/auth";
import moment from "moment";
import ServerCookies from "cookies";
import { loadSettings } from "../lib/settings";
import { useSnackbar } from 'notistack'
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const montserrat = Montserrat({
  weight: ["100", "200", "300", "400", "500", "600", "800", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

// Custom animated MenuItem
const MotionMenuItem = motion(MenuItem);

// Motion components for enhanced UI
const MotionBox = motion(Box);
const MotionCard = motion(Card);
const MotionTypography = motion(Typography);
const MotionFormControl = motion(FormControl);
const MotionContainer = motion(Container);
const MotionPaper = motion(Paper);

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

// Select animation variants
const selectOptionVariants = {
  open: {
    opacity: 1,
    y: 0,
    transition: { type: "spring", stiffness: 300, damping: 24 }
  },
  closed: { opacity: 0, y: 20, transition: { duration: 0.2 } }
};

// Success animation for when both selections are made
const successVariants = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: { 
    scale: 1, 
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 200,
      damping: 20
    }
  }
};

export const EventSpaceSelect = ({ settings }) => {
  const [selectedEventForm, setSelectedEventForm] = useState({
    event: "",
    booth: "",
  });
  const [selectedEventFormText, setSelectedEventFormText] = useState({
    event: "",
    booth: "",
  });
  const [loadingEvent, setLoadingEvent] = useState(false);
  const { cart, resetCart } = useCart();
  const prevEvent = usePrevious(selectedEventForm.event);
  const { data: session, status } = useSession();
  const loading = status === "loading";
  const prevSession = usePrevious(session);
  const router = useRouter();
  const { enqueueSnackbar, closeSnackbar } = useSnackbar()
  const protectedFetcher = (...args) =>
    fetch(...args).then((res) => res.json());
  const { data, error } = useSWR(
    session ? "/api/customer/shows" : null,
    protectedFetcher,
  );
  const theme  = useTheme();


  useEffect(() => {
    let params = router.query

    if (params?.scanError) {
      enqueueSnackbar('Error Occurred While Navigating To Scan. Ensure configuration is set up correctly and try again', {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
      })
    }
  }, [])

  useEffect(() => {
    setSettings(settings);
  }, []);

  const { setSettings } = useSettingsUpdate();
  const { settings: csSettings } = useSettings();

  const [loadingText, setLoadingText] = useState("Loading Your Events...");
  const { event, setBooth } = useEventSelection();

  const { data: currentEvent } = useEventData(
    selectedEventForm?.event || event?.details?.id,
  );

  const [renderSingleShows, setSingleShowsRender] = useState([]);
  const { setSummary, summary } = useAnalytics();

  // Improved preloading functionality that avoids race conditions
  const preloadEventPage = async () => {
    if (!selectedEventForm.event || !canNavigate) return;
    
    try {
      // Only prefetch the Next.js route to avoid SWR conflicts
      // Let the page handle its own data loading to prevent race conditions
      await router.prefetch(`/event/${selectedEventForm.event}`);
      
      console.log('✅ Successfully preloaded event page route');
    } catch (error) {
      // Silently handle preload errors - they shouldn't block the user
      console.warn('⚠️ Route preload failed (non-blocking):', error.message);
    }
  };

  // eslint-disable-next-line consistent-return
  function transform(node, index) {
    // console.log('Working Nodes: ', node)
    if (node.type === "tag" && node.name === "b") {
      node.name = "strong";
      return convertNodeToElement(node, index, transform);
    }
    // if (node.type === 'tag' && node.name === 'span') {
    // 	return null
    // }

    // if (node.type === 'tag' && node.name === 'ul') {
    // 	node.name = 'ol';
    // 	return convertNodeToElement(node, index, transform);
    // }

    if (node.type === "tag" && node.name === "table") {
      node.name = "div";
      // console.log('table node: ', node)
      node.attribs = {
        ...node.attribs,
        width: null,
        class: "container",
        "data-test": "container",
        style: `padding: 0;`,
      };
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tr") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "td") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tbody") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }
  }

  const renderBlurb = (blurb) => {
    return ReactHtmlParser(blurb, { decodeEntities: true, transform });
  };

  const grabDefinedEvent = async (event, space) => {
    // Do netsuite logic to define event
    // event.preventDefault()
    setLoadingEvent(true);
    let cookieEventId = Cookies.get("eventId");
    let cookieEventName = Cookies.get("eventName");
    let cookieBoothId = Cookies.get("booth");
    let cookieBoothName = Cookies.get("boothName");
    let cookieCollections = Cookies.get("collections");
    let cookieCart = Cookies.get("cart");

    // Check if event is different from currently selected event.Then reset the cart to comply with orders per event.
    if (cart && cart.length !== 0) {
      if (
        selectedEventForm.event !== cookieEventId &&
        selectedEventForm.booth !== cookieBoothId
      ) {
        let switchEvent = confirm(
          "There are still items in your cart. Please finish checking out with current event & space. If you wish to switch another event your cart will be reset. Are you sure you want to continue?",
        );
        if (!switchEvent) {
          setLoadingEvent(false);
          return;
        }
        resetCart();
      }
    }

    console.log("Submitting with form:", selectedEventForm);
    console.log("Submitting with form text:", selectedEventFormText);

    const companyDateFormat =
      settings?.company?.preferences?.shortDateFormat || "MM/DD/YYYY";

    const eventExpiration = moment(
      event?.details?.websiteEndDate,
      companyDateFormat,
    )
      .endOf("day")
      .toDate();

    if (cookieEventId || cookieBoothId) {
      Cookies.remove("eventId", { path: "/" });
      Cookies.remove("eventName", { path: "/" });
      Cookies.remove("boothName", { path: "/" });
      Cookies.remove("booth", { path: "/" });
      Cookies.remove("collections", { path: "/" });

      Cookies.set("eventId", selectedEventForm.event, {
        path: "/",
        expires: eventExpiration,
      });
      setBooth({
        name: selectedEventFormText.booth,
        id: selectedEventForm.booth,
      });
      Cookies.set("eventName", selectedEventFormText.event, {
        path: "/",
        expires: eventExpiration,
      });
      setSummary({
        checkoutStarted: new Date(),
        booth: selectedEventForm.booth,
        event: selectedEventForm.event,
      });
    } else {
      Cookies.set("eventId", selectedEventForm.event, {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("eventName", selectedEventFormText.event, {
        path: "/",
        expires: eventExpiration,
      });
      setBooth({
        name: selectedEventFormText.booth,
        id: selectedEventForm.booth,
      });
      setSummary({
        checkoutStarted: new Date(),
        booth: selectedEventForm.booth,
        event: selectedEventForm.event,
      });
    }
    await router.push(`/event/${selectedEventForm.event}`);
    // setLoadingEvent(false)
  };

  const canNavigate = useMemo(() => {
    if (!loadingEvent) {
      if (selectedEventForm.event === null || selectedEventForm.event === "") {
        return false;
      }
      return !(
        selectedEventForm.booth === null || selectedEventForm.booth === ""
      );
    }
    return !loadingEvent;
  }, [selectedEventForm]);

  const handleEventFormChange = (e, boothText, eventText) => {
    console.log("OnChange: ", e);
    if (e.target.name === "event") {
      setSelectedEventForm({
        ...selectedEventForm,
        [e.target.name]: e.target.value,
      });
    } else {
      setSelectedEventForm({
        ...selectedEventForm,
        [e.target.name]: e.target.value,
      });
    }
  };

  useEffect(() => {
    console.log("Data: ", data);
    console.log("Error: ", error);
  }, [data, error]);

  // Get text from feilds to set cookies
  useEffect(() => {
    if (prevEvent !== selectedEventForm.event) {
      setSelectedEventForm({
        ...selectedEventForm,
        booth: "",
      });
    }
    if (data) {
      // Capture text of event & booth that is chosen from the select field.
      let boothName = selectedEventForm.booth
        ? data.shows.find((show) => show.booth.id === selectedEventForm.booth)
            .booth.boothNumber
        : "";
      let eventName = selectedEventForm.event
        ? data.shows.find((show) => show.event.id === selectedEventForm.event)
            .event.name
        : "";
      console.log("Event Name", eventName);
      console.log("Booth Name", boothName);

      setSelectedEventFormText({
        ...selectedEventFormText,
        booth: boothName,
        event: eventName,
      });
    }

    console.log("Selected Event", selectedEventForm);
  }, [selectedEventForm]);

  useEffect(() => {
    if (data) {
      render_single_shows();
    }
  }, [data]);

  function settingsAvailable() {
    return Object.keys(settings).length !== 0;
  }
  // if (!data) {
  // 	return (
  // 		<div style={{ height: props.dimensions.width < 565 ? 'unset' : props.dimensions.height }}>
  // 			<MDBContainer className='margin-nav margin-footer'>
  // 				<MDBRow center style={{ paddingTop: '3rem' }}><CircularProgress color="secondary" size={41} /></MDBRow>
  // 				<MDBRow center className='margin-nav' style={{ paddingTop: '3rem' }}>
  // 					<MDBTypography tag='h2' variant="h2-responsive">Loading Your Events...</MDBTypography>
  // 				</MDBRow>
  // 			</MDBContainer>
  // 		</div>
  // 	)
  // }

  const render_single_shows = () => {
    let single_shows = [];
    data.shows.forEach((show, i) => {
      // console.log('Show: ', event)
      let found = single_shows.find((ev) => ev.event.id === show.event.id);

      if (!found) {
        single_shows.push(show);
      }
    });

    setSingleShowsRender(single_shows);
  };

  if (typeof window === "undefined") {
    return null;
  }

  // When rendering client side don't display anything until loading is complete
  if (typeof window !== "undefined" && loading)
    return (
      <MotionContainer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="margin-nav margin-footer"
        style={{ marginTop: "5.5rem" }}
      >
        <MotionBox 
          display="flex" 
          justifyContent="center" 
          alignItems="center"
          flexDirection="column"
          gap={2}
          height="60vh"
        >
          <MotionBox
            animate={{ 
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <CircularProgress color='secondary' size={60} thickness={4} />
          </MotionBox>
          <MotionTypography 
            variant="h4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            sx={{ fontFamily: montserrat.style.fontFamily }}
          >
            Loading...
          </MotionTypography>
          <MotionTypography 
            variant="body1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            color="text.secondary"
            sx={{ fontFamily: montserrat.style.fontFamily }}
          >
            Preparing your event selection
          </MotionTypography>
        </MotionBox>
      </MotionContainer>
    );

  return (
    <ShopLayout1
      topbarBgColor={settings?.custrecord_ng_cs_navbar_bckgrnd_color}
    >
      <Head>
        <title>Event Selection</title>
      </Head>
      <Box
        sx={{
          position: "relative",
          minHeight: "100vh",
          background: (theme) => `linear-gradient(145deg, ${alpha(theme?.palette?.background?.default, 0.9)}, ${alpha(theme?.palette?.background?.paper, 0.9)})`,
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: (theme) => `radial-gradient(${alpha(theme?.palette?.primary?.main, 0.1)} 1px, transparent 1px)`,
            backgroundSize: "20px 20px",
            pointerEvents: "none",
            zIndex: 0,
          }
        }}
      >
        <MotionContainer 
          maxWidth="md" 
          sx={{ 
            py: 6,
            position: "relative",
            zIndex: 1
          }}
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <MotionBox 
            variants={itemVariants}
            sx={{ mb: 5, textAlign: "center" }}
          >
            <MotionTypography
              component="h1"
              variant="h2"
              fontWeight="bold"
              gutterBottom
              sx={{ 
                fontFamily: montserrat.style.fontFamily,
                position: "relative",
                display: "inline-block",
                "&::after": {
                  content: '""',
                  position: "absolute",
                  width: "40%",
                  height: "4px",
                  bottom: "-8px",
                  left: "30%",
                  backgroundColor: (theme) => theme.palette.primary.main,
                  borderRadius: "2px"
                }
              }}
            >
              Event Selection
            </MotionTypography>
            
            {Object.keys(settings).length !== 0 && (
              <MotionBox 
                variants={itemVariants}
                sx={{ 
                  mt: 3, 
                  mx: "auto", 
                  maxWidth: "800px",
                  color: (theme) => theme.palette.text.secondary
                }}
              >
                {renderBlurb(settings.custrecord_ng_cs_event_selection_info)}
              </MotionBox>
            )}
          </MotionBox>

          <MotionCard
            variants={itemVariants}
            sx={{ 
              borderRadius: 2,
              boxShadow: (theme) => `0 8px 24px ${alpha(theme.palette.primary.main, 0.12)}`,
              overflow: "hidden",
              mb: 4,
              transition: 'transform 0.3s ease, box-shadow 0.3s ease',
              '&:hover': {
                boxShadow: (theme) => `0 12px 32px ${alpha(theme.palette.primary.main, 0.18)}`
              }
            }}
           
            transition={{ type: "spring", stiffness: 100, damping: 10 }}
          >
            <CardContent sx={{ p: 4 }}>
              {!data && !error ? (
                <MotionBox 
                  display="flex" 
                  flexDirection="column"
                  alignItems="center" 
                  justifyContent="center"
                  sx={{ py: 6 }}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <MotionBox
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: [0, 180, 360],
                    }}
                    transition={{ 
                      duration: 2.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <CircularProgress size={48} thickness={3.5} />
                  </MotionBox>
                  <MotionTypography 
                    variant="h5" 
                    sx={{ 
                      mt: 3,
                      fontFamily: montserrat.style.fontFamily
                    }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {loadingText}
                  </MotionTypography>
                  <MotionTypography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{ 
                      mt: 1,
                      fontFamily: montserrat.style.fontFamily
                    }}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    This may take a moment...
                  </MotionTypography>
                </MotionBox>
              ) : (
                <MotionBox
                  component="form"
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <MotionBox variants={itemVariants} sx={{ mb: 4 }}>
                    <MotionBox 
                      sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        mb: 2 
                      }}
                    >
                      <MotionTypography 
                        variant="h5" 
                        fontWeight="medium" 
                        sx={{ 
                          color: (theme) => theme.palette.text.primary,
                          fontFamily: montserrat.style.fontFamily
                        }}
                      >
                        Select an event
                      </MotionTypography>
                      {selectedEventForm.event && (
                        <MotionBox 
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ type: "spring", stiffness: 500, damping: 15 }}
                          sx={{ ml: 2, color: 'success.main', display: 'flex', alignItems: 'center' }}
                        >
                          <CheckCircleIcon fontSize="small" />
                        </MotionBox>
                      )}
                    </MotionBox>
                    <MotionPaper
                      elevation={0}
                      sx={{ 
                        p: 0.5, 
                        borderRadius: 2,
                        background: (theme) => alpha(theme.palette.primary.main, 0.04),
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          background: (theme) => alpha(theme.palette.primary.main, 0.08),
                        }
                      }}
                      whileHover={{ scale: 1.01 }}
                    >
                      <MotionFormControl 
                        fullWidth 
                        variant="outlined"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <InputLabel id="event-select-label">Event</InputLabel>
                        <Select
                          sx={{ 
                            fontFamily: montserrat.style.fontFamily,
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: (theme) => alpha(theme.palette.primary.main, 0.2),
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: (theme) => alpha(theme.palette.primary.main, 0.5),
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: (theme) => theme.palette.primary.main,
                            }
                          }}
                          labelId="event-select-label"
                          id="event-select"
                          value={selectedEventForm.event}
                          onChange={(e) => handleEventFormChange(e)}
                          label="Event"
                          disabled={!data}
                          name="event"
                        >
                          <MenuItem value="">
                            <em>Select an event</em>
                          </MenuItem>
                          {!data
                            ? null
                            : data.shows === "No shows available"
                              ? null
                              : renderSingleShows.map((show, i) => {
                                  let showName =
                                    show.event.displayName || show.event.name;
                                  let showId = show.event.id;

                                  return (
                                    <MotionMenuItem
                                      id={show.id}
                                      key={`event-${i}`}
                                      value={showId}
                                      initial={{ opacity: 0, y: 10 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{ 
                                        delay: i * 0.05,
                                        duration: 0.2
                                      }}
                                      whileHover={{ 
                                        backgroundColor: alpha(theme.palette.primary.main, 0.08)
                                      }}
                                      sx={{
                                        transition: 'background-color 0.2s ease'
                                      }}
                                    >
                                      {showName}
                                    </MotionMenuItem>
                                  );
                                })}
                        </Select>
                      </MotionFormControl>
                    </MotionPaper>
                  </MotionBox>

                  <MotionBox 
                    variants={itemVariants} 
                    sx={{ 
                      mb: 4,
                      opacity: !selectedEventForm.event ? 0.6 : 1,
                      transition: "opacity 0.3s ease"
                    }}
                  >
                    <MotionBox 
                      sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        mb: 2 
                      }}
                    >
                      <MotionTypography 
                        variant="h5" 
                        fontWeight="medium" 
                        sx={{ 
                          color: (theme) => theme.palette.text.primary,
                          fontFamily: montserrat.style.fontFamily
                        }}
                      >
                        Select a space
                      </MotionTypography>
                      {selectedEventForm.booth && (
                        <MotionBox 
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ type: "spring", stiffness: 500, damping: 15 }}
                          sx={{ ml: 2, color: 'success.main', display: 'flex', alignItems: 'center' }}
                        >
                          <CheckCircleIcon fontSize="small" />
                        </MotionBox>
                      )}
                    </MotionBox>
                    <MotionPaper
                      elevation={0}
                      sx={{ 
                        p: 0.5, 
                        borderRadius: 2,
                        background: (theme) => alpha(theme.palette.primary.main, 0.04),
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          background: (theme) => alpha(theme.palette.primary.main, 0.08),
                        }
                      }}
                      whileHover={{ scale: 1.01 }}
                      animate={{ 
                        opacity: !selectedEventForm.event ? 0.6 : 1,
                      }}
                    >
                      <MotionFormControl 
                        fullWidth 
                        variant="outlined"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                      >
                        <InputLabel id="space-select-label">Space</InputLabel>
                        <Select
                          sx={{ 
                            fontFamily: montserrat.style.fontFamily,
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: (theme) => alpha(theme.palette.primary.main, 0.2),
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: (theme) => alpha(theme.palette.primary.main, 0.5),
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: (theme) => theme.palette.primary.main,
                            }
                          }}
                          labelId="space-select-label"
                          id="space-select"
                          value={selectedEventForm.booth}
                          onChange={(e) => handleEventFormChange(e)}
                          label="Space"
                          disabled={!data || !selectedEventForm.event}
                          name="booth"
                        >
                          <MenuItem value="">
                            <em>Select a space</em>
                          </MenuItem>
                          {!data
                            ? null
                            : data.shows === "No shows available"
                              ? null
                              : data.shows.map((show, i) => {
                                  if (show.event.id === selectedEventForm.event) {
                                    let spaceNumber = show.booth.boothNumber;
                                    let boothId = show.booth.id;
                                    return (
                                      <MotionMenuItem
                                        id={show.id}
                                        key={i}
                                        value={boothId}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ 
                                          delay: i * 0.05,
                                          duration: 0.2
                                        }}
                                        whileHover={{ 
                                          backgroundColor: alpha(theme.palette.primary.main, 0.08)
                                        }}
                                        sx={{
                                          transition: 'background-color 0.2s ease'
                                        }}
                                      >
                                        {spaceNumber}
                                      </MotionMenuItem>
                                    );
                                  }
                                  return null;
                                })}
                        </Select>
                      </MotionFormControl>
                    </MotionPaper>
                  </MotionBox>

                  <MotionBox 
                    variants={itemVariants}
                    sx={{ mt: 4 }}
                    whileHover={canNavigate ? { scale: 1.02 } : {}}
                    whileTap={canNavigate ? { scale: 0.98 } : {}}
                  >
                    <MotionBox
                      initial="hidden"
                      animate={canNavigate ? "visible" : "hidden"}
                      variants={successVariants}
                    >
                      <LoadingButton
                        className={`${montserrat.className}`}
                        sx={{
                          py: 1.5,
                          borderRadius: "8px",
                          fontWeight: "600",
                          fontSize: "1rem",
                          textTransform: "none",
                          boxShadow: (theme) => `0 4px 14px ${alpha(theme.palette.primary.main, 0.4)}`,
                          "&:hover": {
                            boxShadow: (theme) => `0 6px 20px ${alpha(theme.palette.primary.main, 0.6)}`,
                          },
                          transition: "all 0.3s ease"
                        }}
                        fullWidth
                        size="large"
                        disabled={!canNavigate}
                        variant="contained"
                        color="primary"
                        onClick={(e) => grabDefinedEvent(e)}
                        onMouseEnter={preloadEventPage} // Preload on hover for better UX
                        loading={loadingEvent}
                        loadingPosition="end"
                        endIcon={
                          loadingEvent ? (
                            <CircularProgress size={20} disableShrink />
                          ) : null
                        }
                      >
                        Continue to Event
                      </LoadingButton>
                    </MotionBox>
                  </MotionBox>
                </MotionBox>
              )}
            </CardContent>
          </MotionCard>
        </MotionContainer>
      </Box>
    </ShopLayout1>
  );
};

EventSpaceSelect.getLayout = (page) => <AuthGuard>{page}</AuthGuard>;

export const getServerSideProps = async ({ req, res }) => {
  // Create a cookies instance
  const cookies = new ServerCookies(req, res);

  // Load csSettings on the server side
  const settings = await loadSettings();
  res.setHeader(
    "Cache-Control",
    "public, s-maxage=120, stale-while-revalidate=59",
  );

  const session = (await getServerSession(req, res, authOptions)) || null;
  console.debug("Session: ", session);

  if (!session) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  if (
    settings?.custrecord_ng_cs_accent_color ||
    settings?.custrecord_ng_cses_web_primary_color
  ) {
    cookies.set(
      "primaryColor",
      settings?.custrecord_ng_cses_web_primary_color,
      {
        httpOnly: false, // true by default
        overwrite: true,
        priority: "high",
      },
    );

    cookies.set("secondaryColor", settings?.custrecord_ng_cs_accent_color, {
      httpOnly: false, // true by default
      overwrite: true,
      priority: "high",
    });
  }

  return {
    props: {
      settings,
    },
  };
};

export default EventSpaceSelect;
