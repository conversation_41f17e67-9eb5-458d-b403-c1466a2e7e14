import Link from "next/link";
import React, { useEffect, useState } from "react";
import { Delete, Edit, Place } from "@mui/icons-material";
import {
  Backdrop,
  Box,
  Button,
  Chip,
  CircularProgress,
  Container,
  Icon<PERSON>utton,
  Stack,
  Typography,
} from "@mui/material";
import Head from "next/head";
import { useSession } from "next-auth/react";
import { useSnackbar } from "notistack";
import TableRow from "components/TableRow";
import UserDashboardHeader from "components/header/UserDashboardHeader";
import CustomerDashboardLayout from "components/layouts/customer-dashboard";
import CustomerDashboardNavigation from "components/layouts/customer-dashboard/Navigations";
// import api from "utils/__api__/address";
import { useTheme } from "@mui/material/styles";
import { loadUserAddresses } from "../../lib/user/address/address-list";
import { AuthGuard } from "../../src/guards/AuthGuard";
import { useSettings } from "../../src/store/zSettingsStore";
import LoadingCircleSpinner from "components/spinners/circle-spinner";

// =======================================================

// =======================================================

const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID;
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY;
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET;
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN;
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET;

const AddressList = ({ addressList }) => {
  const [allAddress, setAllAddress] = useState(addressList);
  const { enqueueSnackbar } = useSnackbar();
  const { settings } = useSettings();
  const { data: session } = useSession();
  const [isDeleting, setIsDeleting] = useState(new Set());
  const [backdrop, setBackdropVisible] = useState({
    visible: false,
    message: "",
    spinner: true,
  });
  const theme = useTheme();
  const { palette } = theme;

  const resetBackdrop = () => {
    setBackdropVisible({ visible: false, message: "", spinner: true });
  };

  const user = session?.user;

  useEffect(() => {
    console.log("isDeleting Addresses:", isDeleting);
  }, [isDeleting]);

  // SECTION TITLE HEADER BUTTON
  const HEADER_BUTTON = (
    <Button
      color="primary"
      LinkComponent={Link}
      href="/address/add"
      sx={{
        bgcolor: "primary.light",
        px: 4,
      }}
    >
      Add New Address
    </Button>
  );

  // HANDLE ADDRESS DELETE
  const handleAddressDelete = async (id) => {
    let updatedAddresses = [...allAddress];
    let updatedDelete = new Set(isDeleting).add(id);
    setIsDeleting(updatedDelete);
    setBackdropVisible({
      visible: true,
      message: "Deleting Address...",
      spinner: true,
    });
    const headers = new Headers();
    headers.append("Content-Type", "application/json");

    const requestConfig = {
      method: "DELETE",
      headers,
    };

    const response = await fetch(
      `/api/customer/delete/address/${id}?uid=${user?.id}&type=address`,
      requestConfig,
    );

    if (!response.ok) {
      console.error("Error deleting address: ", response);
      enqueueSnackbar("Error deleting address", { variant: "error" });
      setAllAddress(updatedAddresses);

      updatedDelete = new Set(isDeleting);
      updatedDelete.delete(id);
      setIsDeleting(updatedDelete);
      setBackdropVisible({
        visible: true,
        message: "Address Failed To Delete",
        spinner: false,
      });
      setTimeout(() => {
        resetBackdrop();
      }, 500);
      return;
    }

    enqueueSnackbar("Address deleted successfully", { variant: "success" });
    setBackdropVisible({
      visible: true,
      message: "Address deleted successfully",
      spinner: false,
    });
    setTimeout(() => {
      setAllAddress(updatedAddresses.filter((item) => item.id !== id));
      updatedDelete = new Set(isDeleting);
      updatedDelete.delete(id);
      setIsDeleting(updatedDelete);
      resetBackdrop();
    }, 1000);
  };

  const renderBorderColor = (address) => {
    if (address.defaultShipping && !address.defaultBilling) {
      return `${palette.primary.main} !important`;
    }
    if (address.defaultBilling && !address.defaultShipping) {
      return `${palette.secondary.main} !important`;
    }
    if (address.defaultBilling && address.defaultShipping) {
      return `${palette.info.main} !important`;
    }
    return "transparent";
  };

  return (
    <CustomerDashboardLayout
      topbarBgColor={settings?.custrecord_ng_cs_navbar_bckgrnd_color}
    >
      <Head>
        <title>
          {settings?.company?.name || "ConventionSuite Event Services"}
          {" | "}
          My Addresses
        </title>
      </Head>

      {/* TITLE HEADER AREA */}
      <UserDashboardHeader
        icon={Place}
        title="My Addresses"
        button={HEADER_BUTTON}
        navigation={<CustomerDashboardNavigation />}
      />
      <Backdrop
        style={{ zIndex: 1000 }}
        open={backdrop.visible /* true backdrop.visible */}
      >
        <Container display="flex">
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
          >
            <Container display="flex">
              <Box
                display="flex"
                flexDirection="row"
                justifyContent="center"
                alignItems="center"
              >
                <span>
                  {backdrop.spinner ? (
                    <LoadingCircleSpinner />
                  ) : null}
                </span>
              </Box>
              <Typography
                sx={{
                  textAlign: "center",
                  marginTop: "4vh",
                  color: "white",
                }}
                variant="body2"
              >
                {backdrop.message}
              </Typography>
            </Container>
          </Box>
        </Container>
      </Backdrop>
      {/* ALL ADDRESS LIST AREA */}
      {allAddress.map((address) => (
        <Box
          key={address.id}
          sx={{
            position: "relative",
          }}
        >
          <TableRow
            sx={{
              my: "1rem",
              padding: "18px 18px",
              borderColor: renderBorderColor(address),
              border:
                (address.defaultShipping || address.defaultBilling) &&
                "1px solid",
            }}
            key={address.id}
          >
            <Typography whiteSpace="pre" m={0.75} textAlign="left">
              {address.label}
            </Typography>

            <Typography flex="1 1 260px !important" m={0.75} textAlign="left">
              {`${address.address1}, ${address.city}, ${address.state} ${address.zip}`}
            </Typography>

            <Typography
              sx={{ justifyContent: "flex-start" }}
              whiteSpace="pre"
              m={0.75}
              textAlign="left"
            >
              {address.country}
            </Typography>

            <Typography whiteSpace="pre" textAlign="center" color="grey.600">
              <IconButton LinkComponent={Link} href={`/address/${address.id}`}>
                <Edit fontSize="small" color="inherit" />
              </IconButton>

              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddressDelete(address.id);
                }}
              >
                {isDeleting && isDeleting.has(address.id) ? (
                  <CircularProgress size={10} disableShrink />
                ) : (
                  <Delete fontSize="small" color="inherit" />
                )}
              </IconButton>
            </Typography>
          </TableRow>
          <Stack
            direction="row"
            spacing={1}
            sx={{
              position: "absolute",
              top: -9,
              right: 8,
            }}
          >
            {address.defaultShipping && (
              <Chip
                size="small"
                variant="filled"
                label="Default Shipping"
                color="primary"
              />
            )}
            {address.defaultBilling && (
              <Chip
                size="small"
                variant="filled"
                label="Default Billing"
                color="secondary"
              />
            )}
          </Stack>
        </Box>
      ))}

      {/* PAGINATION AREA */}
      {/* <FlexBox justifyContent="center" mt={5}> */}
      {/*  <Pagination count={5} onChange={(data) => console.log(data)} /> */}
      {/* </FlexBox> */}
    </CustomerDashboardLayout>
  );
};

AddressList.getLayout = (page) => <AuthGuard>{page}</AuthGuard>;

export const getServerSideProps = async ({ req, res }) => {
  console.debug("cookies", req.cookies);
  const contact = req.cookies?.contact;
  const customer = req.cookies?.customer;

  const addressList = await loadUserAddresses(customer, contact);
  return {
    props: {
      addressList,
    },
  };
};

AddressList.getLayout = (page) => <AuthGuard>{page}</AuthGuard>;

export default AddressList;
