import React, { useEffect, useMemo, useState } from "react";
import PropTypes from "prop-types";
import clsx from "clsx";

import {
  BusinessCenter as BusinessCenterIcon,
  Check as CheckIcon,
  Payment as PaymentIcon,
} from "@mui/icons-material";
import { amber, blue, teal } from "@mui/material/colors";
import {
  Alert,
  AlertTitle,
  Box,
  Button,
  Checkbox,
  Container,
  Grid,
  Skeleton,
  Step,
  StepConnector,
  StepLabel,
  Stepper,
  Typography,
} from "@mui/material";
import tinycolor from "tinycolor2";
import dynamic from "next/dynamic";
import { makeStyles, withStyles } from "@mui/styles";
import ProductOrderReviewItem from "../../containers/ProductOrderReviewItem";
import MuiNextLinkButton from "../../MuiNextLinkButton";
import { AddressStep, PaymentStep, ReviewAndPlaceStep } from "./Steps";
import { useSettings } from "../../../store/zSettingsStore";

const CheckoutAddressListCard = dynamic(
  () => import("../../containers/CheckoutAddressListCard"),
  {
    suspense: true,
  },
);

const CheckoutPaymentMethodCard = dynamic(
  () => import("../../containers/CheckoutPaymentMethodCard"),
  {
    suspense: true,
  }
);

const ColorlibConnector = withStyles({
  alternativeLabel: {
    top: 22,
  },
  active: {
    "& $line": {
      backgroundImage:
        "linear-gradient(to top,rgb(207 217 223) 0,rgb(226 235 240) 100%)",
    },
  },
  completed: {
    "& $line": {
      backgroundImage:
        "linear-gradient(to top,rgb(207 217 223) 0,rgb(226 235 240) 100%)",
    },
  },
  line: {
    height: 3,
    width: "100%",
    border: 0,
    backgroundColor: "#eaeaf0",
    borderRadius: 1,
  },
})(StepConnector);

const useColorlibStepIconStyles = makeStyles({
  root: {
    backgroundColor: "#4F4F4F",
    zIndex: 1,
    color: "#000",
    width: 40,
    height: 40,
    display: "flex",
    borderRadius: "50%",
    justifyContent: "center",
    alignItems: "center",
  },
  active: {
    backgroundImage:
      "linear-gradient(to top,rgb(207 217 223) 0,rgb(226 235 240) 100%)",
    boxShadow: "0 4px 10px 0 rgba(0,0,0,.25)",
  },
  completed: {
    backgroundImage:
      "linear-gradient(to top,rgb(207 217 223) 0,rgb(226 235 240) 100%)",
  },
});

const ColorlibStepIcon = (props) => {
  const classes = useColorlibStepIconStyles();
  const { active, completed } = props;

  const icons = {
    1: <BusinessCenterIcon fontSize="small" />,
    2: <PaymentIcon fontSize="small" />,
    3: <CheckIcon fontSize="small" />,
  };

  return (
    <div
      className={clsx(classes.root, {
        [classes.active]: active,
        [classes.completed]: completed,
      })}
    >
      {icons[String(props.icon)]}
    </div>
  );
};

ColorlibStepIcon.propTypes = {
  /**
   * Whether this step is active.
   */
  active: PropTypes.bool,
  /**
   * Mark the step as completed. Is passed to child components.
   */
  completed: PropTypes.bool,
  /**
   * The label displayed in the step icon.
   */
  icon: PropTypes.node,
};

const useStyles = makeStyles((theme) => ({
  root: {
    width: "100%",
  },
  button: {
    marginRight: theme.spacing(1),
  },
  instructions: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
}));

const useInputStyles = makeStyles((theme) => ({
  root: {
    paddingBottom: "1rem",
    "& label": {
      fontFamily: "Roboto,sans-serif",
      fontWeight: 300,
    },
    "& label.Mui-focused": {
      color: "rgb(0 123 255)",
      borderBottom: "1px",
      fontFamily: "Roboto,sans-serif",
      fontWeight: 300,
    },
    "& .MuiInput-underline:after": {
      borderBottomColor: "rgb(0 123 255)",
      borderBottom: "1px solid rgb(206 212 218)",
    },
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderBottom: "1px solid rgb(206 212 218)",
      },
      "&:hover fieldset": {
        borderColor: "rgb(0 123 255)",
      },
      "&.Mui-focused fieldset": {
        borderColor: "rgb(0 123 255)",
      },
    },
  },
}));

const BlueCheckbox = withStyles({
  root: {
    color: blue[400],
    "&$checked": {
      color: blue[600],
    },
  },
  checked: {},
})((props) => <Checkbox color="default" {...props} />);

const TealCheckbox = withStyles({
  root: {
    color: teal[400],
    "&$checked": {
      color: teal[600],
    },
  },
  checked: {},
})((props) => <Checkbox color="default" {...props} />);

const AmberCheckbox = withStyles({
  root: {
    color: amber[400],
    "&$checked": {
      color: amber[600],
    },
  },
  checked: {},
})((props) => <Checkbox color="default" {...props} />);

export default function CustomCheckoutStepper(props) {
  // RENDERED CHECKOUT PAGE \\
  const { settings } = useSettings();
  const euaEnabled = settings?.custrecord_enable_web_eua_on_checkout;
  const classes = useStyles();
  const inputClasses = useInputStyles();
  const steps = ["Billing Address", "Payment", "Review Order"];
  const [stateOptions, setStateOptions] = useState([]);
  const [stateInputOpen, setStateInputOpen] = useState(false);
  const [countryOptions, setCountryOptions] = useState([]);
  const [countryInputOpen, setCountryInputOpen] = useState(false);
  const loadingCountries = countryInputOpen && countryOptions.length === 0;
  const loadingStates = stateInputOpen && stateOptions.length === 0;
  const [submitting, setSubmitting] = useState(false);
  let newOrder = props.newOrder?.newOrderRec || {};
  let originOrder = props.newOrder?.originOrder || {};
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",

    // These options are needed to round to whole numbers if that's what you want.
    // minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    // maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
  });

 

  

  useEffect(() => {
    if (!stateInputOpen) {
      setStateOptions([]);
    }
  }, [stateInputOpen]);

  

  useEffect(() => {
    if (!countryInputOpen) {
      setCountryOptions([]);
    }
  }, [countryInputOpen]);

  // MAPS Autocomplete fieldA

  const handlePostToNetSuite = (
    type,
    _values,
    setStatus,
    setSubmitting,
    setErrors,
    resetForm
  ) => {
    switch (type) {
      case "address":
        props.handleNetsuitePost(
          "address",
          _values,
          setStatus,
          setSubmitting,
          setErrors,
          resetForm
        );
        break;
      case "card":
        props.handleNetsuitePost(
          "card",
          _values,
          setStatus,
          setSubmitting,
          setErrors,
          resetForm
        );
        break;
      case "order":
        props.handleNetsuitePost("order");
        break;
      default:
        console.error(
          "No TYPE was specified for POST; request will not fire. Please check component CustomCheckoutStepper."
        );
    }
  };

  // DEBUG ONLY
  // useEffect(() => {
  // 	console.log('Props:', props)
  // 	// console.log('Test Algo:', getCountryCodeOrName('USA'))
  // })

  const handleNext = () => {
    props.setActiveStep((prevActiveStep) => prevActiveStep + 1);
    if (props.activeStep === steps.length - 1) {
      if (euaEnabled && !props.euaAccepted) {
        alert(
          "Please accept the End User Agreement before placing your order."
        );
      } else {
        console.log("Placed order");
        setSubmitting(true);
        props.handleNetsuitePost("order");
        setTimeout(() => {
          setSubmitting(false);
        }, 2000);
      }
    }
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleBack = () => {
    props.setActiveStep((prevActiveStep) => prevActiveStep - 1);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handlePaymentBackStep = () => {
    props.setActiveStep((prevActiveStep) => prevActiveStep - 1);
    props.setOrderSubmissionError({
      type: "Internal Error",
      display: false,
      message: "",
    });
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleReset = () => {
    props.setActiveStep(0);
  };

  const checkoutDisabled = useMemo(() => {
    let disabled = true;
    if (props.currentCart.length !== 0) {
      if (euaEnabled) {
        console.log("EUAs enabled", props.euaAccepted);
        // Look for EUA acceptance from props
        if (props.activeStep === steps.length - 1) {
          disabled = !props.euaAccepted;
        } else {
          disabled = submitting;
        }
      } else {
        disabled = submitting;
      }
    }
    return disabled;
  }, [props.currentCart, props.euaAccepted, submitting, props.activeStep]);

  const renderStepperContent = () => {
    if (props.activeStep === steps.length) {
      let debug = false; // Set only on debugging to render reset
      if (debug) {
        return (
          <Container>
            <Typography className={classes.instructions}>
              All steps completed - order submitted.
            </Typography>
            <Button variant="contained" onClick={handleReset}>
              Reset
            </Button>
          </Container>
        );
      }
      // setCart([])
      return (
        <Container>
          <Box
            display="flex"
            flexDirection="row"
            justifyContent="center"
            alignItems="center"
          >
            <Box display="flex" flexDirection="column">
              {!newOrder?.fields && !props.orderSubmissionError.display ? (
                <Alert style={{ width: "100%" }} severity="info">
                  <AlertTitle>
                    <h5>Thank you for your order!</h5>
                  </AlertTitle>
                  <p
                    style={{
                      fontSize: "11pt",
                    }}
                  >
                    Processing please wait...
                  </p>
                </Alert>
              ) : null}
              {/* Handle Payment Error After Authorization */}
              {props.orderSubmissionError.display &&
              props.orderSubmissionError.type === "CREDIT_CARD_ERROR" ? (
                <Alert style={{ width: "100%" }} severity="error">
                  <AlertTitle>
                    <h5>Payment Failed</h5>
                  </AlertTitle>
                  <p
                    style={{
                      fontSize: "11pt",
                    }}
                  >
                    {props.orderSubmissionError?.message}
                    <br />
                    <br />- Please retry with a different card or contact your
                    event coordinator for further instructions.
                  </p>
                </Alert>
              ) : null}
              {props.orderSubmissionError.display &&
              props.orderSubmissionError.type === "NEW_ORDER_ERROR" ? (
                <Alert style={{ width: "100%" }} severity="error">
                  <AlertTitle>
                    <h5>We hit a snag!</h5>
                  </AlertTitle>
                  <p
                    style={{
                      fontSize: "11pt",
                    }}
                  >
                    There was an error during the submission of your order.
                    Please check with your administrator that your order went
                    through. If it didn&apos;t we have saved your cart so you
                    can retry and submit a new one. Otherwise you can remove all
                    the items from your cart to begin a new order.
                    <br /> - Thank you! We apologize for the inconvenience.
                  </p>
                </Alert>
              ) : null}
            </Box>
            <br />
          </Box>
          {Object.keys(newOrder).length !== 0 && (
            <>
              <Box
                display="flex"
                flexDirection="row"
                justifyContent="center"
                alignItems="center"
              >
                <Box display="flex" flexDirection="column">
                  <Alert style={{ width: "100%" }} severity="success">
                    <AlertTitle>
                      <h5>Thank you for your order!</h5>
                    </AlertTitle>
                    <p
                      style={{
                        fontSize: "11pt",
                      }}
                    >
                      Order has processed successfully for your selected booth!
                      - <span style={{ fontSize: "8pt" }}>See info below</span>
                    </p>
                    <h6>
                      Event:{" "}
                      <strong>
                        {newOrder.fields.custbody_jobshow_name
                          ? newOrder.fields.custbody_jobshow_name
                          : null}
                      </strong>
                    </h6>
                    <h6>
                      Order Number:{" "}
                      <strong>
                        #{originOrder?.tranid || newOrder.fields.tranid}
                      </strong>
                    </h6>
                    <br />
                    <p
                      style={{
                        fontSize: "11pt",
                      }}
                    >
                      - Have a wonderful{" "}
                      {new Date().getHours() > 17 ? "night" : "day"}!
                    </p>
                  </Alert>
                </Box>
                <br />
              </Box>
              <hr />
              <Box
                display="flex"
                flexDirection="row"
                justifyContent="center"
                alignItems="center"
              >
                <Container>
                  {/* <MDBTable  theadColor='primary'> */}
                  {/*	<MDBTableHead textWhite color='dark'> */}
                  {/*		<tr> */}
                  {/*			<th width={275}>Item</th> */}
                  {/*			<th width={1}>Quantity</th> */}
                  {/*			<th className='text-right' width={1}>Price</th> */}
                  {/*		</tr> */}
                  {/*	</MDBTableHead> */}
                  {/*	<MDBTableBody> */}
                  {/* eslint-disable-next-line array-callback-return,consistent-return */}
                  {Object.keys(newOrder.sublists.item).map((key, i) => {
                    const { item } = newOrder.sublists;
                    if (i !== 0) {
                      return (
                        <Box display="flex" flexDirection="row" key={i}>
                          <Box display="flex" flexDirection="column">
                            <ProductOrderReviewItem
                              disabled
                              upc={item[key].item}
                              priceLevel={item[key].price_display}
                              price={item[key].amount}
                              quantity={item[key].quantity}
                              name={item[key].item_display}
                              badgeColor={
                                Object.keys(settings).length !== 0
                                  ? settings.custrecord_ng_cs_accent_color
                                  : "#343434"
                              }
                              badgeTextColor={
                                Object.keys(settings).length !== 0
                                  ? tinycolor(
                                      settings.custrecord_ng_cs_accent_color
                                    ).isDark()
                                    ? "white"
                                    : "black"
                                  : "white"
                              }
                            />
                          </Box>
                        </Box>
                      );
                    }
                  })}
                  {/*	</MDBTableBody> */}
                  {/* </MDBTable> */}
                </Container>
              </Box>
              <Box display="flex" flexDirection="row">
                <Container
                  style={{ paddingTop: "3vh" }}
                  className="text-left text-white"
                >
                  <hr className="hr-light" />
                  <Box
                    display="flex"
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="space-between"
                  >
                    <Box display="flex" flexDirection="column" size="4">
                      <Typography tag="h6">
                        {newOrder.fields.nexus_country === "CA"
                          ? "GST/HST"
                          : "Tax"}
                      </Typography>
                    </Box>
                    <Box
                      display="flex"
                      flexDirection="column"
                      className="text-right"
                      size="4"
                    >
                      {!props.data ? (
                        <Skeleton variant="text" width="25%" height="2vh" />
                      ) : (
                        <Typography tag="h6">
                          ${newOrder.fields.taxtotal}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  {newOrder.fields.nexus_country === "CA" ? (
                    <Box
                      display="flex"
                      flexDirection="row"
                      justifyContent="space-between"
                      alignItems="space-between"
                    >
                      <Box display="flex" flexDirection="column" size="4">
                        <Typography tag="h6">PST</Typography>
                      </Box>
                      <Box
                        display="flex"
                        flexDirection="column"
                        className="text-right"
                        size="4"
                      >
                        {!props.data ? (
                          <Skeleton variant="text" width="25%" height="2vh" />
                        ) : (
                          <Typography tag="h6">
                            ${newOrder.fields.tax2total}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  ) : null}
                  {settings && settings.custrecord_ng_cs_use_cc_conv_fee ? (
                    <Box
                      display="flex"
                      flexDirection="row"
                      justifyContent="space-between"
                      alignItems="space-between"
                    >
                      <Box display="flex" flexDirection="column" size="4">
                        <Typography tag="h6">Convenience Fee</Typography>
                      </Box>
                      <Box
                        display="flex"
                        flexDirection="column"
                        className="text-right"
                        size="4"
                      >
                        {!props.data ? (
                          <Skeleton variant="text" width="25%" height="2vh" />
                        ) : (
                          <Typography tag="h6">
                            {formatter.format(
                              (parseFloat(
                                settings.custrecord_ng_cs_cc_conv_fee_rate
                              ) /
                                100) *
                                parseFloat(newOrder.fields.total)
                            )}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  ) : null}
                  <hr className="text-center hr-light" />
                  <Box
                    display="flex"
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="space-between"
                  >
                    <Box display="flex" flexDirection="column" size="4">
                      <Typography tag="h4">Total</Typography>
                    </Box>
                    <Box
                      display="flex"
                      flexDirection="column"
                      className="text-right"
                      size="4"
                    >
                      {!props.data ? (
                        <Skeleton variant="text" width="25%" height="3.5vh" />
                      ) : (
                        <Typography style={{ fontWeight: 500 }} tag="h4">
                          {settings && settings.custrecord_ng_cs_use_cc_conv_fee
                            ? formatter.format(
                                parseFloat(newOrder.fields.total) +
                                  (parseFloat(
                                    settings.custrecord_ng_cs_cc_conv_fee_rate
                                  ) /
                                    100) *
                                    parseFloat(newOrder.fields.total)
                              )
                            : formatter.format(
                                parseFloat(newOrder.fields.total)
                              )}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Container>
              </Box>
              <Typography tag="h6" />
            </>
          )}
          <Grid
            container
            spacing={3}
            display="flex"
            flexDirection="row"
            style={{ marginTop: "3vh" }}
            end
          >
            <Grid item xs={6}>
              {props.orderSubmissionError?.display &&
              props.orderSubmissionError?.type.includes("CARD") ? (
                <Button
                  fullWidth
                  disabled={activeStep === 0}
                  onClick={handlePaymentBackStep}
                  color="secondary"
                  variant="outlined"
                  size="large"
                >
                  Back
                </Button>
              ) : null}
            </Grid>
            <Grid item xs>
              <MuiNextLinkButton
                fullWidth
                variant="outlined"
                size="large"
                href="/eventSelect"
              >
                Return Home
              </MuiNextLinkButton>
            </Grid>
          </Grid>
        </Container>
      );
    } else {
      const { activeStep } = props;
      return (
        <div>
          {activeStep === 0 && (
            <AddressStep
              data={props.data}
              activeStep={activeStep}
              addressList={props.addressList}
              addressSelected={props.addressSelected}
              handlePostToNetSuite={handlePostToNetSuite}
              setSelectedAddress={props.setSelectedAddress}
              handleNetsuiteDelete={props.handleNetsuiteDelete}
              addAddressModalVisible={props.addAddressModalVisible}
              setAddAddressModalVisible={props.setAddAddressModalVisible}
            />
          )}
          {activeStep === 1 && (
            <PaymentStep
              data={props.data}
              activeStep={activeStep}
              selectCard={props.selectCard}
              cardSelected={props.cardSelected}
              p_encryptCard={props.p_encryptCard}
              paymentMethods={props.paymentMethods}
              handleNetsuitePut={props.handleNetsuitePut}
              handlePostToNetSuite={handlePostToNetSuite}
              handleNetsuiteDelete={props.handleNetsuiteDelete}
              addAddressModalVisible={props.addAddressModalVisible}
              addPaymentModalVisible={props.addPaymentModalVisible}
              setAddPaymentModalVisible={props.setAddPaymentModalVisible}
            />
          )}
          {activeStep === 2 && (
            <ReviewAndPlaceStep
              setActiveStep={props.setActiveStep}
              activeStep={activeStep}
              cardSelected={props.cardSelected}
              addressSelected={props.addressSelected}
              euaAccepted={props.euaAccepted}
              setEuaAccepted={props.setEuaAccepted}
            />
          )}
          <Container>
            <Grid
              container
              spacing={1}
              pt={3}
              display="flex"
              flexDirection="row"
              justifyContent="space-between"
            >
              <Grid item xs>
                <Button
                  fullWidth
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  color="secondary"
                  variant="outlined"
                >
                  Back
                </Button>
              </Grid>
              <Grid item xs>
                <Button
                  disabled={checkoutDisabled}
                  variant="contained"
                  fullWidth
                  color={
                    activeStep === steps.length - 1 ? "secondary" : "primary"
                  }
                  onClick={handleNext}
                >
                  {activeStep === steps.length - 1 ? "Place Order" : "Next"}
                </Button>
              </Grid>
            </Grid>
          </Container>
        </div>
      );
    }
  };

  const { activeStep } = props;

  return (
    <Container sx={{ minHeight: "80vh" }}>
      {/* <Head> */}
      {/*    <title>Order Checkout</title> */}
      {/* </Head> */}
      <Stepper
        style={{ backgroundColor: /* 'rgb(250 250 250)' */ "transparent" }}
        alternativeLabel
        activeStep={activeStep}
        connector={<ColorlibConnector />}
      >
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel StepIconComponent={ColorlibStepIcon}>
              <span style={{ color: "#000" }}>{label}</span>
            </StepLabel>
          </Step>
        ))}
      </Stepper>
      <div className="margin-footer">{renderStepperContent()}</div>
    </Container>
  );
}

// END OF STEPPER
