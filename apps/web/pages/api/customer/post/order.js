import { NextRequest, NextResponse } from "next/server";
import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

/**
 * Post new order to netsuite
 * @param {NextRequest} req
 * @param {NextResponse} res
 * @returns {Promise<Object>}
 * */
const postOrder = async (req, res) => {
  let response = await request({
    method: "POST",
    uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_purchase_order_cart&deploy=customdeploy_rcs_rl_order_processing`,
    oauth: {
      consumer_key: process.env.OAUTH1_CONSUMER_KEY,
      consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
      token: process.env.OAUTH1_ACCESS_TOKEN,
      token_secret: process.env.OAUTH1_TOKEN_SECRET,
      signature_method: "HMAC-SHA256",
      realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
      version: "1.0",
    },
    body: req.body,
    resolveWithFullResponse: true,
    headers: { "Content-Type": "application/json" },
  });

  const data = await response.body;
  if (response.statusCode !== 200) {
    return {
      error: `Error processing order request: ${data.message}`,
      data,
    };
  }

  return data;
};
export const config = {
  maxDuration: 210,
};

// eslint-disable-next-line consistent-return
export default async function orderHandler(req, res) {
  const customer = req.cookies.customer;
  const contact = req.cookies.contact;
  const session = await getServerSession(req, res, authOptions);
  const { cartId, ceid, boothId, processingId } = req.query;

  async function within(fn, res, duration) {
    const id = setTimeout(() => {
      // Grab cart  order
      const currRequest = {
        url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_rl_rcs_handle_tracking&deploy=customdeploy_ng_rl_rcs_handle_tracking&uid=${customer}&type=orderRecover&cartRecoverId=${cartId}&ceid=${ceid}&boothId=${boothId}&contactId=${contact}&processingId=${processingId}`,
        method: "GET",
      };

      res.status(207).json({
        url: currRequest.url,
        message: "Processing time exceeded order still processing...",
      });
    }, duration);

    try {
      let data = {};
      await fn(req, res) // postOrder
        .then((jsonRes) => {
          data = JSON.parse(jsonRes || "");
        });
      clearTimeout(id);
      setTimeout(() => res.status(201).json(data), 10000);
    } catch (e) {
      res.status(500).json(e);
    }
  }

  if (session) {
    if (req.method === "POST") {
      await within(postOrder, res, 200500); // 200.5 seconds
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
  }
}
