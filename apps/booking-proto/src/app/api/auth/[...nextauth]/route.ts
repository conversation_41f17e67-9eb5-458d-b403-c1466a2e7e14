import NextAuth from 'next-auth'

import { authOptions, ExtendedSession } from '@event-services/auth'

const handler = NextAuth({
	...authOptions,
	// @ts-ignore
	basePath: '/booking/api/auth',
	callbacks: {
		async jwt({ token, account }) {
			if (account) {
				token.accessToken = account.access_token
			}
			return token
		},
		async session({
			session,
			token,
		}: {
			session: ExtendedSession
			token: any
		}) {
			session.accessToken = token.accessToken
			session.user = token.user
			return session
		},
		async signIn({ user, account, profile, email, credentials }: { user: any, account: any, profile: any, email: any, credentials: any }) {
			if (!user?.isBookingUser) {
				throw new Error('invalidUser')
			}
			return true
		},
	},
})

export { handler as GET, handler as POST }
