const fs = require("fs");
const path = require("path");
const fse = require("fs-extra");
const chalk = require("chalk");
const prompts = require("prompts");
const { exec } = require("node:child_process");

const folderName = "booking-calendar";
const suiteCloudProjectFolder =
  "backend/src/FileCabinet/SuiteApps/com.newgennow.cseventservices/scripts/spas";
const fileCabinetUploadFolder = "/SuiteApps/com.newgennow.cseventservices/scripts/spas";

if (fs.existsSync(`./${folderName}`)) {
  console.log("Folder already exists:", folderName);
} else fs.mkdirSync(`./${folderName}`);

if (fs.existsSync(`../${suiteCloudProjectFolder}/${folderName}`)) {
  console.log("SuiteCloud SPAs folder already exists:", folderName);
  fse.removeSync(`../${suiteCloudProjectFolder}/${folderName}`);
} else fs.mkdirSync(`../${suiteCloudProjectFolder}/${folderName}`);

const args = process.argv;
const ciFlags = ["--ci", "-CI"];
const uploadFlags = ["--upload", "-U"];
let ciFlag = false;
let uploadFlag = false;

async function getCmdFlags() {
  args.forEach((arg) => {
    if (ciFlags.includes(arg)) ciFlag = true;
    if (uploadFlags.includes(arg)) uploadFlag = true;
  });
  return true;
}

// console.log('Process args:', args)
let uploadToNetsuite = false;

async function copyToSuiteCloud() {
  let folderReady = false;
  fs.exists("dist", async (exists) => {
    if (exists) {
      console.log("Build folder exists!");
      await fse.copy(
        "./dist",
        `../${suiteCloudProjectFolder}/${folderName}`,
        async function (err, succ) {
          if (err) {
            console.log("Error encountered copying to SuiteCloud folder!", err);
            folderReady = false;
          } else {
            // Copied to NS file cabinet folder - check for ci or upload flags to skip prompt
            console.log(
              chalk.green("Copied dist to suiteCloud project successfully! ✔")
            );

            uploadToNetsuite = await prompts({
              type: !ciFlag || !uploadFlag ? null : "confirm",
              name: "value",
              message: "Would you like to upload to the file cabinet?",
              initial: false,
            });

            if (uploadFlag || uploadToNetsuite?.value) {

              // const filesInsideFileCabinet = getAllFilePaths(`../${suiteCloudProjectFolder}/${folderName}`)

              // console.log('Files in path:', filesInsideFileCabinet)

              // Most likely will be an exec to call suitecloud-plugin
              const suiteCloud = exec(`suitecloud file:upload --paths ${fileCabinetUploadFolder}/${folderName}/index.html`, { cwd: `${process.cwd().replace('booking-calendar', 'backend')}` } ,(err, stdout, stderr) => {
                if (err) {
                  console.error(`exec error: ${err}`, stdout, stderr);
                  return;
                }
                console.log(`stdout: ${stdout}`);
                if (stdout.includes('uploaded')) {
                  console.log(chalk.green.bold('UPLOAD SUCCESSFUL! ✔'));
                }

                stderr && console.error(`stderr: ${stderr}`);
              });

            } else if (ciFlag) {
              console.log(chalk.blue.bold("SKIPPING UPLOAD DUE TO CI FLAG..."));
            } else {
              console.log(
                chalk.cyan.bold(
                  "MAKE SURE TO UPLOAD FOLDER TO NETSUITE ACCOUNT!"
                )
              );
              console.log(
                "RUN",
                chalk.bold(" ALT + SHIFT + U "),
                "ON FOLDER",
                chalk.italic.blue(suiteCloudProjectFolder)
              );
            }

            await fse.copy(
              "./dist",
              `./${folderName}/${folderName}`,
              function (err, succ) {
                if (err) {
                  console.log("Error encountered copying to EH folder!", err);
                  folderReady = false;
                } else {
                  // Not needed for application.
                  /* fs.writeFile(
                    `./${folderName}/${folderName}/generated-window-env.js`,
                    "",
                    (err) => {
                      if (err) throw err;
                      console.log("Window ENV file has been generated!");
                    }
                  );
                  fs.writeFile(
                    `../${suiteCloudProjectFolder}/${folderName}/generated-window-env.js`,
                    "",
                    (err) => {
                      if (err) throw err;
                      console.log(
                        "Window ENV file has been generated in suitecloud project!"
                      );
                    }
                  );
                  console.log(
                    chalk.green("Copied build to EH folder successfully! ✔")
                  );
                  console.log(chalk.yellow("Zipping build..."));
                  zipFolder(
                    `./${folderName}`,
                    `./${folderName}.zip`,
                    function (err) {
                      if (err) {
                        console.log("oh no!", err);
                      } else {
                        console.log(chalk.green("File zipped successfully! ✔"));
                      }
                    }
                  );*/
                  folderReady = true;
                }
              }
            );
            folderReady = true;
          }
        }
      );
    } else console.log("Build folder not found!");
  });
  return folderReady;
}

function getAllFilePaths(directoryPath, fileArray = []) {
  const files = fs.readdirSync(directoryPath);

  files.forEach((file) => {
    const filePath = path.join(directoryPath, file);
    if (fs.statSync(filePath).isDirectory()) {
      getAllFilePaths(filePath, fileArray);
    } else {
      fileArray.push(filePath);
    }
  });

  return fileArray;
}

async function main() {
  await getCmdFlags()
    .then(() => copyToSuiteCloud())
    .catch((err) => console.log(err));
}

console.log("Initializing production build copy to SuiteCloud SPAs...");
try {
  main().catch((err) => console.log(err));
} catch (err) {
  console.error(err);
}
