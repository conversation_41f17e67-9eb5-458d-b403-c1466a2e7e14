const { NEXT_PUBLIC_ACCOUNT_URL_ID } = process.env;

let userConfig = undefined
try {
  userConfig = await import('./v0-user-next.config')
} catch (e) {
  // ignore error
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['@event-services/auth'],
  basePath: '/booking',
  devIndicators: {},
  publicRuntimeConfig: {
    // Available on both server and client
    theme: 'DEFAULT',
    currency: 'USD',
  },
  // Image rendering
  images: {
    domains: [
      'unsplash.com',
      'i.imgur.com',
      `${NEXT_PUBLIC_ACCOUNT_URL_ID?.toLowerCase()}.app.netsuite.com`,
    ],
    unoptimized: true,
  },
  experimental: {
    nextScriptWorkers: true,
    manualClientBasePath: true,
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  async rewrites() {
    return [
      {
        source: '/booking/api/auth/:path*',
        destination: '/api/auth/:path*',
      },
      {
        source: '/sw.js',
        destination: '/_next/static/sw.js',
      },
    ]
  },
}

mergeConfig(nextConfig, userConfig)

function mergeConfig(nextConfig, userConfig) {
  if (!userConfig) {
    return
  }

  for (const key in userConfig) {
    if (
      typeof nextConfig[key] === 'object' &&
      !Array.isArray(nextConfig[key])
    ) {
      nextConfig[key] = {
        ...nextConfig[key],
        ...userConfig[key],
      }
    } else {
      nextConfig[key] = userConfig[key]
    }
  }
}

export default nextConfig
