import { useRouter } from "next/router";
import Head from "next/head";
import Script from "next/script";
import <PERSON><PERSON> from "react-lottie";
import { Box, Typography } from "@mui/material";

import { loadSettings } from "../../lib/settings";
import ServerCookies from "cookies";
import * as animationEventData from "../../public/assets/lottie/l-RV-trip-night-day.json";
import ShopLayout1 from "../../src/components/layouts/ShopLayout1";
import { useEventData } from "../../src/store/eventStore";
import { EventGuard } from "../../src/guards/EventGuard";
import { useSettings } from "../../src/store/zSettingsStore";
import EventPageContent from "../../pages-sections/event/EventPageContent";

const defaultLottieEventLoadOptions = {
    loop: true,
    autoplay: true,
    animationData: animationEventData,
    rendererSettings: {
        backgroundColor: "transparent",
    },
};

export const Show = () => {
    const router = useRouter();
    const { slug } = router.query;
    const { data, error, isLoading } = useEventData(slug);
    const { settings } = useSettings();

    if (isLoading || !data) {
        return (
            <Box className="margin-nav margin-footer">
                <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    style={{ paddingTop: "3rem" }}
                >
                    <Lottie
                        height="18rem"
                        width="18rem"
                        options={defaultLottieEventLoadOptions}
                    />
                </Box>
                <Box
                    display="flex"
                    justifyContent="center"
                    alignSelf="center"
                    sx={{ paddingTop: "3rem" }}
                >
                    <Typography variant="h2">
                        {!error
                            ? "Loading your event..."
                            : "An error occurred loading the event."}
                    </Typography>
                </Box>
            </Box>
        );
    }

    return (
        <ShopLayout1
            topbarBgColor={settings?.custrecord_ng_cs_navbar_bckgrnd_color}
        >
            <Head>
                <title>
                    ConventionSuite Event -{" "}
                    {data.details?.displayName || data.details?.name}
                </title>
            </Head>
            <Script
                defer
                id="google-maps"
                src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places"
            />
            <EventPageContent data={data} />
        </ShopLayout1>
    );
};

Show.getLayout = (page) => <EventGuard>{page}</EventGuard>;

export const getServerSideProps = async ({ req, res }) => {
    const cookies = new ServerCookies(req, res);
    const settings = await loadSettings();

    res.setHeader(
        "Cache-Control",
        "public, s-maxage=10, stale-while-revalidate=59",
    );

    if (
        settings?.custrecord_ng_cs_accent_color ||
        settings?.custrecord_ng_cses_web_primary_color
    ) {
        cookies.set(
            "primaryColor",
            settings?.custrecord_ng_cses_web_primary_color,
            {
                httpOnly: false,
                overwrite: true,
                priority: "high",
            },
        );

        cookies.set("secondaryColor", settings?.custrecord_ng_cs_accent__color, {
            httpOnly: false,
            overwrite: true,
            priority: "high",
        });
    }

    return {
        props: {
            settings,
        },
    };
};

export default Show;
