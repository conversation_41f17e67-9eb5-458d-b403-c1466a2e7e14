/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NAmdConfig ../../amdRestConfig.json
 */
define([
  "N/cache",
  "N/query",
  "N/search",
  "N/config",
  "M/moment",
  "M/lodash",
  "../../../../packages/@extended/ng_extended_utils",
  "N/record",
  "N/search"
], /**
 * @param {cache} cache
 * @param {query} query
 * @param {search} search
 * @param {config} config
 * @param {moment} moment
 * @param {Object} _
 * @param {xUtils} xUtils
 * @param {record} record
 * @param {search} searchModule
 */ (cache, query, search, config, moment, _, xUtils, record, searchModule) => {
  const CACHE_TTL = 900; // 15 minutes
  const CACHE_KEYS_MASTER = "BOOKING_CACHE_KEYS_MASTER"; // Master key to track all cache keys

  /**
   * Adds a cache key to the master list
   * @param {string} key - The cache key to track
   */
  const trackCacheKey = (key) => {
    const BOOKING_CACHE = cache.getCache({ name: "BOOKING_CACHE" });
    
    // Get existing keys
    let existingKeysJson = BOOKING_CACHE.get({ key: CACHE_KEYS_MASTER });
    let existingKeys = existingKeysJson ? JSON.parse(existingKeysJson) : [];
    
    // Add new key if not already tracked
    if (!existingKeys.includes(key)) {
      existingKeys.push(key);
      BOOKING_CACHE.put({
        key: CACHE_KEYS_MASTER,
        value: JSON.stringify(existingKeys),
        ttl: 86400, // 24 hours for the master list
      });
    }
  };

  /**
   * Clears all tracked cache keys
   */
  const clearAllCache = () => {
    const BOOKING_CACHE = cache.getCache({ name: "BOOKING_CACHE" });
    
    // Get all tracked keys
    let existingKeysJson = BOOKING_CACHE.get({ key: CACHE_KEYS_MASTER });
    if (existingKeysJson) {
      let existingKeys = JSON.parse(existingKeysJson);
      
      // Clear each tracked key
      existingKeys.forEach(key => {
        try {
          BOOKING_CACHE.remove({ key });
          log.debug(`Cleared cache key: ${key}`);
        } catch (e) {
          log.error(`Error clearing cache key ${key}:`, e.message);
        }
      });
      
      // Clear the master list
      BOOKING_CACHE.remove({ key: CACHE_KEYS_MASTER });
      log.audit("Cache Cleared", `Cleared ${existingKeys.length} cache entries`);
    }
  };

  /**
   * Calculates duration based on rate type
   * @param {string} spaceId - The space/resource ID
   * @param {Date} startDate - Start date of the booking
   * @param {Date} endDate - End date of the booking
   * @param {string} [rateId] - Optional rate ID to get specific rate type
   * @returns {Object} Object containing duration and rate type
   */
  const calculateDuration = (spaceId, startDate, endDate, rateId) => {
    try {
      let rateType = 1; // Default to daily if no rate found
      let duration = 0;

      // If rate ID is provided, look it up directly
      if (rateId) {
        const rateLookup = searchModule.lookupFields({
          type: "customrecord_ng_cs_space_booking_rate",
          id: rateId,
          columns: ["custrecord_ng_cs_space_rate_type"]
        });
        
        if (rateLookup.custrecord_ng_cs_space_rate_type && rateLookup.custrecord_ng_cs_space_rate_type[0]) {
          rateType = rateLookup.custrecord_ng_cs_space_rate_type[0].value || 1;
        }
      } else {
        // Fall back to looking up by space ID
        const spaceRateSearch = searchModule.create({
          type: "customrecord_ng_cs_space_booking_rate",
          filters: [
            ["custrecord_ng_cs_eb_space_rate", "anyof", spaceId],
            "AND",
            ["isinactive", "is", "F"]
          ],
          columns: [
            "custrecord_ng_cs_space_rate_type",
            "custrecord_ng_cs_space_rate_amount"
          ]
        });

        spaceRateSearch.run().each((result) => {
          rateType = result.getValue("custrecord_ng_cs_space_rate_type") || 1;
          return false; // Only need first result
        });
      }

      // Calculate duration based on rate type
      const startMoment = moment(startDate);
      const endMoment = moment(endDate);

      switch (parseInt(rateType)) {
        case 1: // Daily rate
          // Calculate days (minimum 1 day)
          duration = Math.max(1, endMoment.diff(startMoment, 'days'));
          // If same day but different times, still count as 1 day
          if (duration === 0 && endMoment.isAfter(startMoment)) {
            duration = 1;
          }
          log.debug({
            title: "Duration Calculation - Daily",
            details: `Start: ${startMoment.format()}, End: ${endMoment.format()}, Days: ${duration}`
          });
          break;

        case 2: // Hourly rate
          // Calculate hours (minimum 1 hour)
          duration = Math.max(1, Math.ceil(endMoment.diff(startMoment, 'hours', true)));
          log.debug({
            title: "Duration Calculation - Hourly",
            details: `Start: ${startMoment.format()}, End: ${endMoment.format()}, Hours: ${duration}`
          });
          break;

        case 3: // Square footage
          // For sq.ft., we would need to look up the space's square footage
          // For now, we'll set duration to 1 as a placeholder
          duration = 1;
          log.debug({
            title: "Duration Calculation - Sq.Ft.",
            details: "Square footage calculation not yet implemented, defaulting to 1"
          });
          break;

        default:
          // Default to 1 day if unknown rate type
          duration = 1;
          log.debug({
            title: "Duration Calculation - Unknown",
            details: `Unknown rate type: ${rateType}, defaulting to 1`
          });
      }

      return {
        duration: duration,
        rateType: rateType
      };

    } catch (e) {
      log.error({
        title: "Error calculating duration",
        details: {
          error: e.message,
          spaceId: spaceId,
          startDate: startDate,
          endDate: endDate
        }
      });
      // Return default values on error
      return {
        duration: 1,
        rateType: 1
      };
    }
  };

  /**
   * Defines the function that is executed when a GET request is sent to a RESTlet.
   * Fetches events (Bookings) for the calendar within a specified date range, with caching.
   * @param {Object} requestParams - Parameters from HTTP request URL.
   * @param {string} requestParams.startDate - The start date of the range to fetch (ISO 8601).
   * @param {string} requestParams.endDate - The end date of the range to fetch (ISO 8601).
   * @param {string} [requestParams.venueId] - Optional. The internal ID of a venue to filter by.
   * @returns {string | Object} HTTP response body.
   */
  const get = (requestParams) => {
    const BOOKING_CACHE = cache.getCache({ name: "BOOKING_CACHE" });
    
    const { startDate, endDate, venueId } = requestParams;
    log.audit({
      title: "🚀 GET /events Request Received",
      details: `Params: ${JSON.stringify(requestParams)}`,
    });

    if (!startDate || !endDate) {
      log.error("MISSING_PARAMS", "Start and end date are required.");
      return {
        ok: false,
        error: "MISSING_PARAMS",
        message: "A start and end date are required.",
      };
    }

    const cacheKey = `calendar_events_v1_${
      venueId || "all"
    }_${startDate}_${endDate}`;
    const cachedData = BOOKING_CACHE.get({ key: cacheKey });

    if (cachedData) {
      log.debug("✅ Event Cache Hit", `Cache key: ${cacheKey}`);
      return cachedData
    }

    log.audit("... Event Cache Miss", `Fetching fresh events for key: ${cacheKey}`);

    try {
      const events = getEvents(startDate, endDate, venueId);

      const response = {
        ok: true,
        data: {
          events: events,
        },
      };

      log.debug("Events Fetched", `Count: ${events.length}`);

      BOOKING_CACHE.put({
        key: cacheKey,
        value: JSON.stringify(response),
        ttl: CACHE_TTL,
      });
      
      // Track this cache key
      trackCacheKey(cacheKey);

      log.audit(
        "Events Cached",
        `Event data stored in cache for ${CACHE_TTL} seconds.`
      );

      return JSON.stringify(response);
    } catch (e) {
      log.error("❌ Error fetching booking events", {
        name: e.name,
        message: e.message,
        stack: e.stack,
      });
      return JSON.stringify({ ok: false, error: e.name, message: e.message });
    }
  };

  /**
   * Defines the function that is executed when a POST request is sent to a RESTlet.
   * Creates new Event Bookings from the cart items.
   * @param {string | Object} requestBody - The HTTP request body containing bookings to create
   * @returns {string | Object} HTTP response body with created booking IDs
   * @since 2015.2
   */
  const post = (requestBody) => {
    log.audit({
      title: "🚀 POST /events Request Received",
      details: `Body: ${JSON.stringify(requestBody)}`,
    });

    try {
      // Validate request body
      if (!requestBody || !requestBody.bookings || !Array.isArray(requestBody.bookings)) {
        return JSON.stringify({
          ok: false,
          error: "INVALID_REQUEST",
          message: "Request must contain a 'bookings' array",
        });
      }

      const { bookings, csEventId, customerId } = requestBody;
      const createdBookings = [];
      const errors = [];

      // Process each booking
      bookings.forEach((booking, index) => {
        try {
          // Validate required fields
          if (!booking.title || !booking.start || !booking.end || !booking.resourceId || !booking.venueId) {
            throw new Error(`Missing required fields for booking at index ${index}. Required: title, start, end, resourceId, venueId`);
          }

          // Parse dates
          const startMoment = moment(booking.start);
          const endMoment = moment(booking.end);
          
          if (!startMoment.isValid() || !endMoment.isValid()) {
            throw new Error(`Invalid date format for booking at index ${index}`);
          }

          // Create the Event Booking record
          const bookingRecord = record.create({
            type: "customrecord_ng_cs_event_booking",
            isDynamic: true,
          });

          // Set basic fields
          bookingRecord.setValue({
            fieldId: "name",
            value: booking.title,
          });

          // Set dates and times
          bookingRecord.setValue({
            fieldId: "custrecord_ng_cs_eb_start_date",
            value: startMoment.toDate(),
          });

          if (!booking.allDay) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_start_time",
              value: startMoment.toDate(),
            });
          }

          bookingRecord.setValue({
            fieldId: "custrecord_ng_cs_eb_end_date",
            value: endMoment.toDate(),
          });

          if (!booking.allDay) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_end_time",
              value: endMoment.toDate(),
            });
          }

          // Set all-day flag
          bookingRecord.setValue({
            fieldId: "custrecord_ng_cs_eb_all_day",
            value: booking.allDay || false,
          });

          // Set the venue first (required before setting space)
          if (booking.venueId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_venue",
              value: booking.venueId,
            });
            log.debug({
              title: "Setting Venue",
              details: `Venue ID: ${booking.venueId} for booking: ${booking.title}`,
            });
          }

          // Set the space (resource)
          bookingRecord.setValue({
            fieldId: "custrecord_ng_cs_eb_space_ui",
            value: booking.resourceId,
          });
          log.debug({
            title: "Setting Space",
            details: `Space ID: ${booking.resourceId} for booking: ${booking.title}`,
          });

          // Set the parent CS Event if provided
          if (csEventId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_event",
              value: csEventId,
            });
          }

          // Set customer if provided
          if (customerId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_customer",
              value: customerId,
            });
          }

          // Set default status (e.g., "Tentative" or "1st Hold")
          const defaultStatusId = booking.statusId || "4"; // 4 = 1st Hold
          bookingRecord.setValue({
            fieldId: "custrecord_ng_cs_eb_status",
            value: defaultStatusId,
          });

          // Set the space rate if provided
          if (booking.rateId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_space_rate",
              value: booking.rateId,
            });
            log.debug({
              title: "Setting Space Rate",
              details: `Rate ID: ${booking.rateId} for booking: ${booking.title}`,
            });
          }

          // Set description if provided
          if (booking.description) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_description",
              value: booking.description,
            });
          }

          // Set expected attendance if provided
          if (booking.expectedAttendance) {
            bookingRecord.setValue({
              fieldId: "custrecord_expected_attendance",
              value: booking.expectedAttendance,
            });
          }

          // Calculate and set duration based on rate type
          const durationInfo = calculateDuration(
            booking.resourceId, 
            startMoment.toDate(), 
            endMoment.toDate(),
            booking.rateId
          );
          
          bookingRecord.setValue({
            fieldId: "custrecord_duration",
            value: durationInfo.duration,
          });
          
          log.audit({
            title: "Setting Duration",
            details: `Duration: ${durationInfo.duration} (Rate Type: ${durationInfo.rateType}) for booking: ${booking.title}`,
          });

          // Save the record
          const bookingId = bookingRecord.save({
            enableSourcing: true,
            ignoreMandatoryFields: true,
          });

          log.audit({
            title: "✅ Booking Created",
            details: `ID: ${bookingId}, Title: ${booking.title}`,
          });

          // Add to created bookings array
          createdBookings.push({
            id: bookingId,
            title: booking.title,
            tempId: booking.tempId, // Return the temporary ID for frontend mapping
          });

        } catch (e) {
          log.error({
            title: `❌ Error creating booking ${index}`,
            details: {
              booking: JSON.stringify(booking),
              error: e.message,
              stack: e.stack,
            },
          });
          errors.push({
            index,
            tempId: booking.tempId,
            error: e.message,
          });
        }
      });

      // Clear cache if any bookings were created
      if (createdBookings.length > 0) {
        try {
          clearAllCache();
        } catch (e) {
          log.error("Cache Clear Error", e.message);
        }
      }

      // Return response
      const response = {
        ok: createdBookings.length > 0,
        data: {
          created: createdBookings,
          errors: errors,
        },
        message: errors.length > 0 
          ? `Created ${createdBookings.length} bookings with ${errors.length} errors`
          : `Successfully created ${createdBookings.length} bookings`,
      };

      return JSON.stringify(response);

    } catch (e) {
      log.error("❌ Error in POST /events", {
        name: e.name,
        message: e.message,
        stack: e.stack,
      });
      return JSON.stringify({ 
        ok: false, 
        error: e.name, 
        message: e.message 
      });
    }
  };

  /**
   * Defines the function that is executed when a PUT request is sent to a RESTlet.
   * Updates existing Event Bookings.
   * @param {string | Object} requestBody - The HTTP request body containing bookings to update
   * @returns {string | Object} HTTP response body with updated booking information
   * @since 2015.2
   */
  const put = (requestBody) => {
    log.audit({
      title: "🚀 PUT /events Request Received",
      details: `Body: ${JSON.stringify(requestBody)}`,
    });

    try {
      // Validate request body
      if (!requestBody || !requestBody.bookings || !Array.isArray(requestBody.bookings)) {
        return JSON.stringify({
          ok: false,
          error: "INVALID_REQUEST",
          message: "Request must contain a 'bookings' array",
        });
      }

      const { bookings } = requestBody;
      const updatedBookings = [];
      const errors = [];

      // Process each booking update
      bookings.forEach((booking, index) => {
        try {
          // Validate required fields
          if (!booking.id) {
            throw new Error(`Missing booking ID at index ${index}`);
          }

          // Load the existing Event Booking record
          const bookingRecord = record.load({
            type: "customrecord_ng_cs_event_booking",
            id: booking.id,
            isDynamic: true,
          });

          // Update fields if provided
          if (booking.title !== undefined) {
            bookingRecord.setValue({
              fieldId: "name",
              value: booking.title,
            });
          }

          // Update dates and times if provided
          if (booking.start) {
            const startMoment = moment(booking.start);
            if (!startMoment.isValid()) {
              throw new Error(`Invalid start date format for booking ${booking.id}`);
            }

            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_start_date",
              value: startMoment.toDate(),
            });

            if (!booking.allDay) {
              bookingRecord.setValue({
                fieldId: "custrecord_ng_cs_eb_start_time",
                value: startMoment.toDate(),
              });
            }
          }

          if (booking.end) {
            const endMoment = moment(booking.end);
            if (!endMoment.isValid()) {
              throw new Error(`Invalid end date format for booking ${booking.id}`);
            }

            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_end_date",
              value: endMoment.toDate(),
            });

            if (!booking.allDay) {
              bookingRecord.setValue({
                fieldId: "custrecord_ng_cs_eb_end_time",
                value: endMoment.toDate(),
              });
            }
          }

          // Update all-day flag if provided
          if (booking.allDay !== undefined) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_all_day",
              value: booking.allDay,
            });
          }

          // Update venue if provided (must be set before space)
          if (booking.venueId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_venue",
              value: booking.venueId,
            });
          }

          // Update space if provided
          if (booking.resourceId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_space_ui",
              value: booking.resourceId,
            });
          }

          // Update status if provided
          if (booking.statusId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_status",
              value: booking.statusId,
            });
          }

          // Update space rate if provided
          if (booking.rateId) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_space_rate",
              value: booking.rateId,
            });
          }

          // Update description if provided
          if (booking.description !== undefined) {
            bookingRecord.setValue({
              fieldId: "custrecord_ng_cs_eb_description",
              value: booking.description,
            });
          }

          // Update expected attendance if provided
          if (booking.expectedAttendance !== undefined) {
            bookingRecord.setValue({
              fieldId: "custrecord_expected_attendance",
              value: booking.expectedAttendance,
            });
          }

          // Recalculate duration if dates or rate changed
          if ((booking.start || booking.end || booking.rateId) && booking.resourceId) {
            const currentStart = booking.start ? moment(booking.start).toDate() : bookingRecord.getValue("custrecord_ng_cs_eb_start_date");
            const currentEnd = booking.end ? moment(booking.end).toDate() : bookingRecord.getValue("custrecord_ng_cs_eb_end_date");
            const currentRateId = booking.rateId || bookingRecord.getValue("custrecord_ng_cs_eb_space_rate");
            const currentResourceId = booking.resourceId || bookingRecord.getValue("custrecord_ng_cs_eb_space_ui");

            const durationInfo = calculateDuration(
              currentResourceId,
              currentStart,
              currentEnd,
              currentRateId
            );

            bookingRecord.setValue({
              fieldId: "custrecord_duration",
              value: durationInfo.duration,
            });

            log.audit({
              title: "Updating Duration",
              details: `Duration: ${durationInfo.duration} for booking: ${booking.id}`,
            });
          }

          // Save the record
          const savedId = bookingRecord.save({
            enableSourcing: true,
            ignoreMandatoryFields: false,
          });

          log.audit({
            title: "✅ Booking Updated",
            details: `ID: ${savedId}, Title: ${booking.title || "(unchanged)"}`,
          });

          // Add to updated bookings array
          updatedBookings.push({
            id: savedId,
            title: booking.title || bookingRecord.getValue("name"),
          });

        } catch (e) {
          log.error({
            title: `❌ Error updating booking ${index}`,
            details: {
              booking: JSON.stringify(booking),
              error: e.message,
              stack: e.stack,
            },
          });
          errors.push({
            index,
            id: booking.id,
            error: e.message,
          });
        }
      });

      // Clear cache if any bookings were updated
      if (updatedBookings.length > 0) {
        try {
          clearAllCache();
        } catch (e) {
          log.error("Cache Clear Error", e.message);
        }
      }

      // Return response
      const response = {
        ok: updatedBookings.length > 0,
        data: {
          updated: updatedBookings,
          errors: errors,
        },
        message: errors.length > 0 
          ? `Updated ${updatedBookings.length} bookings with ${errors.length} errors`
          : `Successfully updated ${updatedBookings.length} bookings`,
      };

      return JSON.stringify(response);

    } catch (e) {
      log.error("❌ Error in PUT /events", {
        name: e.name,
        message: e.message,
        stack: e.stack,
      });
      return JSON.stringify({ 
        ok: false, 
        error: e.name, 
        message: e.message 
      });
    }
  };

  /**
   * Fetches Bookings within a date range and joins them to their parent CS Event.
   * @param {string} startDate - The start date of the query range.
   * @param {string} endDate - The end date of the query range.
   * @param {string} [venueId] - Optional. If provided, filters bookings to a specific venue.
   * @returns {Array<Object>} An array of event objects for FullCalendar.
   */
  const getEvents = (startDate, endDate, venueId) => {
    // Use the config of the NetSuite Date format to format the dates for the search
    const companyPref = config.load({ type: config.Type.COMPANY_PREFERENCES });
    const dateFormat = companyPref.getValue("DATEFORMAT") || "M/DD/YYYY";
    const startDateFormatted = moment(startDate).format(dateFormat);
    const endDateFormatted = moment(endDate).format(dateFormat);

    log.debug(
      "... Fetching Events",
      `Range: ${startDate} to ${endDate}, Venue ID: ${venueId || "All"}`
    );
    
    const filters = [
      ["isinactive", "is", "F"],
      "AND",
      [
        "custrecord_ng_cs_eb_start_date",
        search.Operator.ONORBEFORE,
        endDateFormatted,
      ],
      "AND",
      ["custrecord_ng_cs_eb_end_date", search.Operator.ONORAFTER, startDateFormatted],
    ];

    if (venueId) {
      filters.push("AND", [
        "custrecord_ng_cs_eb_venue", // Filter by the Venue on the booking record
        "anyof",
        venueId,
      ]);
    }

    const eventSearch = search.create({
      type: "customrecord_ng_cs_event_booking", // The Booking record
      filters,
      columns: [
        "name", // Booking Title
        "custrecord_ng_cs_eb_start_date",
        "custrecord_ng_cs_eb_start_time",
        "custrecord_ng_cs_eb_end_date",
        "custrecord_ng_cs_eb_end_time",
        "custrecord_ng_cs_eb_space_ui", // This is the resourceId (The Space)
        "custrecord_ng_cs_eb_venue", // This is the venue ID
        "custrecord_ng_cs_eb_status",
        "custrecord_ng_cs_eb_all_day", // All Day Event
        "custrecord_expected_attendance", // Attendance Expected
        "custrecord_ng_cs_eb_description", // Booking Description
        "custrecord_ng_cs_eb_space_rate", // Space Rate ID
        "custrecord_duration", // Duration for pricing
        search.createColumn({
          name: "custrecord_status_color",
          join: "CUSTRECORD_NG_CS_EB_STATUS",
        }),
        // --- Join to parent CS Event ---
        search.createColumn({
          name: "name",
          join: "CUSTRECORD_NG_CS_EB_EVENT",
          label: "csEventName",
        }),
        search.createColumn({
          name: "internalid",
          join: "CUSTRECORD_NG_CS_EB_EVENT",
          label: "csEventId",
        }),
      ],
    });

    const events = [];
    
    try {
      xUtils.getAllResultsFor(eventSearch, (booking) => {
        const bookingId = booking.id;
        try {
          log.debug({
            title: `Processing Booking ID: ${bookingId}`,
            details: `Raw Data: ${JSON.stringify(booking)}`,
          });

          // Format the start and end times
          const startDate = booking.getValue("custrecord_ng_cs_eb_start_date");
          const startTime = booking.getValue("custrecord_ng_cs_eb_start_time");
          const endDate = booking.getValue("custrecord_ng_cs_eb_end_date");
          const endTime = booking.getValue("custrecord_ng_cs_eb_end_time");
          const isAllDay = booking.getValue("custrecord_ng_cs_eb_all_day");
          
          // Convert NetSuite date/time to ISO 8601 format for FullCalendar
          let startISO, endISO;
          
          if (isAllDay === 'T' || isAllDay === true) {
            // For all-day events, use date-only format
            startISO = moment(startDate, dateFormat).format("YYYY-MM-DD");
            endISO = moment(endDate, dateFormat).add(1, 'day').format("YYYY-MM-DD"); // FullCalendar expects exclusive end date for all-day events
          } else {
            // For timed events, combine date and time
            const startDateTime = startDate + " " + (startTime || "00:00");
            const endDateTime = endDate + " " + (endTime || "23:59");
            
            // Parse and convert to ISO 8601
            startISO = moment(startDateTime, dateFormat + " h:mm a").toISOString();
            endISO = moment(endDateTime, dateFormat + " h:mm a").toISOString();
          }

          const event = {
            id: bookingId,
            title: booking.getValue("name"),
            start: startISO,
            end: endISO,
            allDay: isAllDay === 'T' || isAllDay === true,
            resourceId: booking.getValue("custrecord_ng_cs_eb_space_ui"),
            color: booking.getValue({
              name: "custrecord_status_color",
              join: "CUSTRECORD_NG_CS_EB_STATUS",
            }),
            extendedProps: {
              venueId: booking.getValue("custrecord_ng_cs_eb_venue"),
              venueName: booking.getText("custrecord_ng_cs_eb_venue"),
              roomId: booking.getValue("custrecord_ng_cs_eb_space_ui"),
              roomName: booking.getText("custrecord_ng_cs_eb_space_ui"),              
              csEventId: booking.getValue({ name: "internalid", join: "CUSTRECORD_NG_CS_EB_EVENT"  }),
              csEventName: booking.getValue({ name: "name", join: "CUSTRECORD_NG_CS_EB_EVENT" }),
              statusId: booking.getValue("custrecord_ng_cs_eb_status"),
              statusName: booking.getText("custrecord_ng_cs_eb_status"),
              statusColor: booking.getValue({
                name: "custrecord_status_color",
                join: "CUSTRECORD_NG_CS_EB_STATUS",
              }),
              description: booking.getValue("custrecord_ng_cs_eb_description"),
              expectedAttendance: booking.getValue("custrecord_expected_attendance"),
              duration: booking.getValue("custrecord_duration"),
              spaceRateId: booking.getValue("custrecord_ng_cs_eb_space_rate"),
              spaceRateName: booking.getText("custrecord_ng_cs_eb_space_rate"),
            },
          };
          
          events.push(event);
        } catch (e) {
          log.error({
            title: `❌ Error Processing Booking ID: ${bookingId}`,
            details: {
              errorMessage: e.message,
              errorStack: e.stack,
              bookingData: JSON.stringify(booking),
            },
          });
        }
      });
    } catch (e) {
      log.error({
        title: "❌ Error during event search execution",
        details: { errorMessage: e.message, errorStack: e.stack },
      });
      throw e; // Rethrow to be caught by the main handler
    }
    
    log.audit("Events Fetched", `Found ${events.length} bookings.`);
    return events;
  };

  return { get, post, put };
});
