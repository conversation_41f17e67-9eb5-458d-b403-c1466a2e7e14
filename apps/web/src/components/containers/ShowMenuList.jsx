import React, { memo, useState, useTransition } from "react";
import PropTypes from "prop-types";
import { useTheme } from "@mui/material/styles";
import {
  Badge,
  Box,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Collapse,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Skeleton,
  styled,
  Tooltip,
  Typography,
  useMediaQuery,
} from "@mui/material";
import {
  DeleteOutlined,
  ExpandLess,
  ExpandMore,
  PrintOutlined,
  SettingsBackupRestore,
} from "@mui/icons-material";
import Lottie from "react-lottie";
import ShowAdditionalInfoListItemLink from "./ShowAdditionalInfoListItemLink";
import * as animationData from "../../../public/assets/lottie/l-loading-documents.json";
import { useSettings } from "@/store/zSettingsStore";
import { useEvent } from "@/store/eventStore";
import BillOfLadingModal from "../modals/BillOfLadingModal";
import ConfirmDeleteBOLDialog from "../modals/ConfirmDeleteBOLDialog";

/* Example double nav
   Complete Service Manual
   General Information
   Conference Contact Information
   Hotel Accommodations
   Credit Card Authorization Form
   Show Site Address Labels
   Advance Warehouse Labels
   */

const InfoLinks = memo(({ additionalInfo }) => {
  // console.log('Info Links', props.additionalInfo)
  if (additionalInfo === null) {
    return (
      <Box sx={{ textAlign: "center" }}>
        <Skeleton sx={{ pt: 1 }} width="100%" height={42} variant="text" />
        <Skeleton sx={{ pt: 1 }} width="100%" height={42} variant="text" />
        <Skeleton sx={{ pt: 1 }} width="100%" height={42} variant="text" />
      </Box>
    );
  }
  if (additionalInfo && additionalInfo.length === 0) {
    return <ListItemText primary="No Additional Info Available" />;
  }
  return (
    <>
      {additionalInfo.map((info, i) => {
        return (
          <ShowAdditionalInfoListItemLink
            key={i}
            text={`${info?.linkText}`}
            href={`${
              info?.linkUrl.charAt(0) === "/"
                ? `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.app.netsuite.com${info?.linkUrl}`
                : info?.linkUrl
            }`}
          />
        );
      })}
    </>
  );
});

const StyledBadge = styled(Badge)(({ theme }) => ({
  "& .MuiBadge-badge": {
    right: -20,
    top: 10,
    border: `2px solid ${theme.palette.background.paper}`,
    padding: "0 4px",
  },
}));

const BillOfLadingLinks = memo(
  ({
    billOfLadingRequests,
    selectedBol,
    setSelectedBol,
    confirmDeleteBol,
    setConfirmDeleteBol,
  }) => {
    const defaultLottieOptions = {
      loop: true,
      autoplay: true,
      animationData,
      rendererSettings: {
        backgroundColor: "transparent",
        display: "flex",
      },
    };

    // console.log('Info Links', props.additionalInfo)
    const [mouseOver, setMouseOver] = useState(false);
    if (billOfLadingRequests === null) {
      return (
        <Box sx={{ textAlign: "center" }}>
          <Lottie height="3rem" width="100%" options={defaultLottieOptions} />
          <Skeleton sx={{ pt: 1 }} width="100%" height={42} variant="text" />
          <Skeleton sx={{ pt: 1 }} width="100%" height={42} variant="text" />
          <Skeleton sx={{ pt: 1 }} width="100%" height={42} variant="text" />
        </Box>
      );
    }

    if (billOfLadingRequests && billOfLadingRequests.length === 0) {
      return (
        <ListItem>
          <Grid container>
            <Grid item xs={12} sx={{ textAlign: "center" }}>
              <Typography variant="subtitle2">
                No <b>Bill of Lading</b> requests available as this time
              </Typography>
            </Grid>
          </Grid>
        </ListItem>
      );
    }

    const variants = {
      hover: {
        opacity: 1,
        x: 0,
        transition: {
          when: "beforeChildren",
          duration: 0.5,
          delay: 0.35,
          type: "spring",
          stiffness: 100,
          damping: 20,
          delayChildren: 0.3,
          staggerChildren: 0.2,
        },
      },
      leave: {
        opacity: 0,
        x: 3,
        transition: {
          damping: 20,
          duration: 0.5,
          stiffness: 260,
          type: "spring",
          delayChildren: 0.3,
          staggerChildren: 0.2,
          when: "afterChildren",
        },
      },
    };

    return (
      <>
        {billOfLadingRequests.map((info, i) => {
          return (
            <ListItem
              sx={{ py: 0.1 }}
              key={info.id}
              className="rounded-md"
              onMouseOver={() => setMouseOver(info.id)}
              onMouseLeave={() => setMouseOver(false)}
              secondaryAction={
                <Box
                  sx={{
                    opacity: mouseOver === info.id ? 1 : 0,
                    transition: "opacity 300ms ease-in-out",
                  }}
                >
                  {!info.cancelled ? (
                    <>
                      <Tooltip
                        title={
                          <Typography fontSize={10}>Print Request</Typography>
                        }
                        placement="top-end"
                        arrow
                      >
                        <IconButton
                          onClick={() => window.open(info.printUrl, "_blank")}
                          size="small"
                          edge="end"
                          aria-label="print"
                        >
                          <PrintOutlined />
                        </IconButton>
                      </Tooltip>
                      <Tooltip
                        color="secondary"
                        title={
                          <Typography fontSize={10}>Cancel Request</Typography>
                        }
                        placement="top-end"
                        arrow
                      >
                        <IconButton
                          onClick={() => setConfirmDeleteBol(info)}
                          size="small"
                          color="error"
                          edge="end"
                          aria-label="delete"
                        >
                          <DeleteOutlined />
                        </IconButton>
                      </Tooltip>
                    </>
                  ) : (
                    <Tooltip
                      color="secondary"
                      title={
                        <Typography fontSize={10}>Restore Request</Typography>
                      }
                      placement="top-end"
                      arrow
                    >
                      <IconButton
                        onClick={() => setConfirmDeleteBol(info)}
                        size="small"
                        color="error"
                        edge="end"
                        aria-label="delete"
                      >
                        <SettingsBackupRestore />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              }
            >
              <ListItemButton
                onClick={info.cancelled ? undefined : () => setSelectedBol(info)}
                dense
                role={undefined}
                sx={{ mr: 3.5 }}
                className="rounded-md"
              >

                  <ListItemText
                    id={info.id}
                    primary={info.name}
                    sx={info.cancelled ? { textDecoration: 'line-through', opacity: 0.5 } : undefined}
                  />
                  {info.cancelled && (
                    <Chip
                      label="Cancelled"
                      size="small"
                      color="primary" // Adjust color as needed
                      sx={{ mt: 0.5 }} // Adds vertical spacing
                    />
                  )}

              </ListItemButton>

            </ListItem>
          );
        })}
      </>
    );
  },
);

const EventImageLogo = memo(({ data }) => {
  if (data) {
    if (data.details?.image.url) {
      return (
        <CardMedia
          sx={{
            height: 'auto',
            width: '100%',
            objectFit: 'contain'
          }}
          component="img"
          alt="event logo"
          image={`${data.details?.image.url}`}
          className="rounded-md"
        />
      );
    }
    return null;
  }

  return (
    <div className="flex justify-center mb-4">
      <Skeleton animation="wave" variant="rect" width="25%" height="4rem" />
    </div>
  );
});

const ShowMenuListItems = ({
  data,
  tabs,
  getTabState,
  setCurrentTab,
  additionalInfo,
  billOfLadingRequests,
}) => {
  const [open, setOpen] = React.useState(true);
  const [bolOpen, setBolOpen] = React.useState(true);
  const theme = useTheme();
  const [selectedBol, setSelectedBol] = useState(null);
  const [confirmDeleteBol, setConfirmDeleteBol] = useState(null);
  const [isPending, startTransition] = useTransition();

  const midScreens = useMediaQuery(theme.breakpoints.down("md"));
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { settings } = useSettings();
  const { event } = useEvent();

  const handleClick = () => {
    setOpen(!open);
  };
  const handleRequestExpand = () => {
    setBolOpen(!bolOpen);
  };
  const handleListItemClick = (event, index) => {
    startTransition(() => {
      setCurrentTab(index);
    });
  };

  return (
    <Box display="flex" flexDirection="column" justifyContent="center">
      <Card sx={{ marginTop: "3vh", boxShadow: midScreens ? "none" : "" }}>
        <EventImageLogo data={data} />
        <CardContent
          sx={{
            opacity: isPending ? 0.7 : 1,
            transition: "opacity 0.3s ease-in-out",
            pointerEvents: isPending ? "none" : "auto",
          }}
        >
          <List
            sx={{ borderRight: 0 }}
            component="nav"
            aria-labelledby="nested-list-subheader"
          >
            {tabs.map((tab, index) => (
              <ListItemButton
                key={tab.id}
                disabled={tab.disabled}
                sx={{
                  my: 0.5,
                }}
                selected={getTabState === tab.id}
                onClick={(event) => handleListItemClick(event, tab.id)}
                className="rounded-md"
              >
                <ListItemIcon>{tab.icon}</ListItemIcon>
                <ListItemText primary={tab.label} />
              </ListItemButton>
            ))}
            <Divider sx={{ my: 1 }} />
            <ListItemButton
              sx={{
                my: 1,
              }}
              onClick={handleClick}
            >
              <ListItemText className="text-uppercase">
                <span
                  style={{
                    fontSize: "10pt",
                    color: "#8F95A0",
                    fontWeight: "bold",
                  }}
                >
                  <StyledBadge
                    color="secondary"
                    badgeContent={
                      Array.isArray(additionalInfo) && additionalInfo.length
                    }
                    showZero
                  >
                    Additional Information
                  </StyledBadge>
                </span>
              </ListItemText>
              {open ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>
            <Collapse in={open} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <InfoLinks additionalInfo={additionalInfo} />
              </List>
            </Collapse>
            {/* Render Bill of Lading Requests if feature is enabled */}
            {event &&
              settings?.custrecord_ng_bol_enable &&
              event?.details?.bolEnabled && (
                <>
                  <Divider sx={{ my: 1 }} />
                  <ListItemButton
                    sx={{
                      my: 1,
                    }}
                    onClick={handleRequestExpand}
                    className="rounded-md"
                  >
                    <ListItemText className="text-uppercase">
                      <span
                        style={{
                          fontSize: "10pt",
                          color: "#8F95A0",
                          fontWeight: "bold",
                        }}
                      >
                        <StyledBadge
                          color="secondary"
                          badgeContent={
                            Array.isArray(billOfLadingRequests) &&
                            billOfLadingRequests.length
                          }
                          showZero
                        >
                          Outbound Shipment Requests
                        </StyledBadge>
                      </span>
                    </ListItemText>
                    {bolOpen ? <ExpandLess /> : <ExpandMore />}
                  </ListItemButton>
                  <Collapse in={bolOpen} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      <BillOfLadingLinks
                        billOfLadingRequests={billOfLadingRequests}
                        selectedBole={selectedBol}
                        setSelectedBol={setSelectedBol}
                        confimrDeleteBol={confirmDeleteBol}
                        setConfirmDeleteBol={setConfirmDeleteBol}
                      />
                    </List>
                  </Collapse>
                </>
              )}
          </List>
        </CardContent>
      </Card>
      <BillOfLadingModal
        open={selectedBol}
        setSelectedBol={setSelectedBol}
        selectedBolRequest={selectedBol}
        onClose={() => setSelectedBol(null)}
      />
      <ConfirmDeleteBOLDialog
        open={confirmDeleteBol}
        onClose={() => setConfirmDeleteBol(null)}
        selectedBolRequest={confirmDeleteBol}
      />
    </Box>
  );
};
export default ShowMenuListItems;

ShowMenuListItems.propTypes = {
  getTabState: PropTypes.string.isRequired,
  setCurrentTab: PropTypes.func.isRequired,
};
