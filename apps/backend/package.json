{"name": "recon-suite-cloud", "version": "12.5.81", "description": "SDF react back end for RESTful communication.", "main": "/src/FileCabinet/SuiteApps", "dependencies": {"cheerio": "^1.0.0-rc.12", "jest": "^29.7.0"}, "devDependencies": {"@eslint/js": "^9.2.0", "@oracle/suitecloud-unit-testing": "^1.6.0", "eslint": "^9.2.0", "eslint-plugin-suitescript": "^1.3.1", "prettier": "^3.2.5"}, "scripts": {"test": "jest --collectCoverage --verbose --forceExit --detect<PERSON><PERSON><PERSON>andles"}, "repository": {"type": "git", "url": "git+https://github.com/newgen-business-solutions/recon-suite-cloud.git"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/newgen-business-solutions/recon-suite-cloud/issues"}, "homepage": "https://github.com/newgen-business-solutions/recon-suite-cloud#readme"}