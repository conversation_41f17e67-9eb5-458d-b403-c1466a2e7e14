/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var _TransTypes = new Array("cashrefund","cashsale","customerdeposit","customerpayment","customerrefund");

var _ProcActive = false;
var evType = null;
var _ccProcTriggerFields = new Array("paymentmethod","department","creditcard","ccnumber");

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType 
 * 
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function pmnt_clientPageInit(type){
	evType = type;
	if (_tools.isInArray(nlapiGetRecordType(), _TransTypes)) {
		_ProcActive = true;
	}
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @returns {Boolean} True to continue save, false to abort save
 */
function pmnt_clientSaveRecord(){
	
    return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function pmnt_clientFieldChanged(type, name, linenum){
	if (_ProcActive) {
		if (name == "ccnumber") {
			var cardNumber = nlapiGetFieldValue(name);
			if (!_tools.isEmpty(cardNumber) && cardNumber.search(/\*/) < 0) {
				_scLib.selectCardType(cardNumber, true, "ccnumber", "paymentmethod");
			}
		}
		
		if (_scLib.MultiCCProcessing && _tools.isInArray(name, _ccProcTriggerFields)) {
			nlapiSetFieldValue("creditcardprocessor", _scLib.selectProcessor());
		}
	}
}
