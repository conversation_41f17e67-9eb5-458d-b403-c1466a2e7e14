import { SvgIcon } from "@mui/material";

const BasketBall = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 28 29">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.4201 6.33331C15.3886 6.33331 17.1839 7.08735 18.5338 8.32127C18.3788 9.06964 18.2972 9.85729 18.2972 10.6688C18.2972 11.0118 18.3118 11.3497 18.3398 11.6808C17.7534 11.7244 17.1882 11.7837 16.6428 11.8572C15.6649 9.42215 14.0937 7.4911 12.1331 6.44468C12.5517 6.37288 12.9812 6.33331 13.4201 6.33331ZM18.8391 10.6685C18.8391 10.0207 18.893 9.38913 18.9965 8.78247C19.7324 9.58096 20.2993 10.5374 20.6407 11.5944C20.0359 11.596 19.4482 11.612 18.8814 11.6439C18.854 11.3245 18.8391 10.9986 18.8391 10.6685ZM16.2944 12.4521C15.4777 12.5752 14.7096 12.7307 13.9869 12.9119C14.5064 14.0632 14.8022 15.3741 14.8022 16.7648C14.8022 18.571 14.399 20.1942 13.6388 21.5C14.2809 21.4813 14.9177 21.3835 15.5349 21.2044C15.9592 21.0813 16.3716 20.9207 16.768 20.7256C16.7975 20.711 16.8265 20.6959 16.8557 20.6808L16.8558 20.6807L16.8888 20.6635L16.9514 20.3444C17.0801 19.5364 17.2132 18.7008 17.2132 17.8486C17.2131 15.9089 16.8858 14.0722 16.2944 12.4521ZM18.9423 12.1831C19.5373 12.1501 20.1548 12.1346 20.7929 12.136C20.9314 12.7085 21.0067 13.3054 21.0067 13.9199C21.0067 14.6959 20.8891 15.4451 20.6715 16.1507C19.818 15.4196 19.1802 13.9324 18.9423 12.1831ZM7.75755 9.04281C7.72697 9.04281 7.69665 9.04164 7.66622 9.04047L7.62558 9.03902C6.76151 10.0662 6.16597 11.3155 5.93783 12.6708C5.93714 12.6758 5.93622 12.6805 5.93527 12.6854L5.93484 12.6876C5.90369 12.8751 5.88093 13.0648 5.86387 13.2558C5.8612 13.2866 5.85903 13.3178 5.8569 13.3486L5.85681 13.3499C5.84247 13.5387 5.83325 13.7283 5.83325 13.9199C5.83325 14.7043 5.95301 15.4613 6.17492 16.1736C7.72557 14.865 10.0002 13.4633 13.2072 12.5597C12.0461 10.445 10.0379 9.04281 7.75755 9.04281ZM11.2797 6.64278L11.2867 6.64085C13.3787 7.5163 15.0632 9.43571 16.0936 11.9366C15.2621 12.0658 14.4796 12.2279 13.7437 12.4162C12.558 10.1781 10.4874 8.6497 8.10925 8.51177C8.11412 8.50704 8.11866 8.5021 8.1232 8.49716L8.1232 8.49715C8.12774 8.49221 8.13229 8.48727 8.13716 8.48252L8.13882 8.48096C8.14745 8.47283 8.15584 8.46493 8.16425 8.45677C8.43331 8.1972 8.7224 7.95795 9.02856 7.73954L9.05609 7.71985C9.07501 7.70627 9.09394 7.6927 9.11311 7.67938C9.25589 7.58076 9.4022 7.48646 9.55203 7.39732C9.56987 7.38679 9.5878 7.3766 9.60573 7.36643C9.61964 7.35853 9.63354 7.35063 9.6474 7.34258C9.80076 7.254 9.95682 7.16918 10.1172 7.09142L10.12 7.08996C10.1236 7.088 10.1271 7.0861 10.1308 7.08437C10.299 7.00308 10.4722 6.92938 10.6478 6.8603C10.6608 6.85529 10.6737 6.85014 10.6866 6.84499C10.6996 6.83984 10.7125 6.83469 10.7255 6.82968C10.9019 6.7625 11.0813 6.70154 11.2642 6.64734C11.2693 6.64565 11.2745 6.64422 11.2797 6.64278ZM18.3974 12.2192C17.8604 12.2587 17.341 12.3116 16.8395 12.3766C17.4293 14.0294 17.7553 15.8892 17.7553 17.8489C17.7553 18.7003 17.6315 19.5164 17.5057 20.3075C18.8418 19.45 19.8917 18.186 20.4805 16.6906C19.4287 15.9057 18.6614 14.2285 18.3974 12.2192ZM6.37136 16.7197C7.8922 15.4004 10.1763 13.9598 13.4575 13.0518C13.9688 14.1543 14.2606 15.4196 14.2606 16.7649C14.2606 18.6025 13.8247 20.2317 13.0056 21.4949C9.99016 21.3315 7.43917 19.3988 6.37136 16.7197ZM8.54329 18.5263C8.60262 18.5263 8.66251 18.5068 8.71262 18.4668C8.82915 18.373 8.84811 18.2026 8.75435 18.0858C8.69093 18.0069 8.62158 17.924 8.54978 17.8387L8.54659 17.8349C8.33654 17.5846 8.09906 17.3017 7.9721 17.0494C7.90462 16.9158 7.74177 16.8616 7.60821 16.9294C7.47464 16.9969 7.42071 17.1597 7.48819 17.2933C7.64405 17.6028 7.90426 17.9127 8.13408 18.1864L8.13494 18.1874C8.20405 18.2695 8.2707 18.3491 8.33166 18.4247C8.38533 18.4917 8.46389 18.5263 8.54329 18.5263ZM11.5232 20.4233C11.6362 20.4233 11.7413 20.3523 11.7798 20.2396C11.828 20.0979 11.7524 19.9437 11.6107 19.8954C11.1011 19.7215 10.589 19.4654 10.0454 19.1118C9.91969 19.0303 9.75198 19.0658 9.67043 19.1912C9.58914 19.3167 9.62464 19.4844 9.74982 19.5665C10.3316 19.9447 10.8832 20.2203 11.4359 20.4086C11.4649 20.4184 11.4942 20.4233 11.5232 20.4233Z"
      />
    </SvgIcon>
  );
};
export default BasketBall;
