import Link from "next/link";
import {
  Box,
  Container,
  Grid,
  <PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  styled,
  Typography,
} from "@mui/material";
import { Paragraph, Span } from "components/Typography";
import Google from "components/icons/Google";
import Twitter from "components/icons/Twitter";
import Youtube from "components/icons/Youtube";
import Facebook from "components/icons/Facebook";
import Instagram from "components/icons/Instagram";
import { useSettings } from "../../store/zSettingsStore";
import Image from "next/image";

// styled component
const StyledLink = styled(Link)(({ theme }) => ({
  display: "block",
  borderRadius: 4,
  cursor: "pointer",
  position: "relative",
  padding: "0.3rem 0rem",
  color: theme.palette.grey[500],
  "&:hover": {
    color: theme.palette.grey[100],
  },
}));

const Footer1 = () => {
  const { settings } = useSettings();

  return (
    <footer>

      {settings?.custrecord_footer && (
        <Box>
          <div dangerouslySetInnerHTML={{ __html: settings?.custrecord_footer }}></div>
        </Box>
      )}
      <Box bgcolor="#222935">
        <Container
          sx={{
            p: "1rem",
            color: "white",
          }}
        >
          <Box pt={5} overflow="hidden">
            {(settings?.custrecord_ng_cs_exhibitor_serv_phone ||
              settings?.custrecord_ng_cs_contact_us_url) && (
              <Grid container spacing={3}>
                <Grid item lg={3} md={6} sm={6} xs={12}>
                  <Box
                    fontSize="18px"
                    fontWeight="600"
                    mb={1.5}
                    lineHeight="1"
                    color="white"
                  >
                    Contact Us
                  </Box>

                  {settings.custrecord_ng_cs_contact_us_url && (
                    <Box py={0.6} color="grey.500">
                      <a href={settings.custrecord_ng_cs_contact_us_url}>
                        Exhibitor Services
                      </a>
                    </Box>
                  )}

                  {settings?.custrecord_ng_cs_exhibitor_serv_phone && (
                    <Box py={0.6} mb={2} color="grey.500">
                      Phone: {settings?.custrecord_ng_cs_exhibitor_serv_phone}
                    </Box>
                  )}
                </Grid>
              </Grid>
            )}
            <Grid
              container
              alignItems="center"
              display={{ xs: "block", md: "flex" }}
              textAlign={{ xs: "center", md: "left" }}
              justifyContent="space-between"
            >
              <Grid item md={6} sm={6} xs={12}>
                <Typography variant="subtitle1">
                  &copy; {new Date().getFullYear()}
                </Typography>
              </Grid>
              <Grid item md={6} sm={6} xs={12}>
                <Paragraph sx={{ textAlign: "right" }}>
                  Powered by<Span>
                    <a href="https://newgennow.com" target="_blank" rel="noopener noreferrer">
                      <Image style={{ objectFit: "contain", objectPosition: "bottom", marginBottom: -8}} src="/assets/images/logo/ConventionSuite-white.svg" alt="NewGenNow" width={144} height={58} />
                    </a>
                  </Span>
                </Paragraph>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>
      <Container />
    </footer>
  );
};
const aboutLinks = [
  "Careers",
  "Our Stores",
  "Our Cares",
  "Terms & Conditions",
  "Privacy Policy",
];
const customerCareLinks = [
  "Help Center",
  "How to Buy",
  "Track Your Order",
  "Corporate & Bulk Purchasing",
  "Returns & Refunds",
];
const iconList = [
  {
    icon: Facebook,
    url: "https://www.facebook.com/UILibOfficial",
  },
  {
    icon: Twitter,
    url: "https://twitter.com/uilibofficial",
  },
  {
    icon: Youtube,
    url: "https://www.youtube.com/channel/UCsIyD-TSO1wQFz-n2Y4i3Rg",
  },
  {
    icon: Google,
    url: "https://www.google.com/search?q=ui-lib.com",
  },
  {
    icon: Instagram,
    url: "https://www.instagram.com/uilibofficial/",
  },
];
export default Footer1;
