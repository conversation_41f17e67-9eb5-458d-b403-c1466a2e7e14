import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useBookingCart } from "src/store/booking-cart"
import { toast } from "sonner"

interface BookingSubmission {
  id?: string
  tempId: string
  title: string
  start: string
  end: string
  allDay?: boolean
  resourceId: string
  venueId: string
  statusId?: string
  description?: string
  expectedAttendance?: string
  rateTypeId?: string
  rateId?: string
  rate?: number
}

interface SubmitBookingsPayload {
  bookings: BookingSubmission[]
  csEventId?: string
  customerId?: string
}

interface SubmitBookingsResponse {
  ok: boolean
  data: {
    created: Array<{
      id: string
      title: string
      tempId: string
    }>
    errors: Array<{
      index: number
      tempId: string
      error: string
    }>
  }
  message: string
}

async function submitBookings(payload: SubmitBookingsPayload): Promise<SubmitBookingsResponse> {
  const response = await fetch("/api/bookings", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.details || error.error || "Failed to submit bookings")
  }

  return response.json()
}

export function useBookingCartSubmit() {
  const queryClient = useQueryClient()
  const { items, clearCart } = useBookingCart()

  return useMutation<SubmitBookingsResponse, Error, { csEventId?: string; customerId?: string }>({
    mutationFn: async ({ csEventId, customerId }) => {
      // Transform cart items to the format expected by the API
      const bookings: BookingSubmission[] = items.map((item) => ({
        tempId: item.id,
        title: item.booking.title || "Untitled Booking",
        start: item.booking.start instanceof Date ? item.booking.start.toISOString() : item.booking.start,
        end: item.booking.end instanceof Date ? item.booking.end.toISOString() : item.booking.end,
        allDay: item.booking.allDay,
        resourceId: item.booking.resourceId || item.room.id,
        venueId: item.booking.venueId || item.room.venueId,
        statusId: item.booking.status === "Tentative" ? "4" : undefined, // 4 = 1st Hold
        description: item.booking.description,
        expectedAttendance: item.booking.expectedAttendance,
        rateTypeId: item.booking.rateTypeId,
        rateId: item.booking.rateId,
        rate: item.booking.rate,
      }))

      return submitBookings({
        bookings,
        csEventId,
        customerId,
      })
    },
    onSuccess: (data) => {
      if (data.ok) {
        // Clear the cart after successful submission
        clearCart()
        
        // Invalidate the calendar events query to refresh the calendar
        queryClient.invalidateQueries({ queryKey: ["calendarEvents"] })
        
        // Show success toast with details
        if (data.data.errors.length === 0) {
          toast.success(data.message, {
            description: `${data.data.created.length} bookings created successfully`,
          })
        } else {
          toast.warning(data.message, {
            description: `${data.data.created.length} bookings created, ${data.data.errors.length} failed`,
          })
        }
      } else {
        toast.error("Failed to create bookings", {
          description: data.message,
        })
      }
    },
    onError: (error) => {
      toast.error("Error submitting bookings", {
        description: error.message || "Please try again later",
      })
    },
  })
} 