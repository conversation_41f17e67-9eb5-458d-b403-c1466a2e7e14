import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { decrypt } from './utils/webCryptoEncryption'

export async function middleware(req: NextRequest) {
	try {
		console.log('Middleware started')

		const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET })

		if (!token) {
			console.log('No token found, redirecting to sign in')
			const signInUrl = new URL('/auth/signin', req.url)
			signInUrl.searchParams.set('returnUrl', req.url)
			return NextResponse.redirect(signInUrl)
		}

		console.log('Token found:', token)

		const exhibitorPortalUrl = token.exhibitorUrl as string

		if (!exhibitorPortalUrl) {
			console.log('Exhibitor portal URL not found')
			return new NextResponse('Exhibitor portal URL not found', { status: 400 })
		}

		console.log('Exhibitor Portal URL:', exhibitorPortalUrl)
		console.log(
			'Fetching shared configuration from:',
			`${exhibitorPortalUrl}/api/generate-scan-token`,
		)

		const response = await fetch(
			`${exhibitorPortalUrl}/api/generate-scan-token`,
			{
				headers: {
					Authorization: `Bearer ${token.accessToken}`,
					Cookie: req.headers.get('cookie') || '',
				},
			},
		)

		if (!response.ok) {
			console.log(
				'Failed to fetch shared configuration:',
				response.status,
				response.statusText,
			)
			const errorText = await response.text()
			console.log('Error details:', errorText)
			return new NextResponse('Failed to fetch shared configuration', {
				status: 500,
			})
		}

		const responseData = await response.json()
		console.log('Response data received:', responseData)

		if (!responseData.token || typeof responseData.token !== 'string') {
			console.log('Invalid token received:', responseData.token)
			return new NextResponse('Invalid token received', { status: 500 })
		}

		const decryptedData = await decrypt(responseData.token)
		console.log('Decrypted data:', decryptedData)

		const sharedEnvVars = JSON.parse(decryptedData)
		console.log('Parsed shared environment variables:', sharedEnvVars)

		const requestHeaders = new Headers(req.headers)
		requestHeaders.set('x-shared-env', JSON.stringify(sharedEnvVars))
		requestHeaders.set('x-exhibitor-portal-url', exhibitorPortalUrl)

		const res = NextResponse.next({
			request: {
				headers: requestHeaders,
			},
		})

		res.cookies.set('exhibitorPortalUrl', exhibitorPortalUrl, {
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'strict',
			maxAge: 60 * 60 * 24 * 7, // 1 week
		})

		console.log('Middleware completed')
		return res
	} catch (error) {
		console.error('Middleware error:', error)
		return new NextResponse('Internal Server Error', { status: 500 })
	}
}

export const config = {
	matcher: ['/((?!api/auth|_next/static|_next/image|favicon.ico).*)'],
}
