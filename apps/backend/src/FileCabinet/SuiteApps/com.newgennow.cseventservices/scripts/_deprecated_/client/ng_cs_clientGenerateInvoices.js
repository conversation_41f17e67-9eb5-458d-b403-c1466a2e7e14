/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */
var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType 
 * 
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function clientPageInit(type){
	
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @returns {Boolean} True to continue save, false to abort save
 */
function clientSaveRecord(){
	var showSel = _tools.getMultiSelect("custpage_show", null, false, null);
	if (showSel.length > 5) {
		window.alert("The maximum selection count is 5. Please remove some of your selections.");
		return false;
	}
	
	for (var s = 0; s < showSel.length; s++) {
		var listId = "order_list_{0}".NG_Format(showSel[s]);
		var lineCount = nlapiGetLineItemCount(listId);
		var selectCount = 0;
		for (var l = 1; l <= lineCount; l++) {
			if (nlapiGetLineItemValue(listId, "selected", l) == "T") {
				selectCount++;
			}
		}
		
		if (selectCount >= 50) {
			var showName = nlapiLookupField("customrecord_show", showSel[s], "name");
			var diff = Math.round(selectCount - 50).toFixed(0);
			window.alert("A maximum of 50 individual orders can be selected per event for invoicing. Please deselect at least {0} order(s) for event \"{1}\".".NG_Format(diff, showName));
		}
	}
	
	return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function clientValidateField(type, name, linenum){
	
	return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function clientFieldChanged(type, name, linenum){
	if (name == "custpage_show") {
		var showSel = _tools.getMultiSelect("custpage_show", null, false, null);
		if (showSel.length > 5) {
			window.alert("The maximum selection count is 5. Please remove some of your selections.");
			return;
		}
		
		if (nlapiGetFieldValue("custpage_display_details") == "T") {
			nlapiSetFieldValue("custpage_display_details", "F", false);
		}
	}
	
	if (name == "custpage_invoice_date") {
		var invDate = nlapiGetFieldValue(name);
		if (!_tools.isEmpty(invDate)) {
			var isClosed = nlapiRequestURL(nlapiGetFieldValue("custpage_url"), { recDate : invDate }).getBody();
			if (isClosed == "T") {
				window.alert("The invoice date you have chosen lies within a closed accounting period. Please enter a different date or leave blank to use today's date.");
				nlapiSetFieldValue(name, "", false);
			}
		}
	}
	
	if (name == "custpage_display_details") {
		var showDetails = nlapiGetFieldValue("custpage_display_details") == "T";
		if (showDetails) {
			ng_refreshDetails();
		}
	}
	
	if (!_tools.isEmpty(type) && type.search("order_list_") >= 0) {
		if (name == "selected") {
			if (nlapiGetLineItemValue(type, "selected", linenum) == "T") {
				var lineCount = nlapiGetLineItemCount(type);
				var selectCount = 0;
				for (var l = 1; l <= lineCount; l++) {
					if (nlapiGetLineItemValue(type, "selected", l) == "T") {
						selectCount++;
					}
				}
				
				if (selectCount >= 50) {
					window.alert("A maximum of 50 individual orders can be selected per event for invoicing. This line will be deselected.");
					nlapiSetLineItemValue(type, "selected", linenum, "F", false);
				}
			}
		}
	}
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @returns {Void}
 */
function clientPostSourcing(type, name) {
	
}

function ng_refreshDetails() {
	var shows = _tools.getMultiSelect("custpage_show", null, false, null);
	if (!_tools.isEmpty(shows) && shows.length > 0) {
		var ordTypes = _tools.getMultiSelect("custpage_order_types", null, false, null);
		if (ordTypes.length < 1) {
			ordTypes.push(_scLib.DefaultExhibitorOrderType);
		}
		var sp = NewGen.lib.url.escapeURL(shows.join(","));
		var ot = NewGen.lib.url.escapeURL(ordTypes.join(","));
		var targetURL = "{0}&shdt={1}&shsl={2}&ot={3}".NG_Format(nlapiGetFieldValue("custpage_page_url"), "T", sp, ot);
		NS.form.setChanged(false);
		window.location.replace(targetURL);
	} else {
		window.alert("Please select at least one event first before attempting to retrieve details.");
		nlapiSetFieldValue("custpage_display_details", "F", false);
	}
}
