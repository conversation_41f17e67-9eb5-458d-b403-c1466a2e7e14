import React, { Fragment } from "react";
import { useRouter } from "next/router";
import { format, parse } from "date-fns";
import Head from "next/head";
import { ShoppingBag, ArrowBack } from "@mui/icons-material";
import {
  Avatar,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  styled,
  Typography,
} from "@mui/material";
import { motion } from "motion/react";
import TableRow from "components/TableRow";
import Delivery from "components/icons/Delivery";
import PackageBox from "components/icons/PackageBox";
import TruckFilled from "components/icons/TruckFilled";
import { H5, H6, Paragraph } from "components/Typography";
import { FlexBetween, FlexBox } from "components/flex-box";
import UserDashboardHeader from "components/header/UserDashboardHeader";
import CustomerDashboardLayout from "components/layouts/customer-dashboard";
import CustomerDashboardNavigation from "components/layouts/customer-dashboard/Navigations";
import useWindowSize from "hooks/useWindowSize";
import { currency } from "lib";
import { loadUserOrder } from "../../lib/user/order/order";
import { Span } from "../../src/components/Typography";
import { useSettings } from "../../src/store/zSettingsStore";
import { AuthGuard } from "../../src/guards/AuthGuard";

// styled components
const StyledFlexbox = styled(FlexBetween)(({ theme }) => ({
  flexWrap: "wrap",
  marginTop: "2rem",
  marginBottom: "2rem",
  [theme.breakpoints.down("sm")]: {
    flexDirection: "column",
  },
  "& .line": {
    height: 4,
    minWidth: 50,
    flex: "1 1 0",
    [theme.breakpoints.down("sm")]: {
      flex: "unset",
      height: 50,
      minWidth: 4,
    },
  },
}));
// =============================================================

const OrderDetails = ({ order }) => {
  const router = useRouter();
  const width = useWindowSize();
  const orderStatus = "Shipping";
  const orderStatusList = ["Packaging", "Shipping", "Delivering", "Complete"];
  const stepIconList = [PackageBox, TruckFilled, Delivery];
  const breakpoint = 350;
  const statusIndex = orderStatusList.indexOf(orderStatus);
  const { settings } = useSettings();

  // BACK TO ORDERS BUTTON
  const BACK_BUTTON = (
    <Button
      component={motion.button}
      onClick={() => router.push("/orders")}
      variant="text"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      startIcon={
        <Box
          className="flex items-center justify-center"
          component={motion.div}
          initial={{ x: 0 }}
          whileHover={{ x: -3 }}
          transition={{ type: "spring", stiffness: 400, damping: 17 }}
        >
          <ArrowBack />
        </Box>
      }
      sx={{
        px: 2,
        py: 1,
        borderRadius: 2,
        borderColor: "primary.main",
        color: "primary.main",
        fontSize: "14px",
        fontWeight: 600,
        textTransform: "none",
        background: "linear-gradient(45deg, transparent 0%, rgba(25, 118, 210, 0.04) 100%)",
        backdropFilter: "blur(10px)",
        transition: "all 0.2s ease-in-out",
        "&:hover": {
          borderColor: "primary.dark",
          color: "primary.dark",
          backgroundColor: "rgba(25, 118, 210, 0.08)",
          boxShadow: "0 4px 20px rgba(25, 118, 210, 0.15)",
        },
        "&:active": {
          transform: "translateY(1px)",
        },
      }}
    >
      Back to Orders
    </Button>
  );

  // Show a loading state when the fallback is rendered
  if (router.isFallback) {
    return <h1>Loading...</h1>;
  }

  console.log("Order Loaded:", order);

  return (
    <CustomerDashboardLayout
      topbarBgColor={settings?.custrecord_ng_cs_navbar_bckgrnd_color}
    >
      <Head>
        <title>
          {settings?.company?.name || "ConventionSuite Event Services"}
          {" | "}
          Order {order.orderRecord.fields.tranid}
        </title>
      </Head>

      {/* TITLE HEADER AREA */}
      <UserDashboardHeader
        icon={ShoppingBag}
        title="Order Details"
        navigation={<CustomerDashboardNavigation />}
        button={BACK_BUTTON}
      />

      {/* ORDER PROGRESS AREA */}
      {/* <Card */}
      {/*  sx={{ */}
      {/*    p: "2rem 1.5rem", */}
      {/*    mb: "30px", */}
      {/*  }} */}
      {/* > */}
      {/*  <StyledFlexbox> */}
      {/*    {stepIconList.map((Icon, ind) => ( */}
      {/*      <Fragment key={ind}> */}
      {/*        <Box position="relative"> */}
      {/*          <Avatar */}
      {/*            sx={{ */}
      {/*              width: 64, */}
      {/*              height: 64, */}
      {/*              bgcolor: ind <= statusIndex ? "primary.main" : "grey.300", */}
      {/*              color: ind <= statusIndex ? "grey.white" : "primary.main", */}
      {/*            }} */}
      {/*          > */}
      {/*            <Icon */}
      {/*              color="inherit" */}
      {/*              sx={{ */}
      {/*                fontSize: "32px", */}
      {/*              }} */}
      {/*            /> */}
      {/*          </Avatar> */}

      {/*          {ind < statusIndex && ( */}
      {/*            <Box position="absolute" right="0" top="0"> */}
      {/*              <Avatar */}
      {/*                sx={{ */}
      {/*                  width: 22, */}
      {/*                  height: 22, */}
      {/*                  bgcolor: "grey.200", */}
      {/*                  color: "success.main", */}
      {/*                }} */}
      {/*              > */}
      {/*                <Done */}
      {/*                  color="inherit" */}
      {/*                  sx={{ */}
      {/*                    fontSize: "1rem", */}
      {/*                  }} */}
      {/*                /> */}
      {/*              </Avatar> */}
      {/*            </Box> */}
      {/*          )} */}
      {/*        </Box> */}

      {/*        {ind < stepIconList.length - 1 && ( */}
      {/*          <Box */}
      {/*            className="line" */}
      {/*            bgcolor={ind < statusIndex ? "primary.main" : "grey.300"} */}
      {/*          /> */}
      {/*        )} */}
      {/*      </Fragment> */}
      {/*    ))} */}
      {/*  </StyledFlexbox> */}

      {/*  <FlexBox justifyContent={width < breakpoint ? "center" : "flex-end"}> */}
      {/*    <Typography */}
      {/*      p="0.5rem 1rem" */}
      {/*      textAlign="center" */}
      {/*      borderRadius="300px" */}
      {/*      color="primary.main" */}
      {/*      bgcolor="primary.light" */}
      {/*    > */}
      {/*      Estimated Delivery Date <b>4th October</b> */}
      {/*    </Typography> */}
      {/*  </FlexBox> */}
      {/* </Card> */}

      {/* ORDERED PRODUCT LIST */}
      <Card
        sx={{
          p: 0,
          mb: "30px",
        }}
      >
        <TableRow
          sx={{
            p: "12px",
            borderRadius: 0,
            boxShadow: "none",
            bgcolor: "grey.200",
          }}
        >
          <FlexBox className="pre" m={0.75} alignItems="center">
            <Typography fontSize={14} color="grey.600" mr={0.5}>
              Order ID:
            </Typography>

            <Typography fontSize={14}>
              {order.orderRecord.fields.tranid}
            </Typography>
          </FlexBox>

          <FlexBox className="pre" m={0.75} alignItems="center">
            <Typography fontSize={14} color="grey.600" mr={0.5}>
              Placed on:
            </Typography>

            <Typography fontSize={14}>
              {format(
                parse(
                  order.orderRecord.fields.createddate,
                  `${settings.company.preferences.shortDateFormat.toLowerCase().replace("m", "M")} hh:mm a`,
                  new Date(),
                ),
                "dd MMM, yyyy",
              )}
            </Typography>
          </FlexBox>

          <FlexBox className="pre" m={0.75} alignItems="center">
            <Typography fontSize={14} color="grey.600" mr={0.5}>
              Event:
            </Typography>

            <Typography fontSize={14}>
              {order.orderRecord.fields.custbody_jobshow_name || "N/A"}
            </Typography>
          </FlexBox>

          <FlexBox className="pre" m={0.75} alignItems="center">
            <Typography fontSize={14} color="grey.600" mr={0.5}>
              Booth:
            </Typography>

            <Typography fontSize={14}>{order.booth?.name || "N/A"}</Typography>
          </FlexBox>
        </TableRow>

        <Box py={1}>
          {order.items.map((item, ind) => {
            return (
              <Fragment key={ind}>
                <Divider />
                <FlexBox px={2} py={1} flexWrap="wrap" alignItems="center">
                  <FlexBox flex="2 2 260px" m={0.75} alignItems="center">
                    <Avatar
                      src={
                        item?.image ||
                        "/assets/images/products/NO-PRODUCT-IMAGE.jpg"
                      }
                      sx={{
                        height: 64,
                        width: 64,
                      }}
                      variant="rounded"
                    />
                    <Box ml={2.5}>
                      <H6 my="0px">{item.name}</H6>

                      <Typography fontSize="14px" color="grey.600">
                        {currency(item.price)} x {item.quantity}{" "}
                        <Span color="primary.main">
                          <b>{currency(item.amount)}</b>
                        </Span>
                      </Typography>
                    </Box>
                  </FlexBox>

                  <FlexBox flex="1 1 260px" m={0.75} alignItems="center">
                    <Typography fontSize="14px" color="grey.600">
                      {item.description}
                    </Typography>
                  </FlexBox>

                  {/* <FlexBox flex="160px" m={0.75} alignItems="center"> */}
                  {/*  <Button variant="text" color="primary"> */}
                  {/*    <Typography fontSize="14px">Write a Review</Typography> */}
                  {/*  </Button> */}
                  {/* </FlexBox> */}
                </FlexBox>
              </Fragment>
            );
          })}
        </Box>
      </Card>

      {/* SHIPPING AND ORDER SUMMERY */}
      <Grid container spacing={3}>
        <Grid item lg={6} md={6} xs={12}>
          <Card
            sx={{
              p: "20px 30px",
            }}
          >
            <H5 mt={0} mb={2}>
              Shipping Address
            </H5>

            <Paragraph fontSize={14} my={0} className="whitespace-pre-wrap">
              {order.orderRecord.fields?.shippingaddress_text ||
                "No shipping address found"}
            </Paragraph>
          </Card>
        </Grid>

        <Grid item lg={6} md={6} xs={12}>
          <Card
            sx={{
              p: "20px 30px",
            }}
          >
            <H5 mt={0} mb={2}>
              Total Summary
            </H5>
            <FlexBetween mb={1}>
              <Typography fontSize={14} color="grey.600">
                Subtotal:
              </Typography>

              <H6 my="0px">{currency(order.orderRecord.fields.subtotal)}</H6>
            </FlexBetween>
            {order.orderRecord.fields.entity_nexus_country === "CA" ? (
              <>
                <FlexBetween mb={1}>
                  <Typography fontSize={14} color="grey.600">
                    GST/HST:
                  </Typography>

                  <H6 my="0px">
                    {currency(order.orderRecord.fields.taxtotal)}
                  </H6>
                </FlexBetween>
                <FlexBetween mb={1}>
                  <Typography fontSize={14} color="grey.600">
                    PST:
                  </Typography>

                  <H6 my="0px">
                    {currency(order.orderRecord.fields.tax2total)}
                  </H6>
                </FlexBetween>
              </>
            ) : (
              <FlexBetween mb={1}>
                <Typography fontSize={14} color="grey.600">
                  Tax:
                </Typography>

                <H6 my="0px">{currency(order.orderRecord.fields.taxtotal)}</H6>
              </FlexBetween>
            )}
            {/* <FlexBetween mb={1}> */}
            {/*  <Typography fontSize={14} color="grey.600"> */}
            {/*    Discount: */}
            {/*  </Typography> */}
            {/*  <H6 my="0px">{currency(order.discount)}</H6> */}
            {/* </FlexBetween> */}
            <Divider
              sx={{
                mb: 1,
              }}
            />
            <FlexBetween mb={2}>
              <H6 my="0px">Total</H6>
              <H6 my="0px">{currency(order.orderRecord.fields.total)}</H6>
            </FlexBetween>
            <Typography fontSize={14}>
              {order.orderRecord.fields.custbody_isweborder === "T"
                ? "Paid via Web - Credit/Debit Card"
                : "Paid via Rep - Credit/Debit Card"}
            </Typography>
          </Card>
        </Grid>
      </Grid>
    </CustomerDashboardLayout>
  );
};

OrderDetails.getLayout = (page) => <AuthGuard>{page}</AuthGuard>;

export const getServerSideProps = async ({ req, res, params }) => {
  console.debug("cookies", req.cookies);

  const contact = req.cookies?.contact;
  const customer = req.cookies?.customer;

  if (!contact || !customer) {
    return {
      redirect: {
        destination: "/eventSelect",
        permanent: false,
      },
    };
  }

  let order = await loadUserOrder(customer, contact, params.id);

  console.debug("Order Loaded:", order);

  if (!order?.orderRecord?.id) {
    return {
      redirect: {
        destination: "/404",
        permanent: false,
      },
    };
  }

  return {
    props: {
      order,
    },
  };
};
export default OrderDetails;
