<usereventscript scriptid="customscript_ng_cs_ue_email_template">
  <aftersubmitfunction>userEventAfterSubmit</aftersubmitfunction>
  <beforeloadfunction>userEventBeforeLoad</beforeloadfunction>
  <beforesubmitfunction>userEventBeforeSubmit</beforesubmitfunction>
  <description></description>
  <isinactive>F</isinactive>
  <name>NG CS UE Email Template</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/CS UI Script Files/user event/ng_cs_ueEmailTemplate.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_ue_email_template_dep">
      <allemployees>F</allemployees>
      <alllocalizationcontexts>T</alllocalizationcontexts>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <eventtype></eventtype>
      <executioncontext>ACTION|ADVANCEDREVREC|BANKCONNECTIVITY|BANKSTATEMENTPARSER|BUNDLEINSTALLATION|CLIENT|CONSOLRATEADJUSTOR|CSVIMPORT|CUSTOMGLLINES|CUSTOMMASSUPDATE|DATASETBUILDER|DEBUGGER|EMAILCAPTURE|FICONNECTIVITY|FIPARSER|MAPREDUCE|OCRPLUGIN|OTHER|PAYMENTGATEWAY|PAYMENTPOSTBACK|PLATFORMEXTENSION|PORTLET|PROMOTIONS|RECORDACTION|RESTLET|SCHEDULED|SDFINSTALLATION|SHIPPINGPARTNERS|SUITELET|TAXCALCULATION|USEREVENT|USERINTERFACE|WEBAPPLICATION|WEBSERVICES|WORKBOOKBUILDER|WORKFLOW</executioncontext>
      <isdeployed>T</isdeployed>
      <loglevel>AUDIT</loglevel>
      <recordtype>EMAILTEMPLATE</recordtype>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>RELEASED</status>
    </scriptdeployment>
  </scriptdeployments>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
  </libraries>
</usereventscript>