import { NextResponse, type NextRequest } from "next/server"
import request from "request-promise-native"
// import { getServerSession } from "next-auth"

// NetSuite RESTlet URL for booking events
const NETSUITE_EVENTS_URL =
  "https://tstdrv1516212.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_get_bookings&deploy=customdeploy_ng_cses_rl_get_bookings"

// NetSuite OAuth credentials
const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET

export async function GET(req: NextRequest) {
  // TODO: Uncomment when NextAuth is fully configured
  // const session = await getServerSession()
  // if (!session) {
  //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  // }

  const { searchParams } = new URL(req.url)
  const startDate = searchParams.get("start")
  const endDate = searchParams.get("end")
  const venueId = searchParams.get("venueId")

  if (!startDate || !endDate) {
    return NextResponse.json(
      { error: "Missing start or end date" },
      { status: 400 }
    )
  }

  try {
    // Build URL with query parameters
    const url = new URL(NETSUITE_EVENTS_URL)
    url.searchParams.append("startDate", startDate)
    url.searchParams.append("endDate", endDate)
    if (venueId) {
      url.searchParams.append("venueId", venueId)
    }

    console.log(`Fetching bookings from NetSuite: ${url.toString()}`)

    // Check if OAuth credentials are available
    if (!CONSUMER_KEY || !CONSUMER_SECRET || !TOKEN || !TOKEN_SECRET || !REALM) {
      console.warn("NetSuite OAuth credentials not configured, using mock data")
      
      // Fallback to mock data when credentials are not available
    const mockData = {
      ok: true,
      data: {
        events: [],
      },
    }
    return NextResponse.json(mockData)
    }

    // Make authenticated request to NetSuite using request-promise-native
    const response = await request({
      uri: url.toString(),
      method: "GET",
      oauth: {
        consumer_key: CONSUMER_KEY,
        consumer_secret: CONSUMER_SECRET,
        token: TOKEN,
        token_secret: TOKEN_SECRET,
        signature_method: "HMAC-SHA256",
        realm: REALM,
        version: "1.0",
      } as any, // Cast to any to bypass TypeScript error with signature_method
      json: true,
      resolveWithFullResponse: true,
    })

    if (response.statusCode !== 200) {
      throw new Error(`NetSuite API returned status ${response.statusCode}: ${response.body?.message || 'Unknown error'}`)
    }

    // The NetSuite RESTlet should return the data in the expected format
    // If the response is already in the correct format, return it directly
    if (response.body?.ok && response.body?.data) {
      return NextResponse.json(response.body)
    }

    // If the response doesn't have the expected structure, wrap it
    return NextResponse.json({
      ok: true,
      data: {
        events: response.body?.events || [],
      },
    })
  } catch (error) {
    const message = error instanceof Error ? error.message : "Unknown error"
    console.error("API Bookings Error:", message)
    return NextResponse.json(
      { error: "Internal Server Error", details: message },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  // TODO: Uncomment when NextAuth is fully configured
  // const session = await getServerSession()
  // if (!session) {
  //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  // }

  try {
    const body = await req.json() as {
      bookings: Array<{
        tempId: string
        title: string
        start: string
        end: string
        allDay?: boolean
        resourceId: string
        venueId: string
        statusId?: string
        description?: string
        expectedAttendance?: string
        rateTypeId?: string
        rateId?: string
        rate?: number
      }>
      csEventId?: string
      customerId?: string
    }
    
    // Validate request body
    if (!body.bookings || !Array.isArray(body.bookings) || body.bookings.length === 0) {
      return NextResponse.json(
        { error: "Invalid request: bookings array is required" },
        { status: 400 }
      )
    }

    console.log(`Creating ${body.bookings.length} bookings in NetSuite`)

    // Check if OAuth credentials are available
    if (!CONSUMER_KEY || !CONSUMER_SECRET || !TOKEN || !TOKEN_SECRET || !REALM) {
      console.warn("NetSuite OAuth credentials not configured, using mock response")
      
      // Simulate successful creation for development
      const mockResponse = {
        ok: true,
        data: {
          created: body.bookings.map((booking: any, index: number) => ({
            id: String(Date.now() + index),
            title: booking.title,
            tempId: booking.tempId,
          })),
          errors: [],
        },
        message: `Successfully created ${body.bookings.length} bookings`,
      }
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return NextResponse.json(mockResponse)
    }

    // Make authenticated request to NetSuite
    const response = await request({
      uri: NETSUITE_EVENTS_URL,
      method: "POST",
      oauth: {
        consumer_key: CONSUMER_KEY,
        consumer_secret: CONSUMER_SECRET,
        token: TOKEN,
        token_secret: TOKEN_SECRET,
        signature_method: "HMAC-SHA256",
        realm: REALM,
        version: "1.0",
      } as any,
      json: true,
      body: {
        bookings: body.bookings,
        csEventId: body.csEventId,
        customerId: body.customerId,
      },
      resolveWithFullResponse: true,
    })

    if (response.statusCode !== 200) {
      throw new Error(`NetSuite API returned status ${response.statusCode}: ${response.body?.message || 'Unknown error'}`)
    }

    // Return the NetSuite response
    return NextResponse.json(response.body)
    
  } catch (error) {
    const message = error instanceof Error ? error.message : "Unknown error"
    console.error("API Create Bookings Error:", message)
    return NextResponse.json(
      { error: "Failed to create bookings", details: message },
      { status: 500 }
    )
  }
} 

export async function PUT(req: NextRequest) {
  // TODO: Uncomment when NextAuth is fully configured
  // const session = await getServerSession()
  // if (!session) {
  //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  // }

  try {
    const body = await req.json() as {
      bookings: Array<{
        id: string
        title?: string
        start?: string
        end?: string
        allDay?: boolean
        resourceId?: string
        venueId?: string
        statusId?: string
        description?: string
        expectedAttendance?: string
        rateTypeId?: string
        rateId?: string
        rate?: number
      }>
    }
    
    // Validate request body
    if (!body.bookings || !Array.isArray(body.bookings) || body.bookings.length === 0) {
      return NextResponse.json(
        { error: "Invalid request: bookings array is required" },
        { status: 400 }
      )
    }

    // Validate that all bookings have IDs
    const missingIds = body.bookings.filter(b => !b.id)
    if (missingIds.length > 0) {
      return NextResponse.json(
        { error: "Invalid request: all bookings must have an ID" },
        { status: 400 }
      )
    }

    console.log(`Updating ${body.bookings.length} bookings in NetSuite`)

    // Check if OAuth credentials are available
    if (!CONSUMER_KEY || !CONSUMER_SECRET || !TOKEN || !TOKEN_SECRET || !REALM) {
      console.warn("NetSuite OAuth credentials not configured, using mock response")
      
      // Simulate successful update for development
      const mockResponse = {
        ok: true,
        data: {
          updated: body.bookings.map((booking: any) => ({
            id: booking.id,
            title: booking.title || `Booking ${booking.id}`,
          })),
          errors: [],
        },
        message: `Successfully updated ${body.bookings.length} bookings`,
      }
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return NextResponse.json(mockResponse)
    }

    // Make authenticated request to NetSuite
    const response = await request({
      uri: NETSUITE_EVENTS_URL,
      method: "PUT",
      oauth: {
        consumer_key: CONSUMER_KEY,
        consumer_secret: CONSUMER_SECRET,
        token: TOKEN,
        token_secret: TOKEN_SECRET,
        signature_method: "HMAC-SHA256",
        realm: REALM,
        version: "1.0",
      } as any,
      json: true,
      body: {
        bookings: body.bookings,
      },
      resolveWithFullResponse: true,
    })

    if (response.statusCode !== 200) {
      throw new Error(`NetSuite API returned status ${response.statusCode}: ${response.body?.message || 'Unknown error'}`)
    }

    // Return the NetSuite response
    return NextResponse.json(response.body)
    
  } catch (error) {
    const message = error instanceof Error ? error.message : "Unknown error"
    console.error("API Update Bookings Error:", message)
    return NextResponse.json(
      { error: "Failed to update bookings", details: message },
      { status: 500 }
    )
  }
} 