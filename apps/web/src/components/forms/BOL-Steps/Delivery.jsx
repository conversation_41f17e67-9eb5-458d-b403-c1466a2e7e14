import {
  CircularProgress,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import useSWR from "swr";
import { motion } from "motion/react";
import { useUser } from "../../../store/zUserStore";
import { TextMaskCustom } from "../../containers/PhoneMaskedField";
import React, { useCallback, useEffect, useState } from "react";

const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const Delivery = ({ formik }) => {
  const { touched, errors, handleChange, handleBlur, values, setFieldValue } =
    formik;
  const { user } = useUser();

  const [countryAutoCompleteValue, setCountryAutoCompleteValue] = useState({});
  const [stateAutoCompleteValue, setStateAutoCompleteValue] = useState({});
  const [defaultStateSearch, setDefaultStateSearch] = useState(true);
  const [defaultCountrySearch, setDefaultCountrySearch] = useState(true);
  const [stateOptions, setStateOptions] = useState([]);
  const [stateInputOpen, setStateInputOpen] = useState(false);
  const [countryOptions, setCountryOptions] = useState([]);
  const [countryInputOpen, setCountryInputOpen] = useState(false);
  const [countrySelected, setCountrySelected] = useState("");
  const [countrySearchTerm, setCountrySearchTerm] = useState();
  const [stateSearchTerm, setStateSearchTerm] = useState();

  const debouncedCountrySearch = useDebounce(countrySearchTerm, 300);
  const debouncedStateSearch = useDebounce(stateSearchTerm, 300);

  const { data: countriesData, error: countriesError } = useSWR(
    countryInputOpen
      ? `/api/countries${debouncedCountrySearch ? `?searchQuery=${debouncedCountrySearch}` : ""}`
      : null,
  );

  const { data: statesData, error: statesError } = useSWR(
    countrySelected && stateInputOpen
      ? `/api/states/${countrySelected}${debouncedStateSearch ? `?searchQuery=${debouncedStateSearch}` : ""}`
      : null,
  );

  const loadingCountries =
    countryInputOpen && !countriesData && !countriesError;
  const loadingStates = stateInputOpen && !statesData && !statesError;

  //on render, start country api call to get country list
  useEffect(() => {
    setCountrySearchTerm(values.country);
    setStateSearchTerm(values.state);
    setCountryInputOpen(true);
  }, []);

  // Update states options when statesData changes
  useEffect(() => {
    if (statesData?.states) {
      setStateOptions(statesData.states);
      //if default state search on page render
      if (defaultStateSearch) {
        //use value from formik and update autocomplete component
        const defaultValue = statesData.states.find(
          (state) => state.fullname === values.state,
        );
        if (defaultValue) {
          //update autocomplete component
          setStateAutoCompleteValue(defaultValue);
          setDefaultStateSearch(false);
          setStateInputOpen(false);
        }
      }
    }
  }, [statesData]);

  // Update country options when countriesData changes
  useEffect(() => {
    if (countriesData?.countries) {
      setCountryOptions(countriesData.countries);

      //if default country search on page render
      if (defaultCountrySearch) {
        //use value from formik and find matching value from api array
        const defaultValue = countriesData.countries.find(
          (country) => country.name === values.country,
        );
        if (defaultValue) {
          //update autocomplete component and related component state
          handleCountryChangeSelect(defaultValue, setFieldValue);
          setCountryAutoCompleteValue(defaultValue);
          setDefaultCountrySearch(false);
          setCountryInputOpen(false);
          setStateInputOpen(true);
        }
      }
    }
  }, [countriesData]);

  useEffect(() => {
    if (!stateInputOpen) {
      setStateOptions([]);
      setStateSearchTerm("");
    }
  }, [stateInputOpen]);

  useEffect(() => {
    if (!countryInputOpen) {
      setCountryOptions([]);
      setCountrySearchTerm("");
    }
  }, [countryInputOpen]);

  const handleCountryChangeSelect = useCallback(
    (value, setFieldValue) => {
      const countryId = value?.uniquekey || "";
      setCountrySelected(countryId);
      setFieldValue("country", value?.name || "");
      setCountryAutoCompleteValue(value);

      //only reset state search state after inital api calls on page render
      if (!defaultCountrySearch) {
        setFieldValue("state", "");
        setStateSearchTerm("");
        setStateAutoCompleteValue({});
      }
    },
    [defaultCountrySearch],
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Grid container spacing={3}>
        <Grid item md={6} xs={12}>
          <FormControl
            fullWidth
            error={Boolean(touched.company && errors.company)}
          >
            <InputLabel id="delivery-company-select-label">Company</InputLabel>
            <Select
              disabled
              labelId="delivery-company-select-label"
              label="Company"
              name="company"
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.company}
              sx={{ height: 56, display: "flex", alignItems: "center" }}
            >
              <MenuItem value={user.id} selected>
                {user.name}
              </MenuItem>
            </Select>
            <FormHelperText>{touched.company && errors.company}</FormHelperText>
          </FormControl>
        </Grid>
        <Grid item md={6} xs={12}>
          <FormControl
            fullWidth
            error={Boolean(touched.contact && errors.contact)}
          >
            <InputLabel id="contact-select-label">Contact</InputLabel>
            <Select
              disabled
              labelId="contact-select-label"
              label="Contact"
              name="contact"
              onBlur={handleBlur}
              onChange={handleChange}
              value={values.contact}
            >
              <MenuItem value={user.contact} selected>
                {user.profile?.contactProfile.name}
              </MenuItem>
            </Select>
            <FormHelperText>{touched.contact && errors.contact}</FormHelperText>
          </FormControl>
        </Grid>
        <Grid item md={6} xs={12}>
          <FormControl
            error={Boolean(touched.contactPhone && errors.contactPhone)}
            fullWidth
            variant="outlined"
          >
            <InputLabel
              variant="outlined"
              htmlFor="formatted-text-exhibit-contact-number"
            >
              Contact Number
            </InputLabel>
            <OutlinedInput
              label="Contact Number"
              variant="outlined"
              value={values.contactPhone}
              onChange={handleChange}
              onBlur={handleBlur}
              name="contactPhone"
              id="formatted-text-exhibit-contact-number"
              inputComponent={TextMaskCustom}
            />
            <FormHelperText>
              {touched.contactPhone && errors.contactPhone}
            </FormHelperText>
          </FormControl>
        </Grid>
        <Grid item md={6} xs={12}>
          <Autocomplete
            fullWidth
            value={countryAutoCompleteValue}
            name="country"
            key="country-ac"
            isOptionEqualToValue={(option, value) => option.name === value.name}
            open={countryInputOpen}
            loading={loadingCountries}
            onOpen={() => {
              setCountryInputOpen(true);
            }}
            onClose={() => {
              setCountryInputOpen(false);
            }}
            options={countryOptions}
            getOptionLabel={(option) => option?.name || ""}
            onChange={(_, value) => {
              handleCountryChangeSelect(value, setFieldValue);
            }}
            onInputChange={(_, newInputValue) => {
              setCountrySearchTerm(newInputValue);
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                name="country"
                label="Country/region"
                onBlur={handleBlur}
                error={Boolean(touched.country && errors.country)}
                helperText={touched.country && errors.country}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {loadingCountries ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <Autocomplete
            freeSolo
            fullWidth
            name="state"
            value={stateAutoCompleteValue}
            isOptionEqualToValue={(option, value) =>
              option.fullname === value.fullname
            }
            key="state-ac"
            open={stateInputOpen}
            loading={loadingStates}
            onOpen={() => {
              setStateInputOpen(true);
            }}
            onClose={() => {
              setStateInputOpen(false);
            }}
            options={stateOptions}
            getOptionLabel={(option) => {
              // Handle both object options and string inputs
              return typeof option === "string"
                ? option
                : option?.fullname || "";
            }}
            onChange={(_, value) => {
              // Handle both object selections and free text input
              if (typeof value === "string") {
                setFieldValue("state", value);
              } else {
                setFieldValue("state", value?.fullname || "");
                setStateAutoCompleteValue(value);
              }
            }}
            onInputChange={(_, newInputValue) => {
              setStateSearchTerm(newInputValue);
              // In freeSolo mode, also update the field value as the user types
              if (!stateOptions.length) {
                setFieldValue("state", newInputValue);
              }
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                name="state"
                label="State/Province"
                error={Boolean(touched.state && errors.state)}
                onBlur={handleBlur}
                helperText={
                  (touched.state && errors.state) ||
                  "Enter state or province, even if not in the list"
                }
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {loadingStates ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <TextField
            fullWidth
            label="Address"
            name="address"
            onChange={handleChange}
            onBlur={handleBlur}
            error={Boolean(touched.address && errors.address)}
            helperText={touched.address && errors.address}
            value={values.address}
            InputProps={{
              sx: { height: 56 },
            }}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <TextField
            fullWidth
            label="City/Region"
            name="city"
            onChange={handleChange}
            onBlur={handleBlur}
            error={Boolean(touched.city && errors.city)}
            helperText={touched.city && errors.city}
            value={values.city}
            InputProps={{
              sx: { height: 56 },
            }}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <TextField
            fullWidth
            label="Postal Code"
            name="zipCode"
            onChange={handleChange}
            onBlur={handleBlur}
            error={Boolean(touched.zipCode && errors.zipCode)}
            helperText={touched.zipCode && errors.zipCode}
            value={values.zipCode}
            InputProps={{
              sx: { height: 56 },
            }}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <TextField
            fullWidth
            label="Destination Event"
            name="destinationEvent"
            onChange={handleChange}
            onBlur={handleBlur}
            error={Boolean(touched.destinationEvent && errors.destinationEvent)}
            helperText={touched.destinationEvent && errors.destinationEvent}
            value={values.destinationEvent}
            InputProps={{
              sx: { height: 56 },
            }}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <TextField
            fullWidth
            label="Destination Exhibitor & Booth"
            name="destinationExhibitorBooth"
            onChange={handleChange}
            onBlur={handleBlur}
            error={Boolean(
              touched.destinationExhibitorBooth &&
                errors.destinationExhibitorBooth,
            )}
            helperText={
              touched.destinationExhibitorBooth &&
              errors.destinationExhibitorBooth
            }
            value={values.destinationExhibitorBooth}
            InputProps={{
              sx: { height: 56 },
            }}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <TextField
            fullWidth
            label="Destination C/O"
            name="destinationCo"
            onChange={handleChange}
            onBlur={handleBlur}
            error={Boolean(touched.destinationCo && errors.destinationCo)}
            helperText={touched.destinationCo && errors.destinationCo}
            value={values.destinationCo}
            InputProps={{
              sx: { height: 56 },
            }}
          />
        </Grid>
        <Grid item />
      </Grid>
    </motion.div>
  );
};
