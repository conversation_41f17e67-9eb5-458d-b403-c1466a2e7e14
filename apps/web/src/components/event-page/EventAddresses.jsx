import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON> } from "next/font/google";
import tinycolor from "tinycolor2";
import { FileCopyOutlined } from "@mui/icons-material";

const roboto = Roboto({
  weight: ["100", "300", "400", "500", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

const EventAddresses = ({
  settings,
  siteAddressCopied,
  data,
  warehouseAddressCopied,
  copyText,
}) => (
  <Container style={{ paddingTop: "3vh" }}>
    <Card>
      <CardHeader
        sx={{
          fontFamily: roboto.style.fontFamily,
          lineWidth: "0.1rem",
          fontWeight: "bold",
          backgroundColor: settings?.custrecord_ng_cs_accent_color
            ? settings.custrecord_ng_cs_accent_color
            : "white",
          color: settings?.custrecord_ng_cs_accent_color
            ? tinycolor(settings.custrecord_ng_cs_accent_color).isDark()
              ? "white"
              : "#343434"
            : "#343434",
        }}
        title={
          <Typography
            variant="caption"
            sx={{
              color: settings?.custrecord_ng_cs_accent_color
                ? tinycolor(settings.custrecord_ng_cs_accent_color).isDark()
                  ? "white"
                  : "#343434"
                : "#343434",
            }}
          >
            Site Address
          </Typography>
        }
        action={
          <Tooltip
            title={siteAddressCopied ? "Address Copied" : "Copy Address"}
            color={siteAddressCopied ? "success" : "primary"}
            placement="left"
            arrow
          >
            <IconButton
              size="small"
              onClick={() => copyText("site-address", "site")}
              style={{
                fontSize: "4pt",
                float: "right",
                color: settings?.custrecord_ng_cs_accent_color
                  ? tinycolor(settings.custrecord_ng_cs_accent_color).isDark()
                    ? "white"
                    : "#343434"
                  : "#343434",
              }}
            >
              <FileCopyOutlined style={{ fontSize: "15pt" }} />
            </IconButton>
          </Tooltip>
        }
      />
      <CardContent>
        <Typography className="text-center whitespace-pre-wrap">
          <p id="site-address">{data.details.address.facility}</p>
        </Typography>
      </CardContent>
    </Card>
    <br />
    <Card>
      <CardHeader
        sx={{
          lineWidth: "0.1rem",
          fontWeight: "bold",
          fontFamily: roboto.style.fontFamily,
          color: settings?.custrecord_ng_cs_accent_color
            ? tinycolor(settings.custrecord_ng_cs_accent_color).isDark()
              ? "white"
              : "#343434"
            : "#343434",
          backgroundColor: settings?.custrecord_ng_cs_accent_color
            ? settings.custrecord_ng_cs_accent_color
            : "white",
        }}
        title={
          <Typography
            variant="caption"
            sx={{
              color: settings?.custrecord_ng_cs_accent_color
                ? tinycolor(settings.custrecord_ng_cs_accent_color).isDark()
                  ? "white"
                  : "#343434"
                : "#343434",
            }}
          >
            Advanced Warehouse Address
          </Typography>
        }
        action={
          <Tooltip
            title={warehouseAddressCopied ? "Address Copied" : "Copy Address"}
            color={warehouseAddressCopied ? "success" : "primary"}
            placement="left"
            arrow
          >
            <IconButton
              size="small"
              onClick={() => copyText("warehouse-address", "warehouse")}
              style={{
                fontSize: "4pt",
                float: "right",
                color: settings?.custrecord_ng_cs_accent_color
                  ? tinycolor(settings.custrecord_ng_cs_accent_color).isDark()
                    ? "white"
                    : "#343434"
                  : "#343434",
              }}
            >
              <FileCopyOutlined style={{ fontSize: "15pt" }} />
            </IconButton>
          </Tooltip>
        }
      />
      <CardContent>
        <Typography className="text-center whitespace-pre-wrap">
          <p id="warehouse-address" mb={0}>
            {data.details.address.warehouse}
          </p>
        </Typography>
      </CardContent>
    </Card>
  </Container>
);

export default EventAddresses; 