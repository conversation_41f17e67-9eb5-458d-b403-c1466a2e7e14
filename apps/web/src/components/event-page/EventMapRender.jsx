import React from "react";
import { Container, Paper, Skeleton } from "@mui/material";
import MaterialMapBox from "components/containers/MaterialMapBox";

const parse_address = (add) => {
  let address = add;
  let first_number_occurrence = address.search(/[0-9]/g);
  console.log("Address parsed", address.slice(first_number_occurrence));
  return address.slice(first_number_occurrence);
};

const EventMap = ({ data }) => {
  if (data && data?.venue && data?.details) {
    return (
      <MaterialMapBox
        zoom={4.5}
        warehouseAddress={parse_address(data.details.address.facility)}
        siteAddress={parse_address(
          data.venue.values.custrecord_facility_fulladdress,
        )}
      />
    );
  }
  return <Skeleton variant="rect" width="100%" height="100%" />;
};

const EventMapRender = ({ data }) => (
  <Container style={{ paddingTop: "3vh" }}>
    <Paper sx={{ height: "35em", width: "100%" }}>
      <EventMap data={data} />
    </Paper>
  </Container>
);

export default EventMapRender; 