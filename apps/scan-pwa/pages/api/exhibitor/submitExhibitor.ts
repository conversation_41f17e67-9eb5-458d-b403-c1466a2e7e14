import type { RequestPromiseOptions } from 'request-promise-native'

import request from 'request-promise-native'

export default async (req: any, res: any) => {
	let body = req.body
	let sharedEnv;

	const reqHeaders = req?.headers['x-shared-env']
	console.log('theHeaders', reqHeaders + ' ' + typeof reqHeaders)

	let REALM = '';
	let CONSUMER_KEY = '';
	let CONSUMER_SECRET = '';
	let TOKEN = '';
	let TOKEN_SECRET = '';

	if (reqHeaders) {
		sharedEnv = JSON.parse(reqHeaders)

		REALM = sharedEnv.NEXT_PUBLIC_ACCOUNT_ID as string
		CONSUMER_KEY = sharedEnv.OAUTH1_CONSUMER_KEY as string
		CONSUMER_SECRET = sharedEnv.OAUTH1_CONSUMER_SECRET as string
		TOKEN = sharedEnv.OAUTH1_ACCESS_TOKEN as string
		TOKEN_SECRET = sharedEnv.OAUTH1_TOKEN_SECRET as string
	}

	interface NetSuiteRequest extends RequestPromiseOptions {
		method: string
		uri: string
		oauth: RequestPromiseOptions['oauth'] & {
			realm: string | undefined
			version: string
			signature_method: string
		}
		body: any
	}

	const requestOptions: NetSuiteRequest = {
		method: 'POST',
		uri: `https://${sharedEnv.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_rl_handle_cs_scan&deploy=customdeploy_rcs_rl_handle_cs_scan`,
		oauth: {
			consumer_key: CONSUMER_KEY,
			consumer_secret: CONSUMER_SECRET,
			token: TOKEN,
			token_secret: TOKEN_SECRET,
			signature_method: 'HMAC-SHA256',
			realm: REALM,
			version: '1.0',
		},
		body: body,
		resolveWithFullResponse: true,
		json: true, // Automatically parses the response as JSON
	}

	try {
		const response = await request(requestOptions)

		const data = await response.body
		if (response.statusCode !== 200) {
			return res.status(response.statusCode).send({
				error: `Error processing product upload request:`,
				data,
			})
		}

		if (data?.error) {
			return res.status(500).send(data)
		}

		return res.status(response.statusCode).json(data)
	} catch (error) {
		console.error(error)
		return res
			.status(500)
			.json({ error: 'Failed to forward shipment to endpoint' })
	}
}
