# API Migration Guide: request-promise-native to Optimized axios

## Overview
This guide helps you systematically migrate all remaining API endpoints from `request-promise-native` to the optimized axios pattern using our shared NetSuite API utility.

## Files Already Migrated ✅
- `/api/customer/event/[eid].js` (Reference implementation)
- `/api/collection/items.js`
- `/api/customer/account.js`
- `/api/customer/shows.js`
- `/api/customer/post/address.js`
- `/api/customer/put/address.js`
- `/api/product/[pid]/upload.js`

## Files Remaining to Migrate 🔄

### High Priority (Frequently Used)
1. `/api/collection/[cid].js`
2. `/api/customer/cart/[ceid].js`
3. `/api/customer/cart/item_check.js`
4. `/api/product/search.js`
5. `/api/product/search/[slug].js`
6. `/api/product/[pid]/index.js`
7. `/api/customer/settings.js`

### Medium Priority
8. `/api/customer/post/order.js`
9. `/api/customer/post/card.js`
10. `/api/customer/payment/[slug].js`
11. `/api/customer/tracking.js`
12. `/api/customer/order.js`
13. `/api/customer/pay/paytracekey.js`

### Lower Priority
14. `/api/countries.js`
15. `/api/states/[id].js`
16. `/api/users/[id].js`
17. `/api/users/id-list.js`
18. `/api/customer/delete/address/[id].js`
19. `/api/customer/delete/card/[id].js`
20. `/api/customer/delete/delete-bill-of-lading.js`
21. `/api/customer/post/bill-of-lading.js`
22. `/api/customer/post/forgot-password.js`
23. `/api/customer/post/register.js`
24. `/api/customer/put/cancel-bill-of-lading.js`
25. `/api/customer/put/password.js`
26. `/api/customer/put/update-bill-of-lading.js`

## Migration Pattern

### 1. Import Changes
**Before:**
```javascript
import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID;
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY;
// ... other constants
```

**After:**
```javascript
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import { makeNetSuiteRequest, buildNetSuiteUrl, handleApiError, setOptimizedHeaders } from "../../../lib/netsuite-api";
```

### 2. Function Declaration
**Before:**
```javascript
export default async (req, res) => {
```

**After:**
```javascript
export default async function handler(req, res) {
  const requestStart = Date.now();
```

### 3. Authentication Check
**Before:**
```javascript
if (session) {
  // ... rest of logic
} else {
  res.status(401).send("You must be sign in...");
}
```

**After:**
```javascript
try {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({
      error: "You must be signed in to view the protected content on this page.",
    });
  }
  // ... rest of logic
} catch (error) {
  return handleApiError(error, res, requestStart);
}
```

### 4. HTTP Method Validation
**Add early method validation:**
```javascript
// For single-method endpoints
if (req.method !== "GET") {
  return res.status(405).json({ error: "Method not allowed" });
}

// For multi-method endpoints, use switch statement
switch (req.method) {
  case "GET": {
    // GET logic
    break;
  }
  case "POST": {
    // POST logic
    break;
  }
  default:
    return res.status(405).json({ error: "Method not allowed" });
}
```

### 5. Parameter Validation
**Add parameter validation:**
```javascript
// Validate required parameters
if (!requiredParam) {
  return res.status(400).json({ error: "Required parameter is missing" });
}
```

### 6. NetSuite Request
**Before:**
```javascript
const currRequest = {
  url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=SCRIPT_ID&deploy=DEPLOY_ID&param=${value}`,
  method: "GET",
};

const response = await request({
  uri: currRequest.url,
  oauth: {
    consumer_key: CONSUMER_KEY,
    consumer_secret: CONSUMER_SECRET,
    token: TOKEN,
    token_secret: TOKEN_SECRET,
    signature_method: "HMAC-SHA256",
    realm: REALM,
    version: "1.0",
  },
  resolveWithFullResponse: true,
  json: true,
});

const data = await response.body;
```

**After:**
```javascript
// Build NetSuite URL
const url = buildNetSuiteUrl(
  "SCRIPT_ID",
  "DEPLOY_ID",
  { param: value }
);

// Make request to NetSuite
const { data, duration } = await makeNetSuiteRequest({
  url,
  method: "GET",
  body: req.body, // Only for POST/PUT/PATCH
});
```

### 7. Response Headers and Return
**Add performance headers and return:**
```javascript
// Set optimized response headers (for GET requests)
setOptimizedHeaders(res);

// Add performance headers
res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
res.setHeader("X-NetSuite-Time", `${duration}ms`);

return res.status(200).json(data);
```

### 8. Export Config (Optional)
**Add for GET/DELETE endpoints that don't need body parsing:**
```javascript
export const config = {
  api: {
    bodyParser: false,
  },
};
```

## Special Cases

### File Upload Endpoints
For endpoints handling file uploads (like `/api/product/[pid]/upload.js`):
- Keep `bodyParser: false` in config
- Use Promise wrapper for form parsing
- Process files before sending to NetSuite

### Long-Running Operations
For endpoints like `/api/customer/post/order.js` with custom timeout handling:
- Keep the timeout wrapper logic
- Integrate the optimized request within the existing pattern
- Maintain the recovery URL functionality

### Non-Standard Response Handling
Some endpoints have special response handling:
- `/api/customer/pay/paytracekey.js` returns raw key instead of JSON
- `/api/customer/post/forgot-password.js` has complex form handling
- Preserve existing business logic while applying performance optimizations

## Testing Checklist
For each migrated endpoint:
- [ ] Authentication works correctly
- [ ] All HTTP methods work as expected
- [ ] Parameter validation catches missing/invalid inputs
- [ ] NetSuite responses are handled correctly
- [ ] Performance headers are present
- [ ] Error handling works (test with invalid data)
- [ ] Response format matches original (JSON vs text)
- [ ] Cache headers are appropriate for the endpoint

## Performance Verification
After migration, check browser DevTools Network tab for:
- [ ] `X-Response-Time` header present
- [ ] `X-NetSuite-Time` header present
- [ ] Response times improved (typical 30-50% reduction)
- [ ] Cache headers present on appropriate endpoints
- [ ] No hanging requests (max 15s timeout)

## Migration Priority Order
1. Start with **High Priority** endpoints (most frequently used)
2. Test each endpoint thoroughly after migration
3. Move to **Medium Priority** endpoints
4. Complete **Lower Priority** endpoints
5. Update `EVENT_API_OPTIMIZATION_GUIDE.md` with final results

## Common Pitfalls to Avoid
1. Don't forget to update import paths (relative to file location)
2. Preserve existing business logic while applying performance patterns
3. Test all HTTP methods supported by each endpoint
4. Maintain backward compatibility for response formats
5. Don't remove custom error handling that provides business value
6. Remember to handle both application-level and HTTP-level errors

## Quick Migration Script
You can use find/replace to speed up the basic structure:

```bash
# Find files still using request-promise-native
grep -r "request-promise-native" apps/web/pages/api/

# Replace common patterns (use with caution, review each change)
# 1. Import statement
# 2. Function declaration
# 3. Authentication pattern
```

Remember: Always test each endpoint after migration to ensure functionality is preserved! 