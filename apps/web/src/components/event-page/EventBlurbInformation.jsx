import React, { memo } from "react";
import { Box, Card, CardContent, Typography } from "@mui/material";
import { Montserrat } from "next/font/google";
import <PERSON><PERSON> from "react-lottie";
import ShowInfoAccordion from "components/containers/ShowInfoAccordion";
import * as animationData from "../../../public/assets/lottie/l-empty-file.json";

const montserrat = Montserrat({
  weight: ["100", "200", "300", "400", "500", "600", "800", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

const defaultLottieOptions = {
  loop: true,
  autoplay: true,
  animationData,
  rendererSettings: {
    backgroundColor: "transparent",
  },
};

const EventBlurbInformation = memo(({ mobile, data }) => (
  <>
    {data?.blurbs && data.blurbs.length !== 0 ? (
      <ShowInfoAccordion mobile={mobile} blurbs={data.blurbs} />
    ) : (
      <Card
        className="bg-transparent"
        style={{
          marginTop: "2.25rem",
          borderRadius: "0.25rem",
          borderStyle: "dashed",
          borderWidth: "2px",
          borderColor: "rgba(0, 0, 0, 0.25)",
          fontFamily: montserrat.style.fontFamily,
        }}
      >
        <CardContent>
          <Box
            display="flex"
            flexDirection="row"
            justifyContent="center"
            className="text-center"
          >
            <Typography tag="h6">
              NO WEB BLURBS ARE ATTACHED TO THIS EVENT
            </Typography>
          </Box>
          <Box display="flex" flexDirection="row">
            <Lottie
              height="18rem"
              width="18rem"
              options={defaultLottieOptions}
            />
          </Box>
          <Box
            display="flex"
            flexDirection="row"
            justifyContent="center"
            className="text-center"
          >
            <Typography>
              Begin adding to <b>&quot;Web Blurbs&quot;</b> to see information
              about this event.
            </Typography>
          </Box>
        </CardContent>
      </Card>
    )}
  </>
));

EventBlurbInformation.displayName = "EventBlurbInformation";

export default EventBlurbInformation; 