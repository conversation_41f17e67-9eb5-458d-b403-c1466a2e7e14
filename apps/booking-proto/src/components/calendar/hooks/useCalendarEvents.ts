"use client"

import { useCallback, useRef } from "react"
import type { DateSelectArg, Event<PERSON><PERSON><PERSON>rg, EventChangeArg } from "@fullcalendar/core"
import { useCalendar } from "src/context/calendar-context"
import { useBookingCart } from "src/store/booking-cart"
import { useCalendarPopover } from "../stores/popover-store"
import { v4 as uuidv4 } from "uuid"
import { showToast, showToastDebounced } from "src/components/toast-manager"
import { usePricingRates, applyDefaultRateToBooking } from "src/hooks/use-booking-settings"

// Define interfaces for FullCalendar events with DOM elements
interface FullCalendarEventWithEl {
  el: HTMLElement
  [key: string]: any
}

// Helper function to ensure valid date objects
const ensureValidDate = (date: any): Date => {
  // Check if it's already a valid Date
  if (date instanceof Date && !isNaN(date.getTime())) {
    return date
  }

  // Try to convert from string or number
  if (typeof date === "string" || typeof date === "number") {
    const newDate = new Date(date)
    if (!isNaN(newDate.getTime())) {
      return newDate
    }
  }

  // Return current time as fallback
  console.warn("Invalid date provided, using current time as fallback:", date)
  return new Date()
}

/**
 * Custom hook that encapsulates all event handling logic for the calendar
 *
 * @param setModalOpen - Function to open the booking modal
 * @param setSelectedBooking - Function to set the selected booking for the modal
 * @param setSelectedDates - Function to set the selected dates for the modal
 * @param setSelectedRoomId - Function to set the selected room for the modal
 * @param setSelectedVenueId - Function to set the selected venue for the modal
 * @param isCartMode - Whether cart mode is enabled
 * @returns Object containing all event handler functions
 */
export const useCalendarEvents = (
  onDateSelect: (arg: DateSelectArg) => void,
  onEventClick: (arg: EventClickArg) => void,
  isCartMode: boolean
) => {
  const {
    filteredBookings,
    ghostBookings,
    rooms,
    updateBooking,
    deleteBooking,
    updateGhostBooking,
    convertGhostBookingToReal,
    addGhostBooking,
  } = useCalendar()

  const { addItem, updateItem } = useBookingCart()
  const { rates, rateTypes } = usePricingRates()

  // Get the popover control functions
  const { openPopover, closePopover } = useCalendarPopover()

  // Add a ref to prevent immediate updates when the modal opens
  const isUpdatingRef = useRef(false)

  /**
   * Handle date selection (for creating new events)
   */
  const handleDateSelect = useCallback(
    (selectInfo: DateSelectArg) => {
      // Ensure we have valid Date objects
      if (
        !selectInfo.start ||
        !selectInfo.end ||
        !(selectInfo.start instanceof Date) ||
        !(selectInfo.end instanceof Date)
      ) {
        console.error("Invalid date objects in selection", selectInfo)
        return
      }

      // Validate dates and create safe copies
      const safeStart = ensureValidDate(selectInfo.start)
      const safeEnd = ensureValidDate(selectInfo.end)

      // Check if the selection is on a resource
      const resource = selectInfo.resource

      if (resource) {
        // Get the room ID and venue ID from the resource
        const roomId = resource.id
        const venueId = resource.extendedProps?.venueId
        const room = rooms.find((r) => r.id === roomId)

        if (isCartMode && room) {
          // In cart mode, create a temporary booking and add it to the cart
          const cartItemId = uuidv4()
          const baseBooking = {
            id: cartItemId,
            title: `New Booking - ${room.name}`,
            start: safeStart,
            end: safeEnd,
            venueId: venueId || room.venueId,
            roomId: roomId,
            description: "",
            isAllDay: selectInfo.allDay,
          }

          // Apply default rate information
          const newBooking = applyDefaultRateToBooking(baseBooking, roomId, rates, rateTypes)

          // Add to cart
          addItem({
            id: cartItemId,
            booking: newBooking,
            room,
            addedAt: new Date(),
          })

          // IMPORTANT: Create a ghost booking for the calendar display
          const ghostId = `ghost-cart-${cartItemId}`
          const ghostBooking = {
            id: ghostId,
            title: `Cart: ${newBooking.title}`,
            start: safeStart,
            end: safeEnd,
            venueId: venueId || room.venueId,
            roomId: roomId,
            description: "",
            isAllDay: selectInfo.allDay || false,
            status: "pending" as const,
          }

          // Add the ghost booking to the calendar directly
          addGhostBooking(ghostBooking)

          // Show visual feedback
          showToast(`Added to cart: New booking for ${room.name}`)

          // Open the cart briefly to show the new item
          const { isOpen, setCartOpen } = useBookingCart.getState()
          if (!isOpen) {
            // Open the cart
            setCartOpen(true)

            // Auto-close after 3 seconds
            setTimeout(() => {
              setCartOpen(false)
            }, 3000)
          }

          // Add a visual highlight to the newly created ghost event
          setTimeout(() => {
            // Find the newly created ghost event
            const ghostEl = document.querySelector(`[data-event-id="${ghostId}"]`) as HTMLElement

            if (ghostEl) {
              // Add highlight effect
              ghostEl.classList.add("cart-update-highlight")
              ghostEl.style.border = "2px solid #22c55e"

              // Create and add a "New" badge
              const newBadge = document.createElement("div")
              newBadge.textContent = "New"
              newBadge.style.position = "absolute"
              newBadge.style.top = "-8px"
              newBadge.style.right = "8px"
              newBadge.style.backgroundColor = "#22c55e"
              newBadge.style.color = "white"
              newBadge.style.padding = "2px 6px"
              newBadge.style.borderRadius = "9999px"
              newBadge.style.fontSize = "10px"
              newBadge.style.fontWeight = "bold"
              newBadge.style.zIndex = "10"
              newBadge.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.1)"

              // Add the badge to the event
              ghostEl.appendChild(newBadge)

              // Remove the highlight after 2 seconds
              setTimeout(() => {
                ghostEl.classList.remove("cart-update-highlight")
                ghostEl.style.border = ""

                // Remove the badge with a fade out effect
                newBadge.style.transition = "opacity 0.5s ease-out"
                newBadge.style.opacity = "0"

                // Remove the badge from the DOM after the transition
                setTimeout(() => {
                  if (ghostEl && ghostEl.contains(newBadge)) {
                    ghostEl.removeChild(newBadge)
                  }
                }, 500)
              }, 2000)
            }
          }, 100) // Small delay to ensure the ghost event is rendered
        } else {
          onDateSelect(selectInfo)
        }
      } else {
        // For non-resource views (day, week, month), just open the modal
        // The user will need to select a room in the modal
        onDateSelect(selectInfo)
      }

      // Clear selection
      if (selectInfo.view.calendar) {
        selectInfo.view.calendar.unselect()
      }
    },
    [
      rooms,
      isCartMode,
      addItem,
      onDateSelect,
      addGhostBooking,
    ]
  )

  /**
   * Handle event click
   */
  const handleEventClick = useCallback(
    (clickInfo: EventClickArg) => {
      onEventClick(clickInfo)
    },
    [onEventClick]
  )

  /**
   * Handle event changes (drag, resize)
   */
  const handleEventChange = (changeInfo: EventChangeArg) => {
    console.group(`--- handleEventChange --- [Action: ${changeInfo.oldEvent ? 'DRAG' : 'RESIZE'}]`);
    console.log('Event Change Info:', { 
      event: {
        id: changeInfo.event.id,
        title: changeInfo.event.title,
        start: changeInfo.event.start?.toISOString(),
        end: changeInfo.event.end?.toISOString(),
        allDay: changeInfo.event.allDay,
        extendedProps: changeInfo.event.extendedProps
      },
      oldEvent: {
        id: changeInfo.oldEvent?.id,
        start: changeInfo.oldEvent?.start?.toISOString(),
        end: changeInfo.oldEvent?.end?.toISOString(),
      }
    });

    const isGhost = changeInfo.event.extendedProps.isGhost;
    const isCartGhost = changeInfo.event.extendedProps.isCartGhost;
    console.log(`Event Type: isGhost=${isGhost}, isCartGhost=${isCartGhost}`);

    if (isCartGhost) {
        console.group('--- Updating Cart Item ---');
        const ghostId = changeInfo.event.id;
        const cartItemId = ghostId.replace("ghost-cart-", "");
        console.log(`Syncing Ghost ID: ${ghostId} with Cart Item ID: ${cartItemId}`);

        const { items, updateItem: updateCartItem } = useBookingCart.getState();
        const cartItem = items.find((item) => item.id === cartItemId);

        if (cartItem) {
            console.log('Found Cart Item:', { ...cartItem });
            const resources = changeInfo.event.getResources();
            const resourceId = resources?.length > 0 ? resources[0].id : cartItem.booking.roomId;
            const room = rooms.find((r) => r.id === resourceId);

            if (room) {
                const eventStart = changeInfo.event.start;
                const eventEnd = changeInfo.event.end;

                const updatedCartItem = {
                    ...cartItem,
                    booking: {
                        ...cartItem.booking,
                        start: new Date(eventStart),
                        end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                        roomId: resourceId,
                        venueId: room.venueId || cartItem.booking.venueId,
                    },
                    room: room,
                };

                console.log('Dispatching update to cart store:', updatedCartItem);
                updateCartItem(cartItemId, updatedCartItem);
                
                showToast(`Cart updated: ${room.name}`, { immediate: true });
            } else {
                 console.error('CRITICAL: Could not find room for resourceId:', resourceId);
            }
        } else {
            console.error('CRITICAL: Could not find cart item to sync for ID:', cartItemId);
        }
        console.groupEnd();
    } else {
        console.log('Standard event update (not a cart item).');
        const booking = filteredBookings.find((b) => b.id === changeInfo.event.id);
        if (booking) {
            const resources = changeInfo.event.getResources();
            const resourceId = resources.length > 0 ? resources[0].id : booking.roomId;
            const room = rooms.find((r) => r.id === resourceId);

            if(room) {
                const updatedBooking = {
                    ...booking,
                    start: new Date(changeInfo.event.start),
                    end: changeInfo.event.end ? new Date(changeInfo.event.end) : new Date(changeInfo.event.start.getTime() + 3600000),
                    roomId: resourceId,
                    venueId: room.venueId || booking.venueId,
                };
                console.log('Updating regular booking:', updatedBooking);
                updateBooking(updatedBooking);
            }
        }
    }
    console.groupEnd();
  };

  /**
   * Handles events being mounted in the DOM
   */
  const eventDidMountHandler = useCallback(
    (info: any) => {
      const { event, el } = info
      const { roomId, venueId, isGhost, isCartGhost, status, holdRank } = event.extendedProps

      // Add data-event-id attribute
      el.setAttribute("data-event-id", event.id)

      // Add status as a data attribute for styling purposes
      if (status) {
        el.setAttribute("data-status", status)
        el.classList.add(`event-status-${status.toLowerCase().replace(" ", "-")}`)
      }

      const room = rooms.find((r) => r.id === roomId)

      if (room) {
        el.style.borderLeft = `5px solid ${room.color}`
      }

      // Apply styling for ghost events
      if (isGhost || isCartGhost) {
        el.style.opacity = "0.7"
        el.style.backgroundColor = "#f0f4f8"

        if (isCartGhost) {
          el.classList.add("cart-ghost-event")
          // Add visual indication that this is in the cart
          const cartBadge = document.createElement("div")
          cartBadge.className = "cart-badge"
          cartBadge.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="8" cy="21" r="1"/><circle cx="19" cy="21" r="1"/><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/></svg>`
          el.appendChild(cartBadge)
        }

        if (status === "Hold") {
          const holdBadge = document.createElement("div")
          holdBadge.className = "hold-badge"
          holdBadge.innerText = `${holdRank || ""}`
          el.appendChild(holdBadge)
        }
      }

      // Style the event title to ensure it's visible
      const titleEl = el.querySelector(".fc-event-title")
      if (titleEl) {
        titleEl.style.fontSize = "12px"
        titleEl.style.fontWeight = "500"
        titleEl.style.overflow = "hidden"
        titleEl.style.textOverflow = "ellipsis"
        titleEl.style.whiteSpace = "nowrap"
      }

      // Style the event time
      const timeEl = el.querySelector(".fc-event-time")
      if (timeEl) {
        timeEl.style.fontSize = "11px"
        timeEl.style.opacity = "0.8"
      }

      // Add popover handling directly to the DOM element
      let popoverTimeout: NodeJS.Timeout | null = null

      const handleMouseEnter = (e: MouseEvent) => {
        // Don't show popover if we're dragging
        if (document.body.classList.contains("fc-dragging")) {
          return
        }

        // Clear any existing timeout
        if (popoverTimeout) {
          clearTimeout(popoverTimeout)
          popoverTimeout = null
        }

        // Validate event data
        const eventStart = event.start
        const eventEnd = event.end

        if (!eventStart || !(eventStart instanceof Date) || isNaN(eventStart.getTime())) {
          console.warn("Event has invalid start date, skipping popover:", event)
          return
        }

        if (!eventEnd || !(eventEnd instanceof Date) || isNaN(eventEnd.getTime())) {
          console.warn("Event has invalid end date, using fallback for popover:", event)
          event.end = new Date(eventStart.getTime() + 3600000)
        }

        // Open popover with a small delay to prevent flashing
        popoverTimeout = setTimeout(() => {
          openPopover({ event, el })
        }, 300)
      }

      const handleMouseLeave = (e: MouseEvent) => {
        // Don't manage popover if we're dragging
        if (document.body.classList.contains("fc-dragging")) {
          return
        }

        // Clear any pending popover
        if (popoverTimeout) {
          clearTimeout(popoverTimeout)
          popoverTimeout = null
        }

        // Set timeout to close popover
        const closeTimeout = setTimeout(() => {
          const { isMouseOverPopover } = useCalendarPopover.getState()
          if (!isMouseOverPopover) {
            closePopover()
          }
        }, 50)
      }

      // Add event listeners directly to the DOM element
      el.addEventListener("mouseenter", handleMouseEnter)
      el.addEventListener("mouseleave", handleMouseLeave)

      // Store cleanup function on the element
      ;(el as any).__popoverCleanup = () => {
        el.removeEventListener("mouseenter", handleMouseEnter)
        el.removeEventListener("mouseleave", handleMouseLeave)
        if (popoverTimeout) {
          clearTimeout(popoverTimeout)
        }
      }
    },
    [rooms, openPopover, closePopover]
  )

  /**
   * Clean up popover and event listeners when events are unmounted
   */
  const eventWillUnmountHandler = useCallback(
    (info: any) => {
      const { el } = info

      // Clean up popover listeners if they exist
      if ((el as any).__popoverCleanup) {
        ;(el as any).__popoverCleanup()
      }

      // Close popover if this event was showing it
      closePopover()
    },
    [closePopover]
  )

  /**
   * Clean up popover on various events
   */
  const clearPopover = useCallback(() => {
    // Only close if mouse is not over the popover
    const { isMouseOverPopover } = useCalendarPopover.getState()
    if (!isMouseOverPopover) {
      closePopover()
    }
  }, [closePopover])

  /**
   * Manually synchronize a cart ghost item when eventDrop doesn't fire
   * Used to fix a bug in FullCalendar where eventDrop doesn't always trigger
   */
  const synchronizeCartItem = useCallback(
    (eventInfo: any) => {
      console.log("Manually synchronizing cart ghost event:", eventInfo)

      // Check if this is a cart ghost event
      const isCartGhost = eventInfo.extendedProps?.isCartGhost
      if (!isCartGhost) return

      const ghostId = eventInfo.id
      const ghostBooking = ghostBookings.find((b) => b.id === ghostId)

      if (ghostBooking) {
        const resources = eventInfo.getResources ? eventInfo.getResources() : []
        const resourceId = resources.length > 0 ? resources[0].id : null

        // Ensure we have valid start and end dates
        const eventStart = eventInfo.start
        const eventEnd = eventInfo.end

        if (!eventStart || !(eventStart instanceof Date)) {
          console.error("Invalid start date in manual sync", eventStart)
          return
        }

        if (resourceId) {
          // Get venue ID from the room's extended props
          const resource = resources[0]
          const venueId = resource.extendedProps?.venueId
          const room = rooms.find((r) => r.id === resourceId)

          const updatedGhostBooking = {
            ...ghostBooking,
            start: new Date(eventStart),
            end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
            roomId: resourceId,
            venueId: venueId || ghostBooking.venueId,
          }

          // Update the ghost booking
          updateGhostBooking(updatedGhostBooking)

          // Update the cart item
          if (room) {
            // Extract the cart item ID from the ghost ID
            const cartItemId = ghostId.replace("ghost-cart-", "")

            // Get the current cart items
            const { items, updateItem: updateCartItem } = useBookingCart.getState()

            // Find the cart item
            const cartItem = items.find((item) => item.id === cartItemId)

            if (cartItem) {
              // Create updated cart item
              const updatedCartItem = {
                ...cartItem,
                booking: {
                  ...cartItem.booking,
                  start: new Date(eventStart),
                  end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                  roomId: resourceId,
                  venueId: venueId || cartItem.booking.venueId,
                },
                room: room,
              }

              // Update the cart item
              updateCartItem(cartItemId, updatedCartItem)

              // Show a toast to indicate the manual sync happened
              showToast(`Cart item updated: ${updatedCartItem.booking.title || "Booking"} synchronized successfully`)
            }
          }
        } else {
          // If no resource (e.g., in day/week view), just update times
          const updatedGhostBooking = {
            ...ghostBooking,
            start: new Date(eventStart),
            end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
          }

          // Update the ghost booking
          updateGhostBooking(updatedGhostBooking)

          // Update the cart item too
          const cartItemId = ghostId.replace("ghost-cart-", "")
          const { items, updateItem: updateCartItem } = useBookingCart.getState()
          const cartItem = items.find((item) => item.id === cartItemId)

          if (cartItem) {
            // Create updated cart item
            const updatedCartItem = {
              ...cartItem,
              booking: {
                ...cartItem.booking,
                start: new Date(eventStart),
                end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
              },
            }

            // Update the cart item
            updateCartItem(cartItemId, updatedCartItem)

            // Show a toast to indicate the manual sync happened
            showToast(`Cart item updated: ${updatedCartItem.booking.title || "Booking"} time synchronized`)
          }
        }
      }
    },
    [ghostBookings, rooms, updateGhostBooking]
  )

  return {
    handleDateSelect,
    handleEventClick,
    handleEventChange,
    eventDidMountHandler,
    eventWillUnmountHandler,
    clearPopover,
    synchronizeCartItem,
  }
}

// Declare the global types
declare global {
  interface Window {
    disableGhostUpdates?: boolean
    eventPopoverData?: {
      event: any
      el: HTMLElement
    } | null
  }
}
