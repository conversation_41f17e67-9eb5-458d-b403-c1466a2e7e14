import { useEffect, useState } from 'react'

export function useSharedEnv() {
	const [sharedEnv, setSharedEnv] = useState<Record<string, string> | null>(
		null,
	)

	useEffect(() => {
		async function fetchSharedEnv() {
			try {
				const response = await fetch('/booking/api/shared-env')
				if (response.ok) {
					const data = await response.json()
					setSharedEnv(data)
				} else {
					console.error('Failed to fetch shared environment variables')
				}
			} catch (error) {
				console.error('Error fetching shared environment variables:', error)
			}
		}

		fetchSharedEnv()
	}, [])

	return sharedEnv
}
