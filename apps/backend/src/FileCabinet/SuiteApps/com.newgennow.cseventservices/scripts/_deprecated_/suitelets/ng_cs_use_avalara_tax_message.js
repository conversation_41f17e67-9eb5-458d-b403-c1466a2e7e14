/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 Oct 2019      <PERSON><PERSON>     checks to use_avalara_tax_message if the box is checked in CS SiteSettings.  
 *
 */

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){  
	try{
			var use_avalara_tax_message = nlapiLookupField("customrecord_ng_cs_settings", "1", "custrecord_ng_cs_use_avalara_tax_message");
			response.write(JSON.stringify(use_avalara_tax_message));
		
	} catch(err){
		jsonData = JSON.stringify(err.message);
		response.write(jsonData);
	}

}