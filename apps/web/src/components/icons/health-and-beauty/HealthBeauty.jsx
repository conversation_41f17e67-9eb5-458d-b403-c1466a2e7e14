import { SvgIcon } from "@mui/material";
import React from "react";

const HealthBeauty = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 18 18" fill="none">
      <g clipPath="url(#clip0_6820_26169)">
        <path
          d="M8.87536 10.332C8.70011 10.2487 8.49041 10.323 8.40695 10.4983L8.34866 10.6208C8.26523 10.796 8.33966 11.0057 8.51487 11.0892C8.56367 11.1124 8.61507 11.1234 8.66573 11.1234C8.79707 11.1234 8.92307 11.0495 8.98329 10.923L9.04158 10.8005C9.12504 10.6252 9.05058 10.4155 8.87536 10.332Z"
          fill="currentColor"
        />
        <path
          d="M4.9352 8.29532C4.79795 8.15807 4.57541 8.15807 4.43816 8.29532C4.14679 8.58673 3.67263 8.58673 3.38123 8.29532C3.24398 8.15807 3.02144 8.15807 2.88419 8.29532C2.74694 8.43257 2.74694 8.65511 2.88419 8.79236C3.16691 9.07505 3.5383 9.21644 3.90969 9.21644C4.28108 9.21644 4.65244 9.07508 4.93516 8.79236C5.07241 8.65514 5.07241 8.4326 4.9352 8.29532Z"
          fill="currentColor"
        />
        <path
          d="M16.7826 0.933737C15.5443 -0.304466 13.5322 -0.308052 12.2891 0.922206C11.901 0.729549 11.489 0.56428 11.0546 0.428225C9.62957 -0.0181538 8.46371 -0.00159524 8.36261 0.000619608C6.45605 0.00188523 4.79301 0.801866 4.02158 2.08904C3.91977 2.25892 3.83494 2.43498 3.76687 2.61614C3.40353 2.96211 1.76385 4.65668 1.76385 7.05947C1.76385 8.65985 0.994065 9.49541 0.53524 9.85105C0.363783 9.98394 0.270935 10.1926 0.286932 10.4093C0.302928 10.626 0.425447 10.8189 0.614693 10.9252C0.787592 11.0224 0.982041 11.1157 1.17006 11.2059C1.33631 11.2856 1.57256 11.399 1.68777 11.4727V12.0987C1.68777 12.1844 1.67142 12.268 1.63918 12.3474L1.55203 12.5619C1.43267 12.8556 1.47662 13.1919 1.66604 13.445V13.5221C1.66604 13.7501 1.76645 13.9612 1.93369 14.1044V14.5171C1.93369 15.4014 2.65312 16.1209 3.53748 16.1209H4.57522C4.76932 16.1209 4.92668 15.9635 4.92668 15.7694C4.92668 15.5753 4.76932 15.4179 4.57522 15.4179H3.53748C3.04076 15.4179 2.63656 15.0138 2.63656 14.517V13.9106C2.63656 13.7698 2.55251 13.6427 2.42299 13.5874L2.4078 13.5809C2.38418 13.5708 2.36892 13.5477 2.36892 13.5221V13.3237C2.36892 13.2449 2.34241 13.1683 2.29369 13.1063L2.23167 13.0275C2.18688 12.9706 2.17602 12.8936 2.20326 12.8265L2.29042 12.6119C2.35693 12.4481 2.39068 12.2755 2.39068 12.0987V11.3505C2.39068 11.0118 2.04569 10.8463 1.47412 10.5721C1.32404 10.5001 1.16971 10.426 1.03319 10.353C1.59844 9.89169 2.4668 8.88538 2.4668 7.05943C2.4668 5.73819 3.05492 4.63724 3.56515 3.93053C3.59303 4.54291 3.77826 5.18332 4.12107 5.82453C5.02308 7.51158 6.59386 8.21871 7.856 8.78683C9.05687 9.3274 9.73655 9.66768 9.742 10.375C9.74597 10.8945 9.62651 11.2766 9.38692 11.5109C9.11031 11.7814 8.74314 11.7744 8.74092 11.7744C8.62297 11.7662 8.51241 11.8177 8.44058 11.9115C8.36872 12.0052 8.35238 12.1291 8.39108 12.2407C8.40138 12.2705 8.65036 12.9764 9.22035 13.6643C9.93275 14.5241 10.832 14.9777 11.8347 14.9879L12.7791 17.7566C12.8289 17.9028 12.9654 17.9947 13.1117 17.9947C13.1493 17.9947 13.1876 17.9887 13.2251 17.9758C13.4089 17.9132 13.507 17.7134 13.4443 17.5297L12.5143 14.803C14.2676 13.8634 15.5004 12.4059 16.0881 10.5702C16.5565 9.10697 16.5789 7.4651 16.1831 5.91007C16.4 5.77823 16.6009 5.62084 16.7825 5.43926C18.0247 4.19712 18.0247 2.17595 16.7826 0.933737ZM15.4187 10.356C14.8671 12.0788 13.683 13.4353 11.9921 14.2827C11.137 14.3211 10.3913 13.9678 9.77473 13.2316C9.52614 12.9348 9.34621 12.6293 9.2284 12.3981C9.42893 12.3353 9.65924 12.2236 9.86659 12.025C10.2559 11.6521 10.4505 11.0952 10.4449 10.3697C10.4358 9.17728 9.32283 8.67634 8.14457 8.14594C6.91902 7.59426 5.52996 6.96901 4.74095 5.49319C4.2508 4.57635 4.13542 3.70458 4.39666 2.94024C4.39775 2.93733 4.39877 2.93437 4.39979 2.93146C4.45706 2.76587 4.53177 2.60521 4.62458 2.45034C5.2703 1.37284 6.70442 0.703463 8.36725 0.703463C8.37062 0.703463 8.37396 0.703428 8.37737 0.703323C8.3883 0.702971 9.49601 0.676498 10.8446 1.0989C12.6392 1.66108 13.9612 2.70856 14.7737 4.21227C15.8006 6.11285 16.0418 8.40954 15.4187 10.356ZM15.973 5.20758C15.8162 4.75065 15.6227 4.30498 15.3921 3.87814C14.8151 2.81052 13.9816 1.93393 12.9362 1.28294C13.9108 0.465948 15.3697 0.514815 16.2856 1.43074C17.2537 2.39887 17.2537 3.97415 16.2856 4.94229C16.1881 5.03978 16.0837 5.12833 15.973 5.20758Z"
          fill="currentColor"
        />
        <path
          d="M9.30132 16.4638L8.20019 14.5566L8.16296 13.2543C8.13181 12.1636 7.28824 11.2745 6.20075 11.186C5.40108 11.1208 4.63331 11.5643 4.28987 12.2893L4.2725 12.326C3.81723 13.287 4.14569 14.4495 5.03651 15.0302L6.13265 15.7447L7.33014 17.8188C7.39522 17.9316 7.51334 17.9946 7.63481 17.9946C7.6944 17.9946 7.75487 17.9794 7.8102 17.9474C7.97828 17.8504 8.0359 17.6354 7.93884 17.4673L6.87505 15.6248L7.29889 15.3801L7.72273 15.1353L8.69255 16.8151C8.78955 16.9832 9.00449 17.0408 9.17265 16.9437C9.3408 16.8468 9.39839 16.6319 9.30132 16.4638ZM6.4062 15.084L5.42042 14.4414C4.82269 14.0518 4.6023 13.2717 4.90777 12.6269L4.92514 12.5902C5.12996 12.1579 5.57145 11.8826 6.04483 11.8826C6.0777 11.8826 6.11071 11.884 6.1438 11.8867C6.87346 11.946 7.43948 12.5427 7.46036 13.2744L7.49415 14.4559L6.4062 15.084Z"
          fill="currentColor"
        />
        <path
          d="M9.82846 17.3998C9.7631 17.3345 9.6724 17.2969 9.57997 17.2969C9.4872 17.2969 9.39688 17.3345 9.33149 17.3998C9.2661 17.4652 9.22852 17.5559 9.22852 17.6483C9.22852 17.7407 9.26613 17.8314 9.33149 17.8968C9.39684 17.9621 9.4872 17.9998 9.57997 17.9998C9.6724 17.9998 9.76307 17.9621 9.82846 17.8968C9.89381 17.8314 9.93143 17.7407 9.93143 17.6483C9.93143 17.5559 9.89381 17.4652 9.82846 17.3998Z"
          fill="currentColor"
        />
        <path
          d="M6.94844 2.06006C6.87211 1.88164 6.6656 1.79874 6.48708 1.87507C5.86724 2.14004 5.4202 2.5867 5.22828 3.13282C5.03598 3.68002 5.10819 4.29448 5.43711 4.90968C5.50042 5.02812 5.62182 5.09548 5.74736 5.09548C5.80329 5.09548 5.86 5.08209 5.91274 5.05389C6.08391 4.96238 6.14849 4.74944 6.05698 4.57823C5.8201 4.13516 5.76441 3.72724 5.89143 3.36576C6.01725 3.00777 6.32691 2.70785 6.76341 2.52131C6.94186 2.44509 7.02469 2.23858 6.94844 2.06006Z"
          fill="currentColor"
        />
        <path
          d="M7.80462 1.61719L7.78494 1.61722C7.59087 1.61835 7.43439 1.77662 7.43555 1.97075C7.43668 2.16415 7.59383 2.32014 7.78697 2.32014C7.78768 2.32014 7.78838 2.32014 7.78908 2.32014L7.80462 2.3201C7.99872 2.3201 8.15608 2.16274 8.15608 1.96864C8.15608 1.77455 7.99872 1.61719 7.80462 1.61719Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_6820_26169">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </SvgIcon>
  );
};
export default HealthBeauty;
