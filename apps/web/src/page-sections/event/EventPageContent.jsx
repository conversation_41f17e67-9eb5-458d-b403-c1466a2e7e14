import React, { useState, useEffect } from 'react';
import {
    Box,
    Button,
    Container,
    Grid,
    SwipeableDrawer,
    Tooltip,
    useMediaQuery,
} from "@mui/material";
import { AnimatePresence, LayoutGroup } from "framer-motion";
import { MenuOpen } from "@mui/icons-material";
import { useTheme } from "@mui/material/styles";
import style from "pages/event/[slug].module.css";
import ShowMenuListItems from "components/containers/ShowMenuList";
import RenderEventDetails from "components/event-page/RenderEventDetails";
import EventMapRender from "components/event-page/EventMapRender";
import EventCollections from "components/event-page/EventCollections";
import EventBlurbInformation from "components/event-page/EventBlurbInformation";
import EventAddresses from "components/event-page/EventAddresses";
import { MaterialLabelRequestForm } from "components/forms/MaterialLabelRequestForm";
import { useEventTabs } from 'hooks/useEventTabs';
import { useEventDates } from 'hooks/useEventDates';
import { useEventSelection } from 'store/eventStore';
import { useSettings } from 'store/zSettingsStore';
import { useUser } from 'store/zUserStore';

const EventPageContent = ({ data }) => {
    const theme = useTheme();
    const mobile = useMediaQuery(theme.breakpoints.down("sm"));
    const midScreens = useMediaQuery(theme.breakpoints.down("md"));

    const { event } = useEventSelection();
    const { settings } = useSettings();
    const { user } = useUser();
    
    const { startDate, endDate } = useEventDates(data);
    const { currentTab, setCurrentTab, tabs } = useEventTabs(data);

    const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
    const [siteModalVisible, setSiteModalVisible] = useState(false);
    const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
    const [siteAddressCopied, setSiteAddressCopied] = useState(false);
    const [warehouseAddressCopied, setWarehouseAddressCopied] = useState(false);
    const [billOfLadingRequests, setBillOfLadingRequests] = useState(null);

    useEffect(() => {
        if (user && user?.profile?.bolRequests) {
            const loadedLadingRequests = user.profile.bolRequests.filter(
                (bol) => Number(bol.event.id) === Number(event?.details?.id),
            );
            setBillOfLadingRequests(loadedLadingRequests);
        }
    }, [event?.details?.id, user]);

    const toggleMobileDrawer = (prevState) => {
        setMobileDrawerOpen(!prevState);
    };

    const copyText = (id, type) => {
        const copyText = document.getElementById(id);
        navigator.clipboard.writeText(copyText.innerText).then(() => {
            if (type === "site") {
                setSiteAddressCopied(true);
                setTimeout(() => setSiteAddressCopied(false), 800);
            } else if (type === "warehouse") {
                setWarehouseAddressCopied(true);
                setTimeout(() => setWarehouseAddressCopied(false), 800);
            }
        });
    };

    return (
        <Container sx={{ px: mobile ? 0 : 2, pb: 2 }} className="margin-nav">
            <RenderEventDetails
                mobile={mobile}
                data={data}
                endDate={endDate}
                startDate={startDate}
                setSiteModalVisible={setSiteModalVisible}
                siteModalVisible={siteModalVisible}
                setWarehouseModalVisible={setWarehouseModalVisible}
                warehouseModalVisible={warehouseModalVisible}
            />
            <Grid
                container
                display="flex"
                flexDirection="row"
                justifyContent="center"
                spacing={3}
            >
                {!midScreens ? (
                    <Grid item xs={4}>
                        <LayoutGroup>
                            <ShowMenuListItems
                                data={data}
                                mStyles={style}
                                mobile={mobile}
                                tabs={tabs}
                                getTabState={currentTab}
                                additionalInfo={data.info}
                                setCurrentTab={(e) => setCurrentTab(e)}
                                billOfLadingRequests={billOfLadingRequests}
                            />
                        </LayoutGroup>
                    </Grid>
                ) : (
                    <Container sx={{ pt: 5 }}>
                        <Grid container spacing={3}>
                            <Grid item xs={12}>
                                <Container sx={{ pb: 0, mb: -3 }}>
                                    <Tooltip title="Event Menu" arrow placement="right">
                                        <Button
                                            className="shadow-md"
                                            variant="outlined"
                                            size="medium"
                                            onClick={() => toggleMobileDrawer(mobileDrawerOpen)}
                                        >
                                            <MenuOpen />
                                        </Button>
                                    </Tooltip>
                                </Container>
                            </Grid>
                        </Grid>
                        <Box display="flex" flexDirection="row">
                            <Box display="flex" flexDirection="column">
                                <SwipeableDrawer
                                    data={data}
                                    onOpen={() => toggleMobileDrawer(false)}
                                    anchor="left"
                                    open={mobileDrawerOpen}
                                    onClose={() => toggleMobileDrawer(true)}
                                >
                                    <ShowMenuListItems
                                        data={data}
                                        mStyles={style}
                                        tabs={tabs}
                                        getTabState={currentTab}
                                        setCurrentTab={(e) => setCurrentTab(e)}
                                        additionalInfo={data.info}
                                        billOfLadingRequests={billOfLadingRequests}
                                    />
                                </SwipeableDrawer>
                            </Box>
                        </Box>
                    </Container>
                )}
                <Grid item xs={midScreens ? true : 8}>
                    <LayoutGroup>
                        <Box
                            display="flex"
                            flexDirection="column"
                            sx={{ position: "relative" }}
                        >
                            <AnimatePresence mode="wait">
                                {currentTab === "info" && (
                                    <EventBlurbInformation data={data} mobile={mobile} />
                                )}
                                {currentTab === "order" && (
                                    <EventCollections data={data} mobile={mobile} />
                                )}
                                {currentTab === "addresses" && (
                                    <EventAddresses
                                        data={data}
                                        settings={settings}
                                        copyText={copyText}
                                        siteAddressCopied={siteAddressCopied}
                                        warehouseAddressCopied={warehouseAddressCopied}
                                    />
                                )}
                                {currentTab === "map" && <EventMapRender data={data} />}
                                {currentTab === "material" && (
                                    <MaterialLabelRequestForm data={data} />
                                )}
                            </AnimatePresence>
                        </Box>
                    </LayoutGroup>
                </Grid>
            </Grid>
        </Container>
    );
};

export default EventPageContent; 