import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

export default async (req, res) => {
  const session = await getServerSession(req, res, authOptions);

  if (session) {
    try {
      const response = await request({
        uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_ng_rl_cart_item_valid&deploy=customdeploy_rcs_ng_rl_cart_item_valid`,
        method: "POST",
        oauth: {
          consumer_key: process.env.OAUTH1_CONSUMER_KEY,
          consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
          token: process.env.OAUTH1_ACCESS_TOKEN,
          token_secret: process.env.OAUTH1_TOKEN_SECRET,
          signature_method: "HMAC-SHA256",
          realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
          version: "1.0",
        },
        body: req.body,
        headers: { "Content-Type": "application/json" },
        resolveWithFullResponse: true,
        // json: true, NetSuite don't like this on this endpoint
      });

      const data = await response.body;

      const jsonData = typeof data === 'string' ? JSON.parse(data) : data;

      console.debug('RES: " ', response);
      console.debug('Body: " ', data);
      res.json(jsonData);
    } catch (err) {
      res.status(500).send({
        error: `Error processing cart validation request: ${err}`,
      });
      console.error(`Error processing cart validation request: ${err}`);
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
  }
};
