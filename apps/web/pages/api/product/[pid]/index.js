import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID;
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY;
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET;
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN;
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET;

const productLoader = async (req, res) => {
  const session = await getServerSession(req, res, authOptions);
  const { cid, event, pid } = req.query;

  if (session) {
    // session is default TRUE for TESTING

    const currRequest = {
      url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_item_details&deploy=customdeploy_rcs_item_details&cid=${cid}&event=${event}&pid=${pid}`,
      method: "GET",
    };

    const response = await request({
      uri: currRequest.url,
      oauth: {
        consumer_key: CONSUMER_KEY,
        consumer_secret: CONSUMER_SECRET,
        token: TOKEN,
        token_secret: TOKEN_SECRET,
        signature_method: "HMAC-SHA256",
        realm: REALM,
        version: "1.0",
      },
      resolveWithFullResponse: true,
      json: true,
    });

    const data = await response.body;
    if (response.statusCode !== 200) {
      res.status(response.statusCode).send({
        error: `Error processing product request:`,
        data,
      });
    }

    res.send(data);
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
    // res.redirect('/auth/login')
  }
};

export default productLoader;
