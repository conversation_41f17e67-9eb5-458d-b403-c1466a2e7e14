import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  addPerformanceHeaders
} from "@event-services/netsuite-http";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  // Only allow PUT requests
  if (req.method !== "PUT") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "Request unauthorized. Page protected please sign in.",
      });
    }

    // Validate required body
    if (!req.body) {
      return res.status(400).json({ error: "Request body is required" });
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_ng_cses_rl_get_user_info",
      "customdeploy_ng_cses_rl_get_user_info"
    );

    // Make request to NetSuite
    const { data, duration } = await makeNetSuiteRequest({
      url,
      method: "PUT",
      body: req.body,
    });

    // Add performance headers
    addPerformanceHeaders(res, requestStart, duration);

    return res.status(200).json(data);
  } catch (error) {
    return handleApiError(error, { res: res, requestStart: requestStart });
  }
}
