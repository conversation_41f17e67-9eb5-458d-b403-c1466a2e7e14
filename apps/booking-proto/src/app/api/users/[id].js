import request from 'request-promise-native'
import { getServerSession } from 'next-auth/next'

const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET

export default async function userHandler(req, res) {
	const session = await getServerSession(req, res)
	const contact = req.cookies.contact
	const { id } = req.query

	const currRequest = {
		url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_rl_get_account_info&deploy=customdeploy_rcs_rl_get_account_info&id=${id}&contact=${contact}`,
		method: 'GET',
	}

	if (session) {
		if (req.method === 'GET') {
			const response = await request({
				uri: currRequest.url,
				oauth: {
					consumer_key: CONSUMER_KEY,
					consumer_secret: CONSUMER_SECRET,
					token: TOKEN,
					token_secret: TOKEN_SECRET,
					signature_method: 'HMAC-SHA256',
					realm: REALM,
					version: '1.0',
				},
				resolveWithFullResponse: true,
				json: true,
			})

			const data = await response.body
			if (response.statusCode !== 200) {
				res.status(response.statusCode).send({
					error: `Error processing Address Update request: ${data.message}`,
					data,
				})
			}

			res.send(data)
		}
	} else {
		res.status(401).send('Request unauthorized. Page protected please sign in.')
	}
}
