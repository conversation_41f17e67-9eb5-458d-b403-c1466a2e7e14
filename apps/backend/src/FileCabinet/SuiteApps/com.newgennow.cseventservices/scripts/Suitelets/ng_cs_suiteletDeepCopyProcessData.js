/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define(['N/https', 'N/record', 'N/runtime', 'N/search', 'N/url','../lib/newgen.library.v21'],
    /**
 * @param{https} https
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 */
    (https, record, runtime, search, url, NG) => {
        /**
         * Defines the Suitelet script trigger point.
         * @param {Object} scriptContext
         * @param {ServerRequest} scriptContext.request - Incoming request
         * @param {ServerResponse} scriptContext.response - Suitelet response
         * @since 2015.2
         */
        const onRequest = (scriptContext) => {

            try{

                if(scriptContext.request.method == 'POST'){

                    log.debug({title: 'Start Post Function'});

                    log.debug({title: 'Request Data', details: JSON.stringify(scriptContext.request)});

                    let params = scriptContext.request.parameters;

                    log.debug({title: 'Params', details: JSON.stringify(params)});

                    let processCopyData = params.processCopyData;

                    log.debug({title: 'Process Data', details: processCopyData});

                    var callAgain = false;

                    // if(NG.tools.isEmpty(processCopyData)){
                    //
                    //     processCopyData = JSON.stringify({"sessionToCopy":"22","eventID":"29","sessionEquipmentIDs":["15","16"],"sessionTasksIDs":["18","19","20","21","22","23"],"sessionFlowIDs":["1","2","3"],"venueSpaces":[{"venueSpaceID":"8","venueSpaceName":"Main Hall A","version":"1","equipmentData":[{"id":"15","copied":false},{"id":"16","copied":false}],"tasksData":[{"id":"18","copied":false},{"id":"19","copied":false},{"id":"20","copied":false},{"id":"21","copied":false},{"id":"22","copied":false},{"id":"23","copied":false}],"flowData":[{"id":"1","copied":false},{"id":"2","copied":false},{"id":"3","copied":false}]}]});
                    //
                    // }
                    //
                    // log.debug({title: 'Process Data', details: processCopyData});

                    let venueSpaceCount = params.venueSpaceCount;

                    log.debug({title: 'Venue Space Count', details: venueSpaceCount});

                    if(NG.tools.isEmpty(venueSpaceCount)){

                        venueSpaceCount = 0;

                    }else{

                        venueSpaceCount = Number(venueSpaceCount);

                    }

                    if(!NG.tools.isEmpty(processCopyData)){

                        log.debug('TP1')

                        log.debug({title: 'Process Copy Data', details: processCopyData});

                        var scriptObj = runtime.getCurrentScript();
                        var remainingUnits = scriptObj.getRemainingUsage()
                        log.debug('Remaining governance units: ' + remainingUnits);

                        processCopyData = JSON.parse(processCopyData);

                        let sessionToCopyID = processCopyData.sessionToCopy;

                        log.debug({title: 'Session To Copy', details: sessionToCopyID});

                        let sessionToCopyRec = record.load({
                            type: 'customrecord_ng_cs_event_function',
                            id: sessionToCopyID,
                            isDynamic: true
                        });

                        for(let i=venueSpaceCount; i < processCopyData.venueSpaces.length; i++){

                            try{

                                log.debug('TP2');

                                if(remainingUnits < 50 || venueSpaceCount >= processCopyData.venueSpaces.length){

                                    break;

                                }

                                let venueSpaceObj = processCopyData.venueSpaces[i];

                                log.debug({title: 'Venue Space Data Obj', details: JSON.stringify(venueSpaceObj)});

                                log.debug({title: 'Venue Session Record Check', details: venueSpaceObj.sessionRecordCopied});

                                let sessionTitle = sessionToCopyRec.getValue({fieldId: 'custrecord_ng_cs_session_title'});

                                let venueSpaceName = venueSpaceObj.venueSpaceName;

                                let sessionVersion = venueSpaceObj.version;

                                let eventName = sessionToCopyRec.getText({fieldId: 'custrecord_ng_cs_session_event'});

                                if(!venueSpaceObj.sessionRecordCopied && NG.tools.isEmpty(venueSpaceObj.sessionCopyRecID) && NG.tools.isEmpty(venueSpaceObj.sessionCopyError)){

                                    log.debug('TP3');

                                    //Copy Session Record
                                    try{
                                        log.debug({title: 'Copy Session Record', details: venueSpaceObj.venueSpaceID});

                                        let sessionRec = record.copy({
                                            type: 'customrecord_ng_cs_event_function',
                                            id: processCopyData.sessionToCopy,
                                            isDynamic: true
                                        });

                                        sessionRec.setValue({
                                            fieldId: 'custrecord_ng_cs_session_venue_space',
                                            value: venueSpaceObj.venueSpaceID
                                        });

                                        let sessionDays = sessionRec.getText({fieldId: 'custpage_ng_cs_session_days'});

                                        let sessionDate = '';

                                        if(Array.isArray(sessionDays) && sessionDays.length > 0){

                                            sessionDate = sessionDays[0];

                                        }else{

                                            sessionDate = sessionRec.getText({fieldId: 'custrecord_ng_cs_session_start_date'});

                                        }

                                        if(NG.tools.isEmpty(sessionDate)){

                                            sessionDate = 'TBD';

                                        }

                                        let sessionName = venueSpaceName + ' : ' + sessionTitle + ' : ' + sessionDate;

                                        sessionRec.setValue({
                                            fieldId: 'name',
                                            value: sessionName
                                        });

                                        if(!NG.tools.isEmpty(sessionVersion)){

                                            sessionRec.setValue({
                                                fieldId: 'custrecord_ng_cs_session_version',
                                                value: sessionVersion
                                            });

                                        }

                                        let sessionRecID = sessionRec.save({
                                            enableSourcing: true,
                                            ignoreMandatoryFields: true
                                        });

                                        venueSpaceObj.sessionCopyRecID = sessionRecID;

                                        venueSpaceObj.sessionRecordCopied = true;

                                        if(NG.tools.isEmpty(sessionVersion)){

                                            record.submitFields({
                                                type: 'customrecord_ng_cs_event_function',
                                                id: sessionRecID,
                                                values: {
                                                    custrecord_ng_cs_session_version: sessionRecID
                                                }
                                            })

                                        }
                                        venueSpaceObj.sessionName = sessionName;



                                        processCopyData.venueSpaces[i] = venueSpaceObj;

                                        log.debug({title: 'Session Copied', details: JSON.stringify(venueSpaceObj)});

                                    }catch(err){

                                        log.error({title: 'Error Copying Session Record', details: JSON.stringify(err)});

                                        venueSpaceObj.sessionCopyError = err.message;

                                        throw err;

                                    }

                                }

                                var remainingUnits = scriptObj.getRemainingUsage()
                                log.debug('Remaining governance units: ' + remainingUnits);

                                if(remainingUnits <= 50 && ((i+1) < processCopyData.venueSpaces.length) && (venueSpaceObj.flowData.length > 0 || venueSpaceObj.equipmentData.length > 0 || venueSpaceObj.tasksData.length > 0)){

                                    callAgain = true;

                                    break;

                                }else if(venueSpaceCount >= processCopyData.venueSpaces.length){

                                    callAgain = false;

                                    break;
                                }


                                if(venueSpaceObj.sessionRecordCopied && !NG.tools.isEmpty(venueSpaceObj.sessionCopyRecID)  && NG.tools.isEmpty(venueSpaceObj.sessionCopyError)){

                                    if(venueSpaceObj.flowData.length > 0){

                                        log.debug('TP4');

                                        //Start Flow Copy

                                        let flowCount = venueSpaceObj.flowCount;

                                        if(NG.tools.isEmpty(flowCount)){

                                            venueSpaceObj.flowCount = 0;

                                        }else{

                                            venueSpaceObj.flowCount = Number(flowCount);

                                        }

                                        for(let i=venueSpaceObj.flowCount; i < venueSpaceObj.flowData.length; i++){

                                            try{

                                                log.debug('TP5');

                                                if(remainingUnits < 50 || venueSpaceObj.flowCount >= venueSpaceObj.flowData.length){

                                                    break;

                                                }

                                                let flowDataObj = venueSpaceObj.flowData[i];

                                                log.debug({title: 'Flow Data Obj', details: JSON.stringify(flowDataObj)});

                                                if(!flowDataObj.copied && NG.tools.isEmpty(flowDataObj.flowRecCopyID) && NG.tools.isEmpty(flowDataObj.error)){

                                                    let flowRec = record.copy({
                                                        type: 'customrecord_ng_cs_sess_flow',
                                                        id: flowDataObj.id,
                                                        isDynamic: true
                                                    });

                                                    let flowDate = flowRec.getValue({
                                                        fieldId: 'custrecord_ng_cs_flow_date'
                                                    });

                                                    let month = flowDate.getMonth() + 1;

                                                    let day = flowDate.getDate();

                                                    let year = flowDate.getFullYear();

                                                    let flowDateText = month + '/' + day + '/' + year;

                                                    let flowRecName = venueSpaceName + ' : ' + sessionTitle + ' : ' + flowDateText;

                                                    flowRec.setValue({
                                                        fieldId: 'name',
                                                        value: flowRecName
                                                    });

                                                    flowRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_flow_event_session',
                                                        value: venueSpaceObj.sessionCopyRecID
                                                    })

                                                    let flowRecCopyID = flowRec.save({
                                                        enableSourcing: true,
                                                        ignoreMandatoryFields: true
                                                    });

                                                    flowDataObj.flowRecCopyID = flowRecCopyID;

                                                    flowDataObj.copied = true;

                                                    venueSpaceObj.flowCount = venueSpaceObj.flowCount + 1;

                                                    venueSpaceObj.flowData[i] = flowDataObj;

                                                    var remainingUnits = scriptObj.getRemainingUsage()
                                                    log.debug('Remaining governance units: ' + remainingUnits);

                                                    if(remainingUnits <= 50 && ((i+1) < venueSpaceObj.flowData.length)){

                                                        callAgain = true;

                                                        break;

                                                    }else if (venueSpaceObj.flowCount >= venueSpaceObj.flowData.length) {

                                                        callAgain = false;

                                                        break;
                                                    }

                                                }else{

                                                    venueSpaceObj.flowCount = venueSpaceObj.flowCount + 1;

                                                }

                                            }catch(err){

                                                venueSpaceObj.flowCount = venueSpaceObj.flowCount + 1;

                                                log.error({title: 'Error during flow record copy', details: JSON.stringify(err)});

                                                venueSpaceObj.flowData[i].error = err.message;

                                            }

                                        }


                                    }

                                    processCopyData.venueSpaces[i] = venueSpaceObj;

                                    log.debug({title: 'Flow Processed', details: JSON.stringify(venueSpaceObj)});

                                    if(callAgain){

                                        break;

                                    }

                                    var remainingUnits = scriptObj.getRemainingUsage()
                                    log.debug('Remaining governance units: ' + remainingUnits);

                                    if(remainingUnits <= 50 && ((i+1) < processCopyData.venueSpaces.length) && (venueSpaceObj.equipmentData.length > 0 || venueSpaceObj.tasksData.length > 0)){

                                        callAgain = true;

                                        break;

                                    }

                                    if(venueSpaceObj.equipmentData.length > 0){

                                        log.debug('TP6');

                                        let equipmentCount = venueSpaceObj.equipmentCount;

                                        if(NG.tools.isEmpty(equipmentCount)){

                                            venueSpaceObj.equipmentCount = 0;

                                        }else{

                                            venueSpaceObj.equipmentCount = Number(equipmentCount);

                                        }

                                        for(let i=venueSpaceObj.equipmentCount; i < venueSpaceObj.equipmentData.length; i++){

                                            try{

                                                log.debug('TP7');

                                                if(remainingUnits < 50 || venueSpaceObj.equipmentCount >= venueSpaceObj.equipmentData.length){

                                                    break;

                                                }

                                                let equipmentDataObj = venueSpaceObj.equipmentData[i];

                                                log.debug({title: 'Equipment Data Obj', details: JSON.stringify(equipmentDataObj)});

                                                if(!equipmentDataObj.copied && NG.tools.isEmpty(equipmentDataObj.equipmentRecCopyID) && NG.tools.isEmpty(equipmentDataObj.error)){

                                                    let equipmentRec = record.copy({
                                                        type: 'customrecord_ng_cs_sess_equip',
                                                        id: equipmentDataObj.id,
                                                        isDynamic: true
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_sess',
                                                        value: venueSpaceObj.sessionCopyRecID
                                                    })

                                                    let equipmentRecCopyID = equipmentRec.save({
                                                        enableSourcing: true,
                                                        ignoreMandatoryFields: true
                                                    });

                                                    equipmentDataObj.equipmentRecCopyID = equipmentRecCopyID;

                                                    equipmentDataObj.copied = true;

                                                    venueSpaceObj.equipmentCount = venueSpaceObj.equipmentCount + 1;

                                                    venueSpaceObj.equipmentData[i] = equipmentDataObj;

                                                    var remainingUnits = scriptObj.getRemainingUsage()
                                                    log.debug('Remaining governance units: ' + remainingUnits);

                                                    if(remainingUnits <= 50 && ((i+1) < venueSpaceObj.equipmentData.length)){

                                                        callAgain = true;

                                                        break;

                                                    }else if (venueSpaceObj.equipmentCount >= venueSpaceObj.equipmentData.length) {

                                                        callAgain = false;

                                                        break;
                                                    }

                                                }else{

                                                    venueSpaceObj.equipmentCount = venueSpaceObj.equipmentCount + 1;

                                                }

                                            }catch(err){

                                                venueSpaceObj.equipmentCount = venueSpaceObj.equipmentCount + 1;

                                                log.error({title: 'Error during equipment record copy', details: JSON.stringify(err)});

                                                venueSpaceObj.equipmentData[i].error = err.message;

                                            }

                                        }

                                    }

                                    processCopyData.venueSpaces[i] = venueSpaceObj;

                                    log.debug({title: 'Equipment Processed', details: JSON.stringify(venueSpaceObj)});

                                    if(callAgain){

                                        break;

                                    }

                                    var remainingUnits = scriptObj.getRemainingUsage()
                                    log.debug('Remaining governance units: ' + remainingUnits);

                                    if(remainingUnits <= 50 && ((i+1) < processCopyData.venueSpaces.length) && venueSpaceObj.tasksData.length > 0){

                                        callAgain = true;

                                        break;

                                    }

                                    if(venueSpaceObj.tasksData.length > 0){

                                        log.debug('TP8');

                                        let taskCount = venueSpaceObj.taskCount;

                                        if(NG.tools.isEmpty(taskCount)){

                                            venueSpaceObj.taskCount = 0;

                                        }else{

                                            venueSpaceObj.taskCount = Number(taskCount);

                                        }

                                        for(let i=venueSpaceObj.taskCount; i < venueSpaceObj.tasksData.length; i++){

                                            try{

                                                log.debug('TP9');

                                                if(remainingUnits < 50 || venueSpaceObj.taskCount >= venueSpaceObj.tasksData.length){

                                                    break;

                                                }

                                                let taskDataObj = venueSpaceObj.tasksData[i];

                                                log.debug({title: 'Task Data Obj', details: JSON.stringify(taskDataObj)});

                                                if(!taskDataObj.copied && NG.tools.isEmpty(taskDataObj.taskRecCopyID) && NG.tools.isEmpty(taskDataObj.error)){

                                                    let taskRec = record.copy({
                                                        type: 'customrecord_ng_cs_sess_task',
                                                        id: taskDataObj.id,
                                                        isDynamic: true
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_sess',
                                                        value: venueSpaceObj.sessionCopyRecID
                                                    })

                                                    let taskRecCopyID = taskRec.save({
                                                        enableSourcing: true,
                                                        ignoreMandatoryFields: true
                                                    });

                                                    taskDataObj.taskRecCopyID = taskRecCopyID;

                                                    taskDataObj.copied = true;

                                                    venueSpaceObj.taskCount = venueSpaceObj.taskCount + 1;

                                                    venueSpaceObj.tasksData[i] = taskDataObj;

                                                    var remainingUnits = scriptObj.getRemainingUsage()
                                                    log.debug('Remaining governance units: ' + remainingUnits);

                                                    if(remainingUnits <= 50 && ((i+1) < venueSpaceObj.tasksData.length)){

                                                        callAgain = true;

                                                        break;

                                                    }else if (venueSpaceObj.taskCount >= venueSpaceObj.tasksData.length) {

                                                        callAgain = false;

                                                        break;
                                                    }

                                                }else{

                                                    venueSpaceObj.taskCount = venueSpaceObj.taskCount + 1;

                                                }

                                            }catch(err){

                                                venueSpaceObj.taskCount = venueSpaceObj.taskCount + 1;

                                                log.error({title: 'Error during task record copy', details: JSON.stringify(err)});

                                                venueSpaceObj.tasksData[i].error = err.message;

                                            }

                                        }



                                    }

                                    processCopyData.venueSpaces[i] = venueSpaceObj;

                                    log.debug({title: 'Tasks Processed', details: JSON.stringify(venueSpaceObj)});

                                    if(callAgain){

                                        break;

                                    }

                                    if(remainingUnits <= 50 && ((i+1) < processCopyData.venueSpaces.length)){

                                        callAgain = true;

                                        break;

                                    }


                                }else{

                                    venueSpaceCount = venueSpaceCount + 1;

                                    continue

                                }

                                venueSpaceCount = venueSpaceCount + 1;

                                processCopyData.venueSpaces[i] = venueSpaceObj;

                                if(remainingUnits <= 50 && ((i+1) < processCopyData.venueSpaces.length)){

                                    callAgain = true;

                                    break;

                                }

                            }catch(err){

                                processCopyData.venueSpaces[i].copyError = err.message;

                            }

                        }

                        try{

                            log.debug({title: 'Call Again', details: callAgain});

                            log.debug({title: 'Process Copy Data', details: JSON.stringify(processCopyData)});

                            if(callAgain){

                                var data = {};
                                data['processCopyData'] = JSON.stringify(processCopyData);
                                data['venueSpaceCount'] = venueSpaceCount;

                                var scriptURL = url.resolveScript({
                                    scriptId: 'customscript_ng_cs_sl_deepcopy_data_proc',
                                    deploymentId: 'customdeploy_ng_cs_sl_deepcopy_data_proc',
                                    returnExternalUrl: true
                                });

                                log.debug({title: 'Script URL', details: scriptURL});

                                //http.post - 10 units
                                var scriptResponse = https.post({
                                    url: scriptURL,
                                    body:data,
                                    headers: null
                                });

                                if(scriptResponse.code == 200){
                                    var responseBody = JSON.parse(scriptResponse.body);

                                    if(responseBody.processCopyData){

                                        processCopyData = responseBody.processCopyData;

                                    }
                                }else{

                                    throw scriptContext.response.write(scriptResponse.body);

                                }
                            }


                        }catch(err){

                            log.error({title: 'Error Calling Suitelet Again', details: err.message});

                            var responseObj = {};
                            responseObj.success = false;
                            responseObj.error = err.message;
                            responseObj.processCopyData = processCopyData;

                            scriptContext.response.write(JSON.stringify(responseObj));

                            return;
                        }

                        log.debug({title: 'Process Copy Data', details: JSON.stringify(processCopyData)});

                        var responseObj = {};
                        responseObj.success = true;
                        responseObj.processCopyData = processCopyData;

                        scriptContext.response.write(JSON.stringify(responseObj));

                    }
                }

            }catch(err){

                var responseObj = {};
                responseObj.success = false;
                responseObj.error = err.message;

                scriptContext.response.write(JSON.stringify(responseObj));

                return;

            }



        }

        return {onRequest}

    });
