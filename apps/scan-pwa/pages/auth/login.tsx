import React from 'react'
import Login from '../../page-sections/sessions/Login'
import { FlexRowCenter } from '../../components/flex-box'
import Cookies from 'cookies'
import { loadSettings, Settings } from '../../lib/settings'
import type { GetServerSideProps, InferGetServerSidePropsType } from 'next'
import { config } from '@/auth'
import { getServerSession } from 'next-auth/next'

export const getServerSideProps = (async (ctx) => {
	const { req, res } = ctx

	// @ts-ignore
	const session = await getServerSession(req, res, config)

	// Create a cookies instance
	const cookies = new Cookies(req, res)

	// Load settings on the server side
	const settings = await loadSettings()

	res.setHeader(
		'Cache-Control',
		'public, s-maxage=10, stale-while-revalidate=59',
	)

	if (
		settings?.custrecord_ng_cs_accent_color ||
		settings?.custrecord_ng_cses_web_primary_color
	) {
		cookies.set(
			'primaryColor',
			settings?.custrecord_ng_cses_web_primary_color,
			{
				httpOnly: false, // true by default
				overwrite: true,
				priority: 'high',
			},
		)

		cookies.set('secondaryColor', settings?.custrecord_ng_cs_accent_color, {
			httpOnly: false, // true by default
			overwrite: true,
			priority: 'high',
		})
	}

	if (session) {
		return {
			redirect: {
				destination: '/scan',
				permanent: false,
			},
		}
	}

	return {
		props: {
			settings,
		},
	}
}) satisfies GetServerSideProps<{ settings: Settings | null }>

export default function LoginPage({
	settings,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	// const { settings } = useSettings();

	console.log('SETTING PROP LOGIN', settings)
	return (
		<>
			<FlexRowCenter
				// @ts-ignore
				sx={{
					// backgroundImage: "url(/assets/images/login/login-splash.png)",
					backgroundRepeat: 'no-repeat',
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					minHeight: 'calc(100vh - 40px)',
				}}
				flexDirection='column'
			>
				Scan
				<Login settings={settings} />
			</FlexRowCenter>
		</>
	)
}
