<suitelet scriptid="customscript_ng_cs_s_pull_list_rep">
  <defaultfunction>suitelet</defaultfunction>
  <description></description>
  <isinactive>F</isinactive>
  <name>NG CS SL Load Pull List Report</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/CS UI Script Files/suitelets/ng_cs_suiteletLoadPullListReport.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_sl_pull_list_rep_dep">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <eventtype></eventtype>
      <isdeployed>T</isdeployed>
      <isonline>F</isonline>
      <loglevel>AUDIT</loglevel>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>RELEASED</status>
      <title>NG CS SL Load Pull List Report</title>
    </scriptdeployment>
  </scriptdeployments>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.cs.report.library.js]</scriptfile>
    </library>
  </libraries>
</suitelet>