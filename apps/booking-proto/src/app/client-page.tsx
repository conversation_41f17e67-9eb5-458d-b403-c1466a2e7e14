"use client"

import { useState, useEffect } from "react"
import dynamic from "next/dynamic"

// Use dynamic import with SSR disabled for the BookingCalendar component
const BookingCalendarView = dynamic(() => import("../../booking-calendar"), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-screen">Loading calendar...</div>,
})

export function ClientPage() {
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    // Add error boundary for client-side errors
    window.addEventListener("error", () => {
      setHasError(true)
    })
  }, [])

  if (hasError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="p-4 bg-red-100 text-red-800 rounded-md">
          An error occurred while loading the calendar. Please try refreshing the page.
        </div>
      </div>
    )
  }

  return <BookingCalendarView />
}

export default ClientPage

