import React, { useState, useEffect } from 'react';
import {
    Box,
    Button,
    Container,
    Grid,
    SwipeableDrawer,
    Tooltip,
    useMediaQuery,
} from "@mui/material";
import { AnimatePresence, LayoutGroup, motion } from "framer-motion";
import { MenuOpen } from "@mui/icons-material";
import { useTheme } from "@mui/material/styles";
import style from "../../pages/event/[slug].module.css";
import ShowMenuListItems from "../../src/components/containers/ShowMenuList";
import RenderEventDetails from "../../src/components/event-page/RenderEventDetails";
import EventMapRender from "../../src/components/event-page/EventMapRender";
import EventCollections from "../../src/components/event-page/EventCollections";
import EventBlurbInformation from "../../src/components/event-page/EventBlurbInformation";
import EventAddresses from "../../src/components/event-page/EventAddresses";
import { MaterialLabelRequestForm } from "../../src/components/forms/MaterialLabelRequestForm";
import { useEventTabs } from '../../src/hooks/useEventTabs';
import { useEventDates } from '../../src/hooks/useEventDates';
import { useEventSelection } from '../../src/store/eventStore';
import { useSettings } from '../../src/store/zSettingsStore';
import { useUser } from '../../src/store/zUserStore';

// Animation variants for smooth, beautiful transitions
const pageVariants = {
    initial: {
        x: 20,
        opacity: 0,
    },
    animate: {
        x: 0,
        opacity: 1,
        transition: {
            type: "tween",
            ease: [0.25, 0.46, 0.45, 0.94], // Custom ease-out curve
            duration: 0.3,
        },
    },
    exit: {
        x: -20,
        opacity: 0,
        transition: {
            type: "tween",
            ease: [0.55, 0.06, 0.68, 0.19], // Custom ease-in curve
            duration: 0.2,
        },
    },
};

const EventPageContent = ({ data }) => {
    const theme = useTheme();
    const mobile = useMediaQuery(theme.breakpoints.down("sm"));
    const midScreens = useMediaQuery(theme.breakpoints.down("md"));

    const { event } = useEventSelection();
    const { settings } = useSettings();
    const { user } = useUser();
    
    const { startDate, endDate } = useEventDates(data);
    const { currentTab, setCurrentTab, tabs } = useEventTabs(data);

    const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
    const [siteModalVisible, setSiteModalVisible] = useState(false);
    const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
    const [siteAddressCopied, setSiteAddressCopied] = useState(false);
    const [warehouseAddressCopied, setWarehouseAddressCopied] = useState(false);
    const [billOfLadingRequests, setBillOfLadingRequests] = useState(null);

    useEffect(() => {
        if (user && user?.profile?.bolRequests) {
            const loadedLadingRequests = user.profile.bolRequests.filter(
                (bol) => Number(bol.event.id) === Number(event?.details?.id),
            );
            setBillOfLadingRequests(loadedLadingRequests);
        }
    }, [event?.details?.id, user]);

    const toggleMobileDrawer = (prevState) => {
        setMobileDrawerOpen(!prevState);
    };

    const copyText = (id, type) => {
        const copyText = document.getElementById(id);
        navigator.clipboard.writeText(copyText.innerText).then(() => {
            if (type === "site") {
                setSiteAddressCopied(true);
                setTimeout(() => setSiteAddressCopied(false), 800);
            } else if (type === "warehouse") {
                setWarehouseAddressCopied(true);
                setTimeout(() => setWarehouseAddressCopied(false), 800);
            }
        });
    };

    return (
        <Container sx={{ px: mobile ? 0 : 2, pb: 2 }} className="margin-nav">
            <RenderEventDetails
                mobile={mobile}
                data={data}
                endDate={endDate}
                startDate={startDate}
                setSiteModalVisible={setSiteModalVisible}
                siteModalVisible={siteModalVisible}
                setWarehouseModalVisible={setWarehouseModalVisible}
                warehouseModalVisible={warehouseModalVisible}
            />
            <Grid
                container
                display="flex"
                flexDirection="row"
                justifyContent="center"
                spacing={3}
            >
                {!midScreens ? (
                    <Grid item xs={4}>
                        <LayoutGroup>
                            <ShowMenuListItems
                                data={data}
                                mStyles={style}
                                mobile={mobile}
                                tabs={tabs}
                                getTabState={currentTab}
                                additionalInfo={data.info}
                                setCurrentTab={(e) => setCurrentTab(e)}
                                billOfLadingRequests={billOfLadingRequests}
                            />
                        </LayoutGroup>
                    </Grid>
                ) : (
                    <Container sx={{ pt: 5 }}>
                        <Grid container spacing={3}>
                            <Grid item xs={12}>
                                <Container sx={{ pb: 0, mb: -3 }}>
                                    <Tooltip title="Event Menu" arrow placement="right">
                                        <Button
                                            className="shadow-md"
                                            variant="outlined"
                                            size="medium"
                                            onClick={() => toggleMobileDrawer(mobileDrawerOpen)}
                                        >
                                            <MenuOpen />
                                        </Button>
                                    </Tooltip>
                                </Container>
                            </Grid>
                        </Grid>
                        <Box display="flex" flexDirection="row">
                            <Box display="flex" flexDirection="column">
                                <SwipeableDrawer
                                    data={data}
                                    onOpen={() => toggleMobileDrawer(false)}
                                    anchor="left"
                                    open={mobileDrawerOpen}
                                    onClose={() => toggleMobileDrawer(true)}
                                >
                                    <ShowMenuListItems
                                        data={data}
                                        mStyles={style}
                                        tabs={tabs}
                                        getTabState={currentTab}
                                        setCurrentTab={(e) => setCurrentTab(e)}
                                        additionalInfo={data.info}
                                        billOfLadingRequests={billOfLadingRequests}
                                    />
                                </SwipeableDrawer>
                            </Box>
                        </Box>
                    </Container>
                )}
                <Grid item xs={midScreens ? true : 8}>
                    <LayoutGroup>
                        <Box
                            display="flex"
                            flexDirection="column"
                            sx={{ 
                                position: "relative",
                                minHeight: "400px", // Prevent layout jumping
                            }}
                        >
                            <AnimatePresence mode="wait" initial={false}>
                                {currentTab === "info" && (
                                    <motion.div
                                        key="info-tab"
                                        layout
                                        variants={pageVariants}
                                        initial="initial"
                                        animate="animate"
                                        exit="exit"
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        <EventBlurbInformation data={data} mobile={mobile} />
                                    </motion.div>
                                )}
                                {currentTab === "order" && (
                                    <motion.div
                                        key="order-tab"
                                        layout
                                        variants={pageVariants}
                                        initial="initial"
                                        animate="animate"
                                        exit="exit"
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        <EventCollections data={data} mobile={mobile} />
                                    </motion.div>
                                )}
                                {currentTab === "addresses" && (
                                    <motion.div
                                        key="addresses-tab"
                                        layout
                                        variants={pageVariants}
                                        initial="initial"
                                        animate="animate"
                                        exit="exit"
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        <EventAddresses
                                            data={data}
                                            settings={settings}
                                            copyText={copyText}
                                            siteAddressCopied={siteAddressCopied}
                                            warehouseAddressCopied={warehouseAddressCopied}
                                        />
                                    </motion.div>
                                )}
                                {currentTab === "map" && (
                                    <motion.div
                                        key="map-tab"
                                        layout
                                        variants={pageVariants}
                                        initial="initial"
                                        animate="animate"
                                        exit="exit"
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        <EventMapRender data={data} />
                                    </motion.div>
                                )}
                                {currentTab === "material" && (
                                    <motion.div
                                        key="material-tab"
                                        layout
                                        variants={pageVariants}
                                        initial="initial"
                                        animate="animate"
                                        exit="exit"
                                        style={{
                                            width: "100%",
                                        }}
                                    >
                                        <MaterialLabelRequestForm data={data} />
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </Box>
                    </LayoutGroup>
                </Grid>
            </Grid>
        </Container>
    );
};

export default EventPageContent; 