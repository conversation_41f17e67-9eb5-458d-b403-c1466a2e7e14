<suitelet scriptid="customscript_ng_cs_sl_crt_evnt_wizrd">
  <description></description>
  <isinactive>F</isinactive>
  <name>NG CS SL Create Event Wizard</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/CS UI Script Files SS 2.0/Suitelets/ng_cs_suiteletCreateEventWizard.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_sl_crt_evnt_wizrd">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <eventtype></eventtype>
      <isdeployed>T</isdeployed>
      <isonline>F</isonline>
      <loglevel>AUDIT</loglevel>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>RELEASED</status>
      <title>NG CS SL Create Event Wizard</title>
    </scriptdeployment>
  </scriptdeployments>
</suitelet>