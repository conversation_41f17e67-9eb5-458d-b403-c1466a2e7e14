import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders
} from "@event-services/netsuite-http";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const purgeCache = false;

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_rcs_ng_get_cs_settings",
      "customdeploy_rcs_ng_get_cs_settings",
      { purgeCache }
    );

    // Make request to NetSuite
    const { data, duration } = await makeNetSuiteRequest({
      url,
      method: "GET",
    });

    // Set optimized response headers
    setOptimizedHeaders(res, { maxAge: 300 });
    
    // Add performance headers
    addPerformanceHeaders(res, requestStart, duration);

    return res.status(200).json(data);
  } catch (error) {
    return handleApiError(error, { res: res, requestStart: requestStart });
  }
}

// Disable body parsing for this API route as we don't need it
export const config = {
  api: {
    bodyParser: false,
  },
};
