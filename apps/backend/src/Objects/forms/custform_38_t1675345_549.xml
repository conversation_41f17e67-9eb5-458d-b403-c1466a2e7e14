<entryForm scriptid="custform_38_t1675345_549" standard="STANDARDCUSTOMRECORD_SHOWFORM">
  <name>S1 Trade Show Table</name>
  <recordType>[scriptid=customrecord_show]</recordType>
  <inactive>T</inactive>
  <preferred>F</preferred>
  <storedWithRecord>T</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="fieldgroup_506_t1675345_585">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Job Type</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>NAME</id>
          <label>Show Name</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_venue]</id>
          <label>Location</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_type]</id>
          <label>Show Type</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_status]</id>
          <label>Status</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_customer]</id>
          <label>Customer</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_subsidiary]</id>
          <label>Subsidiary</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_customer_address]</id>
          <label>Customer Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_customer_phone]</id>
          <label>Customer Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_customer_cell]</id>
          <label>Customer Cell</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_customer_email]</id>
          <label>Customer Email</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_sales_rep]</id>
          <label>Sales Rep</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_sales_rep_cell]</id>
          <label>Sales Rep Cell Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_acct_exec]</id>
          <label>Account Rep</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_acct_exec_cell]</id>
          <label>Account Rep Cell Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_project_mgr_cell]</id>
          <label>Project Manager Cell Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_project_mgr]</id>
          <label>Project Manager</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_exh_svs_rep]</id>
          <label>Exhibitor Services Rep</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_exh_svs_rep_cell]</id>
          <label>Exhibitor Services Rep Cell Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_floor_plan]</id>
          <label>Floor Plan</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_facility]</id>
          <label>Facility</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_facility_address]</id>
          <label>Facility Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_fac_map]</id>
          <label/>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_facility_contact]</id>
          <label>Facility Contact Name</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_facility_contact_phone]</id>
          <label>Facility Contact Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_facility_contact_email]</id>
          <label>Facility Contact Email</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_hall]</id>
          <label>Ballroom/Hall</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_507_t1675345_672">
      <label>Financial</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_show.custrecord_fin_show]</id>
          <label>Job</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_terms]</id>
          <label>Payment Terms</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_adv_wh_ship_rate]</id>
          <label>Advance Warehouse Drayage Rate</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_inbetween_ship_rate]</id>
          <label>In-Between Shipping Rate</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_direct_shipping_rate]</id>
          <label>Show Site Drayage Rate</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_adv_price_level]</id>
          <label>Advance Price Level</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_std_price_level]</id>
          <label>Standard Price Level</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_site_price_level]</id>
          <label>On Site Price Level</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_mgmnt_price_lvl]</id>
          <label>Show Management Price Level</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_tax_rate]</id>
          <label>Tax Rate</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_tax_percent]</id>
          <label>Tax Per Cent</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_cancellation_pct]</id>
          <label>Cancellation Per Cent</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_508_t1675345_188">
      <label>Show Management</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_509_t1675345_519">
      <label>Floor</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_show.custrecord_booth_size]</id>
          <label>Booth Size</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_510_t1675345_427">
      <label>Vendor Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_511_t1675345_982">
      <label>Website Show Vendors</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>PARENT</id>
          <label>Parent</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_514_t1675345_830">
      <label>Web</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_show.custrecord_w_start_date]</id>
          <label>Website Start Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_w_end_date]</id>
          <label>Website End Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_image]</id>
          <label>Show Management Logo</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ship_to_warehouse_address]</id>
          <label>Ship To Warehouse Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ship_to_facility_address]</id>
          <label>Ship To Facility Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_512_t1675345_410">
      <label>Additional Web</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_513_t1675345_951">
      <label>Equipment Counts</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>RECORDID</id>
          <label>ID</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>OWNER</id>
          <label>Owner</label>
          <visible>F</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>CREATED</id>
          <label>Date Created</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>LASTMODIFIED</id>
          <label>Last Modified</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_show_job]</id>
          <label>Job</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ng_cs_evt_gst_pct]</id>
          <label>GST Percent</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ng_cs_evt_pst_pct]</id>
          <label>PST Percent</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ng_cs_event_rental_location]</id>
          <label>Rentals Warehouse Location</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ng_cs_event_comments]</id>
          <label>Event Comments</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_58_cseg_ng_cs_job]</id>
          <label>CS Job</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_cs_st_show_complete]</id>
          <label>Show Complete</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ng_cs_use_web_store_alt_pay]</id>
          <label>Use Web Store Alt Pay</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_cs_st_contact_group]</id>
          <label>Contact Group</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_cs_st_contact_search]</id>
          <label>Contact Search</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_show.custrecord_ng_cs_st_show_campaign]</id>
          <label>Show Campaign</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>[scriptid=customrecord_show.tab_19_4132987_329]</id>
      <label>Show Dates</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_show.custrecord_adv_ord_date]</id>
              <label>Advanced Order Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=customrecord_show.custrecord_wh_ship_date]</id>
              <label>Warehouse Ship Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=customrecord_show.custrecord_last_adv_ship]</id>
              <label>Last Day for Advance Shipments</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=customrecord_show.custrecord_last_late_ship]</id>
              <label>Last Day for Late Shipments</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=customrecord_show.custrecord_cs_st_send_inventory_date]</id>
              <label>Inventory Outgoing Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=customrecord_show.custrecord_cs_st_return_inventory_date]</id>
              <label>Inventory Return Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_show_date.custrecord_show_number_date]</id>
          <label>Show Dates</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_15_4132987_329]</id>
      <label>Booths</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_show_booths.custrecord_booth_show_table]</id>
          <label>Show Booth</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_18_4132987_329]</id>
      <label>Freight</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_freight_table.custrecord_show_freight]</id>
          <label>Freight Tables</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_17_4132987_329]</id>
      <label>Display Forms</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_14_4132987_329]</id>
      <label>Additional Documents</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cses_upload_attachment.custrecord_show_upload]</id>
          <label>Upload Documents</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDNOTES</id>
      <label>Notes</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>ISINACTIVE</id>
              <label>Inactive</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDUSERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDMERGE</id>
      <label>Mail Merge</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDMESSAGES</id>
          <label>Messages</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDMEDIA</id>
      <label>Files</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDMEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDWORKFLOW</id>
      <label>Workflow</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDCUSTOM</id>
      <label>Custom</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_event_function.custrecord_ng_cs_function_event]</id>
          <label>CS Event Function</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_16_4132987_329]</id>
      <label>Transactions</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_show.sublist_10_4132987_298]</id>
          <label>Orders</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_37_t1675345_475]</id>
      <label>Labor</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_show.custrecord_ng_cs_show_sprvsr_markup]</id>
              <label>Labor Supervisor Markup</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_show_labor_schedule.custrecord_ng_cs_labor_show]</id>
          <label>Show Labor Schedule</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_83_t1516212_757]</id>
      <label>Shipments</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_shipment.custrecord_ng_cs_ship_event]</id>
          <label>CS Shipment</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_66_t1516212_605]</id>
      <label>Tasks</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_67_t1516212_384]</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=custevent_ng_cs_task_show]</id>
          <label>Tasks</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_68_t1516212_219]</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_70_t1516212_746]</id>
      <label>Areas</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_area_details.custrecord_ng_cs_ad_show_table]</id>
          <label>Area Details</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_71_t1516212_694]</id>
      <label>Vendors</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_show_table_vendor.custrecord_ng_cs_stv_show_table]</id>
          <label>Show Table Vendor</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_72_t1516212_597]</id>
      <label>Web</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_show.custrecord_ng_cs_show_image_url]</id>
              <label>Show Image URL</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_show_table_web_blurbs.custrecord_ng_cs_stwb_show_table]</id>
          <label>Show Table Web Blurbs</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=customrecord_ng_cs_item_collection.custrecord_ng_cs_itemcoll_event]</id>
          <label>CS Item Collection</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=customrecord_ng_cs_show_table_adtnl_info.custrecord_ng_cs_stai_show_table]</id>
          <label>Show Table Additional Information</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_74_t1516212_392]</id>
      <label>Bookings</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_cs_event_booking.custrecord_ng_cs_eb_event]</id>
          <label>CS Event Booking</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_show.tab_78_t1516212_313]</id>
      <label>Functions</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITCOPY</id>
        <label>Save &amp; Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEXT</id>
        <label>Save &amp; Next</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITAS</id>
        <label>Save As</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
