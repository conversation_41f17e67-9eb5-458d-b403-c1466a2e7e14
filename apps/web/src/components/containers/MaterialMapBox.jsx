import React, { useEffect, useState } from "react";
import { motion } from "motion/react";
import GoogleMapReact from "google-map-react";
import { Place, Warehouse } from "@mui/icons-material";
import { IconButton, Tooltip, Typography } from "@mui/material";

const AnyReactComponent = ({
  text,
  color,
  icon,
  warehouseAddress,
  siteAddress,
  title,
}) => (
  <motion.div
    initial={{
      opacity: 0,
    }}
    animate={{
      opacity: 1,
    }}
    transition={{
      duration: 0.5,
    }}
  >
    <Tooltip arrow placement="bottom" title={<Typography>{text}</Typography>}>
      <IconButton color={color}>{icon || <Place />}</IconButton>
    </Tooltip>
  </motion.div>
);

const MaterialMapBox = ({
  center = { lat: 59.955, lng: 30.337844 },
  siteAddress = "Scottsdale, AZ 85260",
  warehouseAddress = "Scottsdale, AZ 85260",
  zoom = 13,
}) => {
  const [siteLat, setSiteLat] = useState(59.955);
  const [siteLng, setSiteLng] = useState(30.337844);
  const [warehouseLat, setWarehouseLat] = useState(59.945);
  const [warehouseLng, setWarehouseLng] = useState(30.3379);
  const [latitude, setLatitude] = useState(0);
  const [longitude, setLongitude] = useState(0);

  // eslint-disable-next-line consistent-return
  const getLocationResultsCallback = async (results, status, mounted) => {
    let latitude;
    let longitude;
    if (mounted) {
      // eslint-disable-next-line no-undef
      if (status === google.maps.GeocoderStatus.OK) {
        latitude = results[0].geometry.location.lat();
        longitude = results[0].geometry.location.lng();
        console.log("Address for:", latitude, longitude);
        return { lat: latitude, lng: longitude };
      }
    }
  };

  useEffect(() => {
    let isMounted = true;
    getLocation(`${siteAddress}`, true, false).then((res) =>
      console.log("Res: ", res),
    ); // Grab Warehouse
    getLocation(`${warehouseAddress}`, false, true).then((res) =>
      console.log("Res: ", res),
    ); // Grab Site
    return () => {
      isMounted = false;
    };

    async function getLocation(address, site, warehouse) {
      // eslint-disable-next-line no-undef
      const geocoder = new google.maps.Geocoder();
      return geocoder.geocode({ address }, async (results, status) =>
        getLocationResultsCallback(results, status, isMounted).then(
          (res, rej) => {
            console.log("callback res: ", res);
            if (isMounted) {
              console.log("callback is mounted res: ", res);
              if (site) {
                setSiteLat(res.lat);
                setSiteLng(res.lng);
              } else if (warehouse) {
                setWarehouseLat(res.lat);
                setWarehouseLng(res.lng);
              } else {
                setLongitude(res.lng);
                setLatitude(res.lat);
              }
            }
          },
        ),
      );
    }
  }, []);

  const mapCenter = {
    lat: siteLat - 0.005,
    lng: warehouseLng + 0.005,
  };

  return (
    // Important! Always set the container height explicitly
    <GoogleMapReact
      bootstrapURLKeys={{ key: "AIzaSyDTZ5dA53eNtQbVDdMpWtrcW7MJFUVEZ8I" }}
      defaultCenter={center}
      center={mapCenter}
      defaultZoom={zoom}
      options={{
        styles:
          new Date().getHours() > 18
            ? [
                { elementType: "geometry", stylers: [{ color: "#242f3e" }] },
                {
                  elementType: "labels.text.stroke",
                  stylers: [{ color: "#242f3e" }],
                },
                {
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#746855" }],
                },
                {
                  featureType: "administrative.locality",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#d59563" }],
                },
                {
                  featureType: "poi",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#d59563" }],
                },
                {
                  featureType: "poi.park",
                  elementType: "geometry",
                  stylers: [{ color: "#263c3f" }],
                },
                {
                  featureType: "poi.park",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#6b9a76" }],
                },
                {
                  featureType: "road",
                  elementType: "geometry",
                  stylers: [{ color: "#38414e" }],
                },
                {
                  featureType: "road",
                  elementType: "geometry.stroke",
                  stylers: [{ color: "#212a37" }],
                },
                {
                  featureType: "road",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#9ca5b3" }],
                },
                {
                  featureType: "road.highway",
                  elementType: "geometry",
                  stylers: [{ color: "#746855" }],
                },
                {
                  featureType: "road.highway",
                  elementType: "geometry.stroke",
                  stylers: [{ color: "#1f2835" }],
                },
                {
                  featureType: "road.highway",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#f3d19c" }],
                },
                {
                  featureType: "transit",
                  elementType: "geometry",
                  stylers: [{ color: "#2f3948" }],
                },
                {
                  featureType: "transit.station",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#d59563" }],
                },
                {
                  featureType: "water",
                  elementType: "geometry",
                  stylers: [{ color: "#17263c" }],
                },
                {
                  featureType: "water",
                  elementType: "labels.text.fill",
                  stylers: [{ color: "#515c6d" }],
                },
                {
                  featureType: "water",
                  elementType: "labels.text.stroke",
                  stylers: [{ color: "#17263c" }],
                },
              ]
            : null,
      }}
    >
      {warehouseAddress !== "Scottsdale, AZ 85260" ? (
        <AnyReactComponent
          icon={<Warehouse />}
          color={new Date().getHours() > 18 ? "success" : "warning"}
          lat={Number.isNaN(warehouseLat) ? 59.945 : warehouseLat}
          lng={Number.isNaN(warehouseLng) ? 30.3379 : warehouseLng}
          text="Warehouse Location"
        />
      ) : null}
      {siteAddress !== "Scottsdale, AZ 85260" ? (
        <AnyReactComponent
          color="info"
          lat={Number.isNaN(siteLat) ? 59.955 : siteLat}
          lng={Number.isNaN(siteLng) ? 30.337844 : siteLng}
          text="Site Location"
        />
      ) : null}
    </GoogleMapReact>
  );
};

export default MaterialMapBox;
