import React, {useEffect, useRef, useState} from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import moment from "moment";
import { AddShoppingCartOutlined } from "@mui/icons-material";
import FormikDynamic from "../item-attributes/FormikDynamic"

const LaborTablePreview = ({
  isOpen,
  toggle,
  size,
  frame,
  rows,
  itemAttributes,
  innerRef,
  handleAttributeSubmit,
  laborStartTimeSelected,
  laborEndTimeSelected,
  laborDateSelected,
  workerCount,
  supervisionRequired,
  totalHoursChosen,
  laborBareCostTotal,
  totalSupervisonCost,
  totalAllLaborCost,
  addToCart,
  ...props
}) => {
  const [dateSelected, setDateSelected] = useState(null);

  const formRef = useRef();

  const handleSubmit = (e) => {

    let submittedAttributes = formRef?.current.values;

    formRef?.current.validateForm()
        .then((res) => {
          if (Object.keys(res).length === 0) {
            formRef?.current?.submitForm()
                .then((ret) => {
                  cartOptions.attributes = submittedAttributes;
                  formRef?.current.resetForm()
                  return addToCart(e)
                })
          }
        })

  }

  useEffect(() => {
    console.log("Labor preview props:");
    if (laborDateSelected?.date) {
      setDateSelected(laborDateSelected?.date);
    }
  }, [isOpen, laborDateSelected?.date]);

  return (
    <Dialog sx={{pt: 7}} open={isOpen} maxWidth={size} onClose={toggle} fullScreen={frame}>
      <DialogTitle
        sx={{
          p: 3,
        }}
      >
        <Typography variant="h4" gutterBottom>
          Labor Cost Preview
        </Typography>
        <Typography variant="subtitle1">
          Showing time selected{" "}
          <b>{moment(laborStartTimeSelected).format("h:mm a")}</b> to{" "}
          <b>{moment(laborEndTimeSelected).format("h:mm a")}</b> on{" "}
          <b>{moment(dateSelected).format("MMMM Do YYYY")}</b>&nbsp;needing{" "}
          <b>{workerCount}</b> worker{workerCount !== 1 && "s"}{" "}
          <b>{supervisionRequired ? "with" : "without"}</b> supervision.
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        <TableContainer component={Paper}>
          <Table size="small" aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>Labor Rate Type</TableCell>
                <TableCell align="right">Date &amp; Time</TableCell>
                <TableCell align="center"># of Laborers</TableCell>
                <TableCell align="right">Hours</TableCell>
                <TableCell align="right">Cost Rate</TableCell>
                {supervisionRequired ? (
                  <>
                    <TableCell align="right">
                      Labor Cost w/o Supervision
                    </TableCell>
                    <TableCell align="right">Supervision</TableCell>
                    <TableCell align="right">Supervision Cost</TableCell>
                  </>
                ) : (
                  <>
                    <TableCell align="right">Supervision</TableCell>
                    <TableCell align="right">Supervision Cost</TableCell>
                  </>
                )}
                <TableCell align="right">Total Price + Supervision</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows.map((row) => (
                <TableRow key={row.id}>
                  <TableCell component="th" scope="row">
                    {row.type}
                  </TableCell>
                  <TableCell align="right">
                    {row.date}
                    <br />
                    <Typography variant="caption">
                      Start: <strong>{row.start}</strong>
                    </Typography>
                    <br />
                    <Typography variant="caption">
                      End: <strong>{row.end}</strong>
                    </Typography>
                  </TableCell>
                  <TableCell align="center">{row.workerQuantity}</TableCell>
                  <TableCell align="right">{row.hours}</TableCell>
                  <TableCell align="right">{row.rate}</TableCell>
                  {supervisionRequired ? (
                    <>
                      <TableCell align="right">{row.totalLabor}</TableCell>
                      <TableCell align="right">{row.supervision}</TableCell>
                      <TableCell align="right">{row.supervisionCost}</TableCell>
                    </>
                  ) : (
                    <>
                      <TableCell align="right">{row.supervision}</TableCell>
                      <TableCell align="right">{row.supervisionCost}</TableCell>
                    </>
                  )}
                  <TableCell align="right">{row.amount}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        {totalHoursChosen && (
          <Box sx={{ pt: 2 }} display="flex" justifyContent="space-between">
            <Box display="flex" flexDirection="column">
              <Typography>Total Hours Selected</Typography>
            </Box>
            <Box display="flex" flexDirection="column" className="text-right">
              <Typography>{totalHoursChosen}</Typography>
            </Box>
          </Box>
        )}
        {laborBareCostTotal && supervisionRequired && (
          <>
            <Divider />
            <Box pt={2} display="flex" justifyContent="space-between">
              <Box display="flex" flexDirection="column">
                <Typography variant="h6">Labor Cost</Typography>
              </Box>
              <Box display="flex" flexDirection="column" className="text-right">
                <Typography variant="h6">
                  <strong>{laborBareCostTotal}</strong>
                </Typography>
              </Box>
            </Box>
          </>
        )}
        {totalSupervisonCost && supervisionRequired && (
          <>
            <Divider />
            <Box pt={2} display="flex" justifyContent="space-between">
              <Box display="flex" flexDirection="column">
                <Typography variant="h6">Supervision Cost</Typography>
              </Box>
              <Box display="flex" flexDirection="column" className="text-right">
                <Typography variant="h6">
                  <strong>{totalSupervisonCost}</strong>
                </Typography>
              </Box>
            </Box>
          </>
        )}
        {totalAllLaborCost && (
          <>
            <Divider />
            <Box pt={3} display="flex" justifyContent="space-between">
              <Box display="flex" flexDirection="column">
                <Typography variant="h5">Total Labor Cost</Typography>
              </Box>
              <Box display="flex" flexDirection="column" className="text-right">
                <Typography variant="h5">
                  <strong>{totalAllLaborCost}</strong>
                </Typography>
              </Box>
            </Box>
          </>
        )}
        {itemAttributes.length !== 0 && (
            <>
              <FormikDynamic
                  itemAttributes={itemAttributes}
                  innerRef={innerRef}
              />
            </>
        )}
      </DialogContent>
      <DialogActions>
        <Button color="secondary" variant="outlined" onClick={toggle}>
          Close
        </Button>
        <Button
          variant="contained"
          endIcon={<AddShoppingCartOutlined />}
          onClick={itemAttributes.length === 0 ?
              (e) => {
                return addToCart(e)
              } : handleAttributeSubmit}
          // onClick={(e) => addToCart(e)}
          color="primary"
        >
          Add To Cart
        </Button>
      </DialogActions>
    </Dialog>
  );
};

LaborTablePreview.defaultProps = {
  rows: [],
};

LaborTablePreview.propTypes = {};

export default LaborTablePreview;
