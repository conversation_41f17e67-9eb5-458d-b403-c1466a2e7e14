import SEO from "components/SEO";
import { FlexRowCenter } from "components/flex-box";
import Signup from "pages-sections/sessions/Signup";
import Cookies from "cookies";
import { loadSettings } from "../../lib/settings";
import TopbarLogin from "../../src/components/TopbarLogin";

const SignUpPage = ({ settings }) => {
  return (
    <>
      <TopbarLogin bgColor={settings?.custrecord_ng_cs_navbar_bckgrnd_color} />

      <FlexRowCenter
        sx={{
          backgroundImage: `url(${settings.loginBackgroundSplashUrl || "/assets/images/login/login-splash.png"} )`,
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "calc(100vh - 40px)",
        }}
        flexDirection="column"
        minHeight="100vh"
      >
        <SEO title="Sign up" sitename={settings?.company?.name} />
        <Signup settings={settings} />
      </FlexRowCenter>
    </>
  );
};

export const getServerSideProps = async ({ req, res }) => {
  // Create a cookies instance
  const cookies = new Cookies(req, res);

  // Load settings on the server side
  const settings = await loadSettings();
  // Set Cache-Control header for 8 minutes
  res.setHeader(
    "Cache-Control",
    "public, s-maxage=480, stale-while-revalidate=59",
  );

  if (
    settings?.custrecord_ng_cs_accent_color ||
    settings?.custrecord_ng_cses_web_primary_color
  ) {
    cookies.set(
      "primaryColor",
      settings?.custrecord_ng_cses_web_primary_color,
      {
        httpOnly: false, // true by default
        overwrite: true,
        priority: "high",
      },
    );

    cookies.set("secondaryColor", settings?.custrecord_ng_cs_accent_color, {
      httpOnly: false, // true by default
      overwrite: true,
      priority: "high",
    });
  }

  return {
    props: {
      settings,
    },
  };
};
export default SignUpPage;
