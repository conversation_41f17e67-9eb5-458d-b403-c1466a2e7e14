import { SvgIcon } from "@mui/material";
import React from "react";

const TwitterFilled = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 30 30">
      <circle cx="15" cy="15" r="15" fill="#00ACEE" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.5556 8C12.4147 8 13.1111 8.69645 13.1111 9.55556V11.8889H17.7778C18.6369 11.8889 19.3333 12.5853 19.3333 13.4444C19.3333 14.3036 18.6369 15 17.7778 15H13.1111V16.5556C13.1111 17.8442 14.1558 18.8889 15.4444 18.8889H17.7778C18.6369 18.8889 19.3333 19.5853 19.3333 20.4444C19.3333 21.3036 18.6369 22 17.7778 22H15.4444C12.4376 22 10 19.5624 10 16.5556V9.55556C10 8.69645 10.6964 8 11.5556 8Z"
        fill="white"
      />
    </SvgIcon>
  );
};
export default TwitterFilled;
