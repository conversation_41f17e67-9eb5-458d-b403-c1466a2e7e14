<scheduledscript scriptid="customscript_ng_cs_sched_auto_charge">
  <defaultfunction>scheduled</defaultfunction>
  <description></description>
  <isinactive>F</isinactive>
  <name>NG CS SCHED Auto-Charge Booth Orders</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <scriptfile>[/SuiteScripts/CS UI Script Files/scheduled/ng_cs_schedBillAllShowOrders.js]</scriptfile>
  <scriptcustomfields>
    <scriptcustomfield scriptid="custscript_ng_cs_auto_chrge_show_id">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>TEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Show ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_ng_cs_auto_chrge_exc_cats">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Exclude Categories</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_ng_cs_auto_chrge_exmpt_est">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Exempt Estimated</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_ng_cs_auto_chrge_slct">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CLOBTEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Selected Orders</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_ng_cs_auto_chrge_ord_types">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>TEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Order Types</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
  </scriptcustomfields>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_006">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 006</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2020-03-04</startdate>
          <starttime>00:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_002">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 002</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2016-02-16</startdate>
          <starttime>02:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_003">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 003</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2016-02-16</startdate>
          <starttime>02:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_004">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 004</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2016-02-16</startdate>
          <starttime>02:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_010">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 010</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2020-03-04</startdate>
          <starttime>00:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_001">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 001</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2016-02-16</startdate>
          <starttime>02:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_007">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 007</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2020-03-04</startdate>
          <starttime>00:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_008">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 008</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2020-03-04</startdate>
          <starttime>00:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_009">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 009</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2020-03-04</startdate>
          <starttime>00:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_ng_cs_schd_autochrg_dep_005">
      <custscript_ng_cs_auto_chrge_exc_cats>F</custscript_ng_cs_auto_chrge_exc_cats>
      <custscript_ng_cs_auto_chrge_exmpt_est>F</custscript_ng_cs_auto_chrge_exmpt_est>
      <custscript_ng_cs_auto_chrge_ord_types></custscript_ng_cs_auto_chrge_ord_types>
      <custscript_ng_cs_auto_chrge_show_id></custscript_ng_cs_auto_chrge_show_id>
      <custscript_ng_cs_auto_chrge_slct></custscript_ng_cs_auto_chrge_slct>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG CS SCHED Auto-Charge Booth Orders 005</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2016-02-16</startdate>
          <starttime>02:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
  </scriptdeployments>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
  </libraries>
</scheduledscript>