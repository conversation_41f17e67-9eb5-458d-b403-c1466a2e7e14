import React, { Suspense } from "react";
import PropTypes from "prop-types";
import {
  alpha,
  Box,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Container,
  Grid,
  IconButton,
  Skeleton,
  Tooltip,
  Typography,
} from "@mui/material";
import { AddLocationOutlined, AddTwoTone } from "@mui/icons-material";
import Scrollbar from "../../../Scrollbar";
import AddressAdditionModal from "../../../Modals/AddressAdditionModal";
import CheckoutAddressListCard from "../../../containers/CheckoutAddressListCard";

export const AddressStep = ({
  data,
  addressList,
  addressSelected,
  setSelectedAddress,
  handleNetsuiteDelete,
  handlePostToNetSuite,
  setAddAddressModalVisible,
  addAddressModalVisible,
}) => {
  return (
    <Container>
      <Box display="flex" flexDirection="column">
        <Box
          display="flex"
          flexDirection="row"
          justifyContent="end"
          alignContent="end"
        >
          <Tooltip title="Add An Address" placement="left">
            <Chip
              label="Add Address"
              icon={<AddTwoTone />}
              color="primary"
              onClick={() => setAddAddressModalVisible(!addAddressModalVisible)}
              sx={{ float: "right", borderRadius: "5%" }}
              size="large"
            />
          </Tooltip>
        </Box>
        <Box display="flex" flexDirection="row" />
      </Box>
      <Typography component="div">Pick an address</Typography>
      <Grid
        container
        spacing={3}
        sx={{
          height: "64vh",
          pt: 3,
        }}
      >
        <Scrollbar>
          {addressList?.length !== 0 ? (
            addressList.map((address, i) => {
              return (
                <Grid item key={i} sx={{ paddingTop: "2vh" }} xs={12}>
                  <Box flexDirection="column" display="flex">
                    <Suspense
                      fallback={
                        <Card>
                          <CardContent>
                            <CardHeader
                              title={
                                <Skeleton
                                  animation="wave"
                                  width="15rem"
                                  height="3.5rem"
                                  variant="text"
                                />
                              }
                              action={
                                <Skeleton
                                  animation="wave"
                                  variant="circular"
                                  width={40}
                                  height={40}
                                />
                              }
                            />
                            <Box display="flex" flexDirection="row">
                              <Skeleton
                                width={25}
                                animation="wave"
                                variant="text"
                              />
                              <Skeleton
                                sx={{ ml: 3, width: "100%" }}
                                animation="wave"
                                variant="text"
                              />
                            </Box>
                          </CardContent>
                        </Card>
                      }
                    >
                      <CheckoutAddressListCard
                        onDelete={() =>
                          handleNetsuiteDelete("address", address)
                        }
                        markedBillingAddress={address.defaultbilling === "T"}
                        markedShippingAddress={address.defaultshipping === "T"}
                        fullName={address.addressee_initialvalue}
                        addressType={
                          address.isresidential === "T" ? "home" : "commercial"
                        }
                        address={address.addr1_initialvalue}
                        city={address.city_initialvalue}
                        state={address.state_initialvalue}
                        zipCode={address.zip_initialvalue}
                        addressSelected={
                          address.internalid === addressSelected.internalid
                        }
                        selectAddress={() => setSelectedAddress(address)}
                      />
                    </Suspense>
                  </Box>
                </Grid>
              );
            })
          ) : !data ? (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box display="flex" flexDirection="column">
                  <Card>
                    <CardContent>
                      <CardHeader
                        title={
                          <Skeleton
                            animation="wave"
                            width="15rem"
                            height="3.5rem"
                            variant="text"
                          />
                        }
                        action={
                          <Skeleton
                            animation="wave"
                            variant="circular"
                            width={40}
                            height={40}
                          />
                        }
                      />
                      <Box display="flex" flexDirection="row">
                        <Skeleton width={25} animation="wave" variant="text" />
                        <Skeleton
                          sx={{ ml: 3, width: "100%" }}
                          animation="wave"
                          variant="text"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Box display="flex" flexDirection="column">
                  <Card>
                    <CardContent>
                      <CardHeader
                        title={
                          <Skeleton
                            animation="wave"
                            width="15rem"
                            height="3.5rem"
                            variant="text"
                          />
                        }
                        action={
                          <Skeleton
                            animation="wave"
                            variant="circular"
                            width={40}
                            height={40}
                          />
                        }
                      />
                      <Box display="flex" flexDirection="row">
                        <Skeleton width={25} animation="wave" variant="text" />
                        <Skeleton
                          sx={{ ml: 3, width: "100%" }}
                          animation="wave"
                          variant="text"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Box display="flex" flexDirection="column">
                  <Card>
                    <CardContent>
                      <CardHeader
                        title={
                          <Skeleton
                            animation="wave"
                            width="15rem"
                            height="3.5rem"
                            variant="text"
                          />
                        }
                        action={
                          <Skeleton
                            animation="wave"
                            variant="circular"
                            width={40}
                            height={40}
                          />
                        }
                      />
                      <Box display="flex" flexDirection="row">
                        <Skeleton width={25} animation="wave" variant="text" />
                        <Skeleton
                          sx={{ ml: 3, width: "100%" }}
                          animation="wave"
                          variant="text"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Grid>
            </Grid>
          ) : (
            <Grid container display="flex" style={{ paddingTop: "2vh" }}>
              <Grid item xs={12}>
                <Box display="flex" flexDirection="column">
                  <Card
                    sx={{
                      border: "dashed",
                      cursor: "pointer",
                      background: alpha("#fff", 0.3),
                    }}
                    onClick={() =>
                      setAddAddressModalVisible(!addAddressModalVisible)
                    }
                  >
                    <CardContent>
                      <CardHeader
                        title={
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: "white",
                              textAlign: "center",
                            }}
                          >
                            Your address book is empty
                          </Typography>
                        }
                      />
                      <Box
                        display="flex"
                        flexDirection="row"
                        justifyContent="center"
                        alignItems="center"
                      >
                        <Box display="flex" flexDirection="column">
                          <IconButton sx={{ color: "#fefefe" }}>
                            <AddLocationOutlined fontSize="large" />
                          </IconButton>
                        </Box>
                      </Box>
                      <br />
                      <Box
                        display="flex"
                        flexDirection="row"
                        justifyContent="center"
                      >
                        <Box
                          display="flex"
                          flexDirection="column"
                          justifyContent="center"
                        >
                          <Typography
                            variant="caption"
                            sx={{
                              textAlign: "center",
                              color: "white",
                            }}
                          >
                            Begin by adding an address.
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              </Grid>
            </Grid>
          )}
        </Scrollbar>
      </Grid>
      <AddressAdditionModal
        open={addAddressModalVisible}
        toggle={() => setAddAddressModalVisible(!addAddressModalVisible)}
        handlePostToNetSuite={(
          type,
          _values,
          setStatus,
          setSubmitting,
          setErrors,
          resetForm,
        ) =>
          handlePostToNetSuite(
            type,
            _values,
            setStatus,
            setSubmitting,
            setErrors,
            resetForm,
          )
        }
      />
      <hr />
    </Container>
  );
};

AddressStep.defaultProps = {
  addressList: [],
  addressSelected: {},
};

AddressStep.propTypes = {
  addressList: PropTypes.array.isRequired,
  handleNetsuiteDelete: PropTypes.func.isRequired,
  setSelectedAddress: PropTypes.func.isRequired,
  addressSelected: PropTypes.object.isRequired,
  handlePostToNetSuite: PropTypes.func.isRequired,
};
