import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"

interface BookingUpdate {
  id: string
  title?: string
  start?: string | Date
  end?: string | Date
  allDay?: boolean
  resourceId?: string
  venueId?: string
  statusId?: string
  description?: string
  expectedAttendance?: string
  rateTypeId?: string
  rateId?: string
  rate?: number
}

interface UpdateBookingsPayload {
  bookings: BookingUpdate[]
}

interface UpdateBookingsResponse {
  ok: boolean
  data: {
    updated: Array<{
      id: string
      title: string
    }>
    errors: Array<{
      index: number
      id: string
      error: string
    }>
  }
  message: string
}

async function updateBookings(payload: UpdateBookingsPayload): Promise<UpdateBookingsResponse> {
  const response = await fetch("/api/bookings", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload, null, 2),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.details || error.error || "Failed to update bookings")
  }

  return response.json()
}

export function useBookingUpdate() {
  const queryClient = useQueryClient()

  return useMutation<UpdateBookingsResponse, Error, UpdateBookingsPayload>({
    mutationFn: (payload) => {
      // Ensure the payload is serializable by converting Date objects to ISO strings
      const serializablePayload = {
        ...payload,
        bookings: payload.bookings.map(booking => ({
          ...booking,
          start: booking.start instanceof Date ? booking.start.toISOString() : booking.start,
          end: booking.end instanceof Date ? booking.end.toISOString() : booking.end,
        }))
      };
      return updateBookings(serializablePayload);
    },
    onSuccess: (data) => {
      if (data.ok) {
        // Invalidate the calendar events query to refresh the calendar
        queryClient.invalidateQueries({ queryKey: ["calendarEvents"] })
        
        // Show success toast with details
        if (data.data.errors.length === 0) {
          toast.success(data.message, {
            description: `${data.data.updated.length} booking${data.data.updated.length > 1 ? 's' : ''} updated successfully`,
          })
        } else {
          toast.warning(data.message, {
            description: `${data.data.updated.length} booking${data.data.updated.length > 1 ? 's' : ''} updated, ${data.data.errors.length} failed`,
          })
        }
      } else {
        toast.error("Failed to update bookings", {
          description: data.message,
        })
      }
    },
    onError: (error) => {
      toast.error("Error updating bookings", {
        description: error.message || "Please try again later",
      })
    },
  })
} 