import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import Cookies from "js-cookie";
import { useEffect, useCallback } from "react";
import moment from "moment/moment";
import useSWR from "swr";
import { useSettings } from "./zSettingsStore";

export const useEventStore = create(
  persist(
    // eslint-disable-next-line no-unused-vars
    (set, get) => ({
      event: {},
      booth: {},
      setEvent: (data) => set({ event: data }),
      setBooth: (data) => set({ booth: data }),
    }),
    {
      name: "selected-event", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
      partialize: (state) => ({ event: state.event, booth: state.booth }),
    },
  ),
);

export const useEventData = (slug) => {
  const { event, setEvent, booth, setBooth } = useEventStore((state) => state);
  const { settings } = useSettings();

  const eventId = slug || event?.details?.id;
  
  // Move fetcher outside to be accessible in the callback
  const fetcher = (...args) => fetch(...args).then((res) => res.json());

  const { data, error, isLoading, mutate } = useSWR(
    eventId ? `/api/customer/event/${eventId}` : null,
    fetcher,
    {
      revalidateOnMount: true,
      dedupingInterval: 10000, // Increased deduplication interval to prevent conflicts
      keepPreviousData: true, // Keep showing old data while fetching new
      revalidateOnFocus: false, // Prevent refetch on window focus to reduce requests
      revalidateOnReconnect: true, // Refetch when connection is restored
      refreshInterval: 0, // Disable automatic refresh
      errorRetryCount: 2, // Limit retry attempts to prevent hanging
      errorRetryInterval: 1000, // 1 second between retries
      timeout: 20000, // 20 second timeout to prevent hanging
      onSuccess: (data) => {
        // Log performance metrics in development
        if (process.env.NODE_ENV === "development") {
          console.log(`Event ${eventId} loaded successfully`);
        }
      },
      onError: (err) => {
        console.error(`Failed to load event ${eventId}:`, err);
      },
      onErrorRetry: (error, key, config, revalidate, { retryCount }) => {
        // Don't retry on 4xx errors
        if (error.status >= 400 && error.status < 500) return;
        
        // Don't retry more than 2 times
        if (retryCount >= 2) return;
        
        // Retry with exponential backoff
        setTimeout(() => revalidate({ retryCount }), 1000 * Math.pow(2, retryCount));
      },
    }
  );

  // Prefetch function for preloading event data
  const prefetchEvent = useCallback((prefetchEventId) => {
    if (prefetchEventId) {
      mutate(
        `/api/customer/event/${prefetchEventId}`,
        fetcher(`/api/customer/event/${prefetchEventId}`),
        { revalidate: false }
      );
    }
  }, [mutate, fetcher]);

  useEffect(() => {
    let active = true;

    if (active && data?.details) {
      setEvent(data);

      const companyDateFormat =
        settings?.company?.preferences?.shortDateFormat || "MM/DD/YYYY";

      const eventExpiration = moment(
        data?.details?.websiteEndDate,
        companyDateFormat,
      )
        .endOf("day")
        .toDate();

      // Use data instead of event to ensure we're using the fresh data
      Cookies.set("eventId", data?.details?.id, { path: "/", expires: 7 });
      Cookies.set("lastAdvOrderDate", data?.details?.advancedOrderDate, {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("eventExpires", data?.details?.websiteEndDate || "", {
        path: "/",
        expires: eventExpiration,
      });
    }

    return () => {
      active = false;
    };
  }, [data]);

  return { data, error, isLoading, booth, setEvent, setBooth, prefetchEvent };
};

export const useEvent = () => {
  const { event, setEvent } = useEventStore((state) => state);
  const { settings } = useSettings();

  useEffect(() => {
    let active = true;

    if (active && event?.details) {
      const companyDateFormat =
        settings?.company?.preferences?.shortDateFormat || "MM/DD/YYYY";

      const eventExpiration = moment(
        event?.details?.websiteEndDate,
        companyDateFormat,
      )
        .endOf("day")
        .toDate();

      Cookies.set("eventId", event?.details?.id, {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("lastAdvOrderDate", event?.details?.advancedOrderDate, {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("eventExpires", event?.details?.websiteEndDate || "", {
        path: "/",
        expires: eventExpiration,
      });
    }

    return () => {
      active = false;
    };
  }, [event?.details]);

  return { event, setEvent };
};

export const useBooth = () => {
  const { booth, setBooth, event } = useEventStore((state) => state);
  const { settings } = useSettings();

  const companyDateFormat =
    settings?.company?.preferences?.shortDateFormat || "MM/DD/YYYY";

  const eventExpiration = moment(
    event?.details?.websiteEndDate,
    companyDateFormat,
  )
    .endOf("day")
    .toDate();

  useEffect(() => {
    let active = true;

    if (active) {
      Cookies.set("boothName", booth.name, {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("booth", booth.id, { path: "/", expires: eventExpiration });
    }

    return () => {
      active = false;
    };
  }, [booth]);

  return { booth, setBooth };
};

export const useEventSelection = () => {
  const { event, setEvent, booth, setBooth } = useEventStore((state) => state);
  const { settings } = useSettings();

  const companyDateFormat =
    settings?.company?.preferences?.shortDateFormat || "MM/DD/YYYY";

  const eventExpiration = moment(
    event?.details?.websiteEndDate,
    companyDateFormat,
  )
    .endOf("day")
    .toDate();

  useEffect(() => {
    let active = true;

    if (active) {
      Cookies.set("boothName", booth?.name || "", {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("booth", booth?.id || "", {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("eventId", event?.details?.id || "", {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("lastAdvOrderDate", event?.details?.advancedOrderDate || "", {
        path: "/",
        expires: eventExpiration,
      });
      Cookies.set("eventExpires", event?.details?.websiteEndDate || "", {
        path: "/",
        expires: eventExpiration,
      });
    }

    return () => {
      active = false;
    };
  }, [booth, event?.details]);

  return { event, setEvent, booth, setBooth };
};
