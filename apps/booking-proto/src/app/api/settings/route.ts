import { NextResponse, type NextRequest } from "next/server"
import request from "request-promise-native"
// import { getServerSession } from "next-auth"

// NetSuite RESTlet URL for booking settings
const NETSUITE_SETTINGS_URL =
  "https://tstdrv1516212.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_booking_settings&deploy=customdeploy_ng_cses_rl_booking_settings"

// NetSuite OAuth credentials
const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET

export async function GET(req: NextRequest) {
  // TODO: Uncomment when NextA<PERSON> is fully configured
  // const session = await getServerSession()
  // if (!session) {
  //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  // }

  try {
    console.log(`Fetching settings from NetSuite: ${NETSUITE_SETTINGS_URL}`)

    // Check if OAuth credentials are available
    if (!CONSUMER_KEY || !CONSUMER_SECRET || !TOKEN || !TOKEN_SECRET || !REALM) {
      console.warn("NetSuite OAuth credentials not configured, using mock data")
      
      // Fallback to mock data when credentials are not available
      const mockData = {
        status: {
          list: [
            { id: "1", name: "Inquiry", custrecord_status_color: "#6B7280" },
            { id: "2", name: "Prospect", custrecord_status_color: "#F59E0B" },
            { id: "3", name: "Tentative", custrecord_status_color: "#3B82F6" },
            { id: "4", name: "1st Hold", custrecord_status_color: "#F97316" },
            { id: "5", name: "Definite", custrecord_status_color: "#10B981" },
            { id: "6", name: "Lost", custrecord_status_color: "#EF4444" },
            { id: "7", name: "Canceled", custrecord_status_color: "#DC2626" },
          ],
        },
        event: {
          list: [
            { id: "109", name: "Atlanta Tech Summit 2025" },
            { id: "110", name: "Healthcare Innovation Expo 2025" },
            { id: "111", name: "Retail Connect Conference 2025" },
          ],
        },
        pricing: {
          rates: [
            {
              id: "4",
              name: "Ballroom A - Daily Rate [Standard] $1,000.00/Day",
              custrecord_ng_cs_space_rate_label: "Daily Rate",
              custrecord_ng_cs_space_rate_type: "1",
              custrecord_ng_cs_space_rate_rate: 1000,
              custrecord_ng_cs_space_rate_assoc_space: "23",
            },
            {
              id: "5",
              name: "Ballroom A - Hourly Rate $150.00/Hour",
              custrecord_ng_cs_space_rate_label: "Hourly Rate",
              custrecord_ng_cs_space_rate_type: "2",
              custrecord_ng_cs_space_rate_rate: 150,
              custrecord_ng_cs_space_rate_assoc_space: "23",
            },
            {
              id: "6",
              name: "Conference Room B - Daily Rate $500.00/Day",
              custrecord_ng_cs_space_rate_label: "Daily Rate",
              custrecord_ng_cs_space_rate_type: "1",
              custrecord_ng_cs_space_rate_rate: 500,
              custrecord_ng_cs_space_rate_assoc_space: "24",
            },
          ],
          list: [],
        },
        rateTypes: [
          { id: "1", name: "Standard", scriptid: "VAL_55728_T1516212_871" },
          { id: "2", name: "Hourly", scriptid: "VAL_55729_T1516212_302" },
          { id: "3", name: "Sq Ft", scriptid: "VAL_55730_T1516212_744" },
        ],
      }
      return NextResponse.json(mockData)
    }

    // Make authenticated request to NetSuite using request-promise-native
    const response = await request({
      uri: NETSUITE_SETTINGS_URL,
      method: "GET",
      oauth: {
        consumer_key: CONSUMER_KEY,
        consumer_secret: CONSUMER_SECRET,
        token: TOKEN,
        token_secret: TOKEN_SECRET,
        signature_method: "HMAC-SHA256",
        realm: REALM,
        version: "1.0",
      } as any, // Cast to any to bypass TypeScript error with signature_method
      json: true,
      resolveWithFullResponse: true,
    })

    if (response.statusCode !== 200) {
      throw new Error(`NetSuite API returned status ${response.statusCode}: ${response.body?.message || 'Unknown error'}`)
    }

    // Return the NetSuite response
    return NextResponse.json(response.body)
    
  } catch (error) {
    const message = error instanceof Error ? error.message : "Unknown error"
    console.error("API Settings Error:", message)
    return NextResponse.json(
      { error: "Internal Server Error", details: message },
      { status: 500 }
    )
  }
} 