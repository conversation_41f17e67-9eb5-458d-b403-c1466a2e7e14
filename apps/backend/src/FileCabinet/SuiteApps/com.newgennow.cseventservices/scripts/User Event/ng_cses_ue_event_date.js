/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAMDConfig ./amdUserEventConfig.json
 */
define([
  "N/config",
  "N/redirect",
  "N/file",
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "moment",
  "settings",
  "N/task"
] /**
 * @param{config} config
 * @param{redirect} redirect
 * @param{file} file
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{moment} moment
 * @param{Object} settings
 * @param{() => CS_Settings} settings.useSettings
 * @param{task} task
 */, (
  config,
  redirect,
  file,
  query,
  record,
  runtime,
  search,
  url,
  moment,
  settings,
  task,
) => {
  /*
   * When a user adds/updates/deletes a CS Event Date, if that date type is set to create a labor schedule, the system should either add/update/delete the labor schedule to match it.
   * The labor schedule records created should match those set as the global master labor schedule records -
   * the set of dates that are used are determined on the day of the week set on the event date.
   * */

  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServletRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  const beforeLoad = (scriptContext) => {
    const { newRecord, type: mode, form } = scriptContext;
    if (["create", "copy", "edit"].includes(mode)) {
      const calendarEventField = form.getField({ id: "custrecord_cal_event" });

      log.debug({ title: "URL", details: url });

      if (calendarEventField) {
        if (mode !== "edit") {
          const calendarEventHoldField = form.addField({
            id: "custpage_cal_event",
            type: "text",
            label: "Calendar Event",
          });

          calendarEventHoldField.updateDisplayType({ displayType: "INLINE" });
          calendarEventHoldField.helpText =
            "This field is automatically populated with script. Is it not recommend to overwrite or change this value/record.";
          calendarEventHoldField.defaultValue = "To Be Generated";
          form.insertField({
            field: calendarEventHoldField,
            nextfield: "custrecord_cal_event",
          });
          calendarEventField.updateDisplayType({ displayType: "HIDDEN" });
        } else {
          const calendarEvent = newRecord.getValue({
            fieldId: "custrecord_cal_event",
          });
          calendarEvent &&
            calendarEventField.updateDisplayType({ displayType: "INLINE" });
        }
      }
    }
  };

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = (scriptContext) => {
    const { newRecord, oldRecord, type: mode } = scriptContext;

    switch (mode) {
      case "delete":
        if (oldRecord?.id) {
          const eventId = newRecord.getValue({
            fieldId: "custrecord_cal_event",
          });
          const showDate = newRecord.getValue({ fieldId: "custrecord_date" });
          const showId = newRecord.getValue({
            fieldId: "custrecord_show_number_date",
          });

          try {
            eventId && record.delete({ type: "calendarevent", id: eventId });
          } catch (err) {
            log.error({ title: "Error Deleting Calendar Event", details: err });
          }

          if (showDate) {
            let showDateSearchErrored = false;
            const showDateResults = [];
            const showDateFilters = [
              ["custrecord_date", "on", showDate],
              "and",
              ["custrecord_show_number_date", "is", showId],
            ];
            const showDateSearch = search.create({
              type: "customrecord_show_date",
              filters: showDateFilters,
              columns: [],
            });

            try {
              getAllResultsFor(showDateSearch, (result) => {
                showDateResults.push(result.id);
              });
            } catch (err) {
              showDateSearchErrored = true;
              log.error({
                title: "🔴 Error occurred running show date search: ",
                details: err,
              });
            }

            if (!showDateSearchErrored) {
              const laborTableFilters = [
                ["custrecord_ng_cs_labor_date", "on", showDate],
                "and",
                ["custrecord_ng_cs_labor_show", "is", showId],
              ];
              const laborTableSearch = search.create({
                type: "customrecord_ng_cs_show_labor_schedule",
                filters: laborTableFilters,
                columns: [],
              });
              let laborSchedulesFound = [];
              try {
                getAllResultsFor(laborTableSearch, (result) => {
                  laborSchedulesFound.push(result.id);
                });

                log.audit({
                  title: "📬 Labor schedules found:",
                  details: laborSchedulesFound,
                });

                laborSchedulesFound.forEach((scheduleId) => {
                  try {
                    record.delete({
                      type: "customrecord_ng_cs_show_labor_schedule",
                      id: scheduleId,
                    });
                  } catch (err) {
                    log.error({
                      title: "🔴 Error deleting labor schedule",
                      details: err,
                    });
                  }
                });
              } catch (err) {
                log.error({
                  title: "🔴 Error encountered deleting labor schedules:",
                  details: err,
                });
              }
            }
          }
        }
        break;
      default:
        log.audit({
          title: `📘 No beforeSubmit logic specified for ${mode}`,
          details: "",
        });
        break;
    }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (scriptContext) => {
    const CS_SETTINGS = settings.useSettings();
    const { newRecord, oldRecord, type: mode } = scriptContext;
    const { id } = newRecord;
    let calendarEventId = null;
    let currentDateRecord = null;
    const laborDateTypes = parseMultiSelectQueryValues(
      CS_SETTINGS.custrecord_ng_cs_dflt_labor_date_types,
    );

    switch (mode) {
      case "create":
        calendarEventId = newRecord.getValue({
          fieldId: "custrecord_cal_event",
        });

        if (id) {
          currentDateRecord = record.load({
            type: newRecord.type,
            id: id,
          });
          createCalendarEvent(currentDateRecord);

          const eventDateLookup = search.lookupFields({
            type: "customrecord_show_date",
            id: id,
            columns: [
              "custrecord_date",
              "custrecord_show_number_date",
              "custrecord_date_type",
            ],
          });

          log.debug({ title: "Event Date Lookup:", details: eventDateLookup });

          const eventDate = eventDateLookup.custrecord_date;
          const eventId = eventDateLookup.custrecord_show_number_date;
          const dateType = eventDateLookup.custrecord_date_type;

          log.debug({
            title:
              "Checking labor date types if date type matches of current date:",
            details: dateType,
          });

          if (Array.isArray(dateType) && dateType.length > 0 && laborDateTypes.includes(dateType[0].value)) {
            const newShowId = newRecord.getValue("custrecord_show_number_date");
            const eventLaborScheduleCount = checkEventScheduleDateTypes(newShowId, dateType[0].value);

            if (eventLaborScheduleCount === 0) {
              createLaborSchedules(eventId?.[0]?.value, eventDate, dateType[0].value);
            }

          }
          setEventStartAndEndDates(scriptContext, CS_SETTINGS);
          triggerAutoBlurbMr(eventId)
        }
        break;
      case "edit":
        calendarEventId = newRecord.getValue({
          fieldId: "custrecord_cal_event",
        });

        if (id) {
          currentDateRecord = record.load({
            type: newRecord.type,
            id: id,
          });

          if (calendarEventId) {
            // Update the calendar event
            updateCalendarEvent(currentDateRecord);
          } else {
            // Create the calendar event
            createCalendarEvent(currentDateRecord);
          }

          const oldDate = oldRecord.getValue({ fieldId: "custrecord_date" });
          const newDate = newRecord.getValue({ fieldId: "custrecord_date" });
          const oldShowId = oldRecord.getValue("custrecord_show_number_date");
          const newShowId = newRecord.getValue("custrecord_show_number_date");
          const newDateType = newRecord.getValue({
            fieldId: "custrecord_date_type",
          });

          const COMPANY_PREFERENCES = config.load({
            type: config.Type.COMPANY_PREFERENCES,
          });
          const dateformat = COMPANY_PREFERENCES.getValue("DATEFORMAT");
          const oldDateParsed = moment(oldDate);
          const oldDateFormatted = oldDateParsed.format(dateformat);
          const oldDateLocal = oldDateParsed.format("L");
          const newDateParsed = moment(newDate);
          const newDateFormatted = newDateParsed.format(dateformat);
          const oldJDate = oldDateParsed.toDate();
          const newJDate = newDateParsed.toDate();
          const today = new Date();

          log.debug({
            title: "🟨 Date Comparison:",
            details: {
              oldDate,
              newDate,
              oldShowId,
              newShowId,
              newDateType,
              oldDateFormatted,
              newDateFormatted,
              oldDateLocal,
              oldJDate,
              today,
            },
          });

          log.debug({
            title: "🟨 Dates formatted:",
            details: { oldDateFormatted, newDateFormatted },
          });

          if (laborDateTypes.includes(newDateType)) {
            // Check if date has changed
            if (oldJDate !== newJDate) {
              // Delete existing labor schedules
              // const laborSchedules = query
              //   .runSuiteQL({
              //     query: `
              //   SELECT * FROM customrecord_ng_cs_show_labor_schedule
              //   WHERE isinactive = 'F'
              //   AND custrecord_ng_cs_labor_date = '${oldDateParsed}'
              //   AND custrecord_ng_cs_labor_show = '${oldShowId}'
              //   `,
              //   })
              //   .asMappedResults();

              const laborTableFilters = [
                search.createFilter({
                  name: "isinactive",
                  operator: search.Operator.IS,
                  values: false,
                }),
                search.createFilter({
                  name: "custrecord_ng_cs_labor_date",
                  operator: search.Operator.ON,
                  values: oldDateLocal,
                }),
                search.createFilter({
                  name: "custrecord_ng_cs_labor_show",
                  operator: search.Operator.IS,
                  values: oldShowId,
                }),
              ];

              const laborTableSearch = search.create({
                type: "customrecord_ng_cs_show_labor_schedule",
                filters: laborTableFilters,
                columns: [],
              });

              const laborScheduleCount = laborTableSearch.runPaged().count;

              log.audit({
                title: "📥 Number of Labor schedules found:",
                details: laborScheduleCount,
              });

              const laborSchedulesDeleted = [];
              const laborSchedulesFound = [];

              try {
                getAllResultsFor(laborTableSearch, (result) => {
                  laborSchedulesFound.push(result.id);
                });

                log.audit({
                  title: "📬 Labor schedules found:",
                  details: laborSchedulesFound,
                });

                laborSchedulesFound.forEach((scheduleId) => {
                  try {
                    record.delete({
                      type: "customrecord_ng_cs_show_labor_schedule",
                      id: scheduleId,
                    });

                    laborSchedulesDeleted.push(scheduleId);
                  } catch (err) {
                    log.error({
                      title: "🔴 Error deleting labor schedule",
                      details: err,
                    });
                  }
                });

                log.audit({
                  title: "🟢 Labor Schedules Deleted:",
                  details: laborSchedulesDeleted,
                });
              } catch (err) {
                log.error({
                  title:
                    "🔴 Error encountered deleting labor schedules for update:",
                  details: err,
                });
              }

              log.audit({
                title: "📘 Updating schedule with new scheduled dates",
                details: "",
              });
              // Create new labor schedules
              createLaborSchedules(newShowId, newDate);
            } else {
              log.audit({
                title: "🟢 Date has not changed, updating existing schedules",
                details: "",
              });
              // Update existing labor schedules check schedules exist
              const eventLaborTableExists = checkEventSchedules(
                { id: newShowId },
                newDate,
              );

              if (!eventLaborTableExists) {
                createLaborSchedules(newShowId, newDate);
              }
            }
          } else {
            log.audit({
              title: "📝 Labor Type is not in list:",
              details: {
                laborDateTypes,
                newDateType,
              },
            });
          }

          log.debug({
            title:
              "Checking labor date types if date type matches of current date:",
            details: {
              newDateType,
              oldDate,
              newDate,
              oldShowId,
              newShowId,
            },
          });

          // Logic moved from ng_cses_ue_cs_show_date.js
          setEventStartAndEndDates(scriptContext, CS_SETTINGS);
          triggerAutoBlurbMr(newShowId)
        }
        break;
      case "copy":
        const eventId = newRecord.getValue({fieldId: 'custrecord_show_number_date'});

        triggerAutoBlurbMr(eventId)
        break;
      case "delete":
        if (oldRecord?.id) {
          const oldEventId = oldRecord.getValue("custrecord_show_number_date");

          if (oldEventId) {
            const laborTableFilters = [
              [
                "custrecord_ng_cs_labor_date",
                "on",
                oldRecord.getValue("custrecord_date"),
              ],
              "and",
              ["custrecord_ng_cs_labor_show", "is", oldEventId],
            ];
            const laborTableSearch = search.create({
              type: "customrecord_ng_cs_show_labor_schedule",
              filters: laborTableFilters,
              columns: [],
            });

            const schedulesDeleted = [];

            try {
              getAllResultsFor(laborTableSearch, (result) => {
                try {
                  const scheduleId = record.delete({
                    type: "customrecord_ng_cs_show_labor_schedule",
                    id: result.id,
                  });

                  schedulesDeleted.push(scheduleId);
                } catch (err) {
                  log.error({
                    title: "🔴 Error deleting labor schedule",
                    details: err,
                  });
                }
              });

              log.audit({
                title: "🙅 Labor Schedules Deleted:",
                details: schedulesDeleted,
              });

              redirect.toRecord({
                type: "customrecord_show",
                id: oldRecord.id,
                isEditMode: false,
              });
            } catch (err) {
              log.error({
                title: "🔴 Error encountered deleting labor schedules:",
                details: err,
              });
            }
            triggerAutoBlurbMr(oldEventId)
          }
        }
        break;
      default:
        log.audit({
          title: `📘 No afterSubmit logic specified for ${mode}`,
          details: "",
        });
        break;
    }
  };

  /**
   * A utility function to check if there are any existing labor schedules with a specific date type for a given event.
   *
   * @param {string} eventId - A string containing the ID of the event.
   * @param {string} dateType - A string containing the ID of the date type selected.
   * @returns {number} - A number of existing labor schedules for the given event.
   */
  const checkEventScheduleDateTypes = (eventId, dateType) => {
    let eventLaborSchedResultCount = 0;

    let scheduleFilters = {
      local: [],
      preferred: [],
    };

    log.debug({
      title: "📝 Checking labor schedules:",
      details: { eventId, dateType },
    });

    let searchObjects = {
      preferred: null,
    };

    scheduleFilters.preferred = [
      search.createFilter({
        name: "custrecord_ng_cs_labor_show",
        operator: search.Operator.IS,
        values: eventId,
      }),
      search.createFilter({
        name: "custrecord_ng_cs_date_type",
        operator: search.Operator.IS,
        values: dateType,
      }),
    ];

    searchObjects.preferred = search.create({
      type: "customrecord_ng_cs_show_labor_schedule",
      filters: scheduleFilters.preferred,
      columns: [],
    });

    try {
      const resultsCount = searchObjects.preferred.runPaged().count;
      eventLaborSchedResultCount = resultsCount;

      log.audit({
        title: "📘 Count of schedule items on event:",
        details: resultsCount,
      });
    } catch (error) {
      log.error({
        title:
          "🔴 Error checking event schedules",
        details: error,
      });
    }

    return eventLaborSchedResultCount;
  }

  /**
   * Updates a calendar event based on the provided date record.
   * The function loads the corresponding event, sets its time details,
   * and base on the event status, it updates the title and status.
   * If the event doesn't have an end time, it's defaulted to 11:59 PM by calling getEventEndTime with eventDate.
   * The event will be saved upon all updates, or an error log will be thrown.
   *
   * @param {Record} currentDateRecord - The provided date record containing the necessary event details.
   * @param {string} currentDateRecord.custrecord_show_number_date - The show number and date of the event.
   * @param {string} currentDateRecord.custrecord_date - The date of the event.
   * @param {string} currentDateRecord.custrecord_start_time - The start time of the event.
   * @param {string} currentDateRecord.custrecord_end_time - The end time of the event.
   * @param {string} currentDateRecord.custrecord_date_type - The date type label of the event.
   *
   * @throws {Object} Will throw an error if saving the updated event fails for any reason.
   */
  const updateCalendarEvent = (currentDateRecord) => {
    log.audit({ title: "🟢 Updating Calendar event...", details: "" });

    const eventNumber = currentDateRecord.getText(
      "custrecord_show_number_date",
    );
    const eventNumberId = currentDateRecord.getValue(
      "custrecord_show_number_date",
    );
    const calendarEventId = currentDateRecord.getValue("custrecord_cal_event");
    const eventDate = currentDateRecord.getValue("custrecord_date");
    const startTime = currentDateRecord.getValue("custrecord_start_time");
    let endTime =
      currentDateRecord.getValue("custrecord_end_time") ||
      getEventDefaultEndTime(eventDate);
    const dateTypeLabel = currentDateRecord.getText("custrecord_date_type");

    let eventTitle = `${eventNumber} - ${dateTypeLabel}`;

    const event = record.load({
      type: "calendarevent",
      id: calendarEventId,
    });

    const csEventLookup = search.lookupFields({
      type: "customrecord_show",
      id: eventNumberId,
      columns: ["custrecord_show_status"],
    });

    const eventStatus =
      csEventLookup.custrecord_show_status &&
      Number(csEventLookup.custrecord_show_status[0]?.value);

    log.debug({ title: "🟡 Event status:", details: eventStatus });

    if (eventStatus === 3) {
      event.setValue({ fieldId: "status", value: "CONFIRMED" });
    } else if (eventStatus === 4) {
      event.setValue({ fieldId: "status", value: "CANCELED" });
    } else if (eventStatus === -1) {
      log.audit({ title: "❗ Event status is null", details: eventStatus });
    } else {
      eventTitle = `TENT - ${eventTitle}`;
      event.setValue({ fieldId: "status", value: "TENTATIVE" });
    }

    event
      .setValue({ fieldId: "title", value: eventTitle })
      .setValue({
        fieldId: "startdate",
        value: eventDate,
      })
      .setValue({
        fieldId: "starttime",
        value: startTime,
      })
      .setValue({
        fieldId: "endtime",
        value: endTime,
      });

    try {
      event.save();
    } catch (error) {
      log.error({ title: "🔴 Error updating calendar event", details: error });
    }
  };

  /**
   * Creates a calendar event.
   *
   * @param {Record} currentDateRecord - The record of the current date.
   *
   * @returns {void}
   *
   * @function createCalendarEvent
   *
   * The function begins by retrieving settings with `settings.useSettings()`.
   * It then extracts various details from the `currentDateRecord` argument:
   * eventId, eventDate, startTime, endTime, and dateType.
   *
   * With these data, it constructs the `eventTitle`.
   *
   * Next, it performs a `search.lookupFields` operation to retrieve additional
   * details like accountExecutive and salesRep.
   *
   * With all of these information, it then creates a new `calendarEvent` record, setting
   * various properties and adjusting its attendee and resources.
   *
   * If `accountExecutive` is truthy, it will be added as an attendee of the
   * calendar event. If `salesRep` is truthy and is not the same as `accountExecutive`,
   * it will also be added as an attendee.
   *
   * Finally, it adds a resource to the event using the `custrecord_ng_cs_show_calendar_id`
   * from the CS_SETTINGS, and commits the line.
   */
  const createCalendarEvent = (currentDateRecord) => {
    let calendarEventId = null;
    const CS_SETTINGS = settings.useSettings();
    const eventNumber = currentDateRecord.getText(
      "custrecord_show_number_date",
    );
    const eventId = currentDateRecord.getValue("custrecord_show_number_date");
    const eventDate = currentDateRecord.getValue("custrecord_date");
    const startTime = currentDateRecord.getValue("custrecord_start_time");
    const endTime =
      currentDateRecord.getValue("custrecord_end_time") ||
      getEventDefaultEndTime(eventDate);
    const dateType = currentDateRecord.getValue("custrecord_date_type");
    const dateTypeLabel = currentDateRecord.getText("custrecord_date_type");

    let eventTitle = `${eventNumber} - ${dateTypeLabel}`;

    const eventLookup = search.lookupFields({
      type: "customrecord_show",
      id: eventId,
      columns: [
        "custrecord_acct_exec",
        "custrecord_sales_rep",
        "custrecord_show_status",
      ],
    });

    log.debug({
      title: "🟡 Search lookup info:",
      details: { eventLookup, dateType },
    });

    const accountExecutive = eventLookup.custrecord_acct_exec[0]?.value;
    const salesRep = eventLookup.custrecord_sales_rep[0]?.value;
    const eventStatus =
      eventLookup.custrecord_show_status &&
      Number(eventLookup.custrecord_show_status[0]?.value);

    log.debug({
      title: "🟡 Attendee Info:",
      details: {
        accountExecutive,
        salesRep,
      },
    });

    const calendarEvent = record.create({
      type: record.Type.CALENDAR_EVENT,
      isDynamic: true,
    });

    calendarEvent.setValue({ fieldId: "title", value: eventTitle });
    calendarEvent.setValue({ fieldId: "startdate", value: eventDate });
    calendarEvent.setValue({ fieldId: "starttime", value: startTime });
    calendarEvent.setValue({ fieldId: "endtime", value: endTime });
    calendarEvent.setText({ fieldId: "accesslevel", text: "Public" });

    const attendeesCount = calendarEvent.getLineCount({
      sublistId: "attendee",
    });

    for (let i = attendeesCount - 1; i > 0; i--) {
      calendarEvent.removeLine({
        sublistId: "attendee",
        line: i,
      });
    }

    calendarEvent.selectNewLine({ sublistId: "resource" });
    calendarEvent.setCurrentSublistValue({
      sublistId: "resource",
      fieldId: "resource",
      value: CS_SETTINGS.custrecord_ng_cs_show_calendar_id,
    });
    calendarEvent.commitLine({ sublistId: "resource" });

    if (accountExecutive) {
      try {
        calendarEvent.selectNewLine({ sublistId: "attendee" });
        calendarEvent.setCurrentSublistValue({
          sublistId: "attendee",
          fieldId: "attendee",
          value: accountExecutive,
        });
        calendarEvent.commitLine({ sublistId: "attendee" });
      } catch (err) {
        log.error({
          title:
            "🔴 Error adding account executive as attendee ensure appropriate permissions are set",
          details: err,
        });
      }
    }

    if (salesRep && salesRep !== accountExecutive) {
      try {
        calendarEvent.selectNewLine({ sublistId: "attendee" });
        calendarEvent.setCurrentSublistValue({
          sublistId: "attendee",
          fieldId: "attendee",
          value: salesRep,
        });
        calendarEvent.commitLine({ sublistId: "attendee" });
      } catch (err) {
        log.error({
          title:
            "🔴 Error adding sales rep as attendee ensure appropriate permissions are set",
          details: err,
        });
      }
    }

    try {
      calendarEventId = calendarEvent.save();
      if (calendarEventId) {
        log.audit({
          title: "🟢 Calendar Event Created Successfully!",
          details: `calendarEvent:${calendarEventId}`,
        });

        if (eventStatus === 3) {
          record.submitFields({
            type: "calendarevent",
            id: calendarEventId,
            values: {
              status: "CONFIRMED",
            },
          });
        } else if (eventStatus === 4) {
          record.submitFields({
            type: "calendarevent",
            id: calendarEventId,
            values: {
              status: "CANCELED",
            },
          });
        } else {
          eventTitle = `TENT - ${eventTitle}`;
          record.submitFields({
            type: "calendarevent",
            id: calendarEventId,
            values: {
              status: "TENTATIVE",
              title: eventTitle,
            },
          });
        }

        record.submitFields({
          type: "customrecord_show_date",
          id: currentDateRecord.id,
          values: {
            custrecord_cal_event: calendarEventId,
          },
        });
      }
    } catch (err) {
      log.error({
        title: "🔴 Error occurred saving and updating calendar event:",
        details: err,
      });
    }
  };

  /**
   * This function is used to create labor schedules.
   * It parses the event date and creates labor schedules based on the parsed date and event details.
   * If a labor schedule matching the current day of the week already exists for the given event, it logs an appropriate audit message.
   * If a labor schedule does not exist, it proceeds to create a new one.
   * It also catches and logs any errors that may occur during the labor schedule creation.
   *
   * @param {string} eventId - The id of the event.
   * @param {Date} eventDate - The date of the event.
   * @param {string} dateType - The date type of the event.
   */
  const createLaborSchedules = (eventId, eventDate, dateType) => {
    log.audit({
      title: "📝 Creating Labor Schedules...",
      details: {
        eventId,
        eventDate,
      },
    });
    const COMPANY_PREFERENCES = config.load({
      type: config.Type.COMPANY_PREFERENCES,
    });
    const dateformat = COMPANY_PREFERENCES.getValue("DATEFORMAT");
    const eventDateParsed = moment(eventDate, dateformat);
    const eventDay = eventDateParsed.date();
    const eventDayOfWeek = String(eventDateParsed.day() + 1); // 1 = Sunday, 7 = Saturday
    const eventDateFormatted = eventDateParsed.format(dateformat);

    log.debug({
      title: "📬 Labor date formatted:",
      details: {
        eventDate,
        eventDateParsed,
        eventDay,
        eventDayOfWeek,
        eventDateFormatted,
      },
    });

    const masterScheduleResults = [];
    const laborMasterColumns = [
      search.createColumn({ name: "custrecord_ng_cs_day_of_week" }),
      search.createColumn({ name: "custrecord_ng_cs_master_start_time" }),
      search.createColumn({ name: "custrecord_ng_cs_master_end_time" }),
      search.createColumn({ name: "custrecord_ng_cs_master_labor_type" }),
      search.createColumn({ name: "custrecord_ng_cs_master_rate_multiplier" }),
      search.createColumn({ name: "custrecord_ng_cs_master_super_markup" }),
    ];
    const laborMasterFilters = [
      ["custrecord_ng_cs_day_of_week", "anyof", eventDayOfWeek],
      "and",
      ["isinactive", "is", "F"],
    ];

    const laborMasterSearch = search.create({
      type: "customrecord_ng_cs_show_mstr_labor_schd",
      columns: laborMasterColumns,
      filters: laborMasterFilters,
    });

    const laborMasterCount = laborMasterSearch.runPaged().count;

    log.debug({
      title: "🟡 Searching for master schedule:",
      details: laborMasterCount,
    });

    getAllResultsFor(laborMasterSearch, (result) => {
      masterScheduleResults.push({
        dayOfWeek: parseMultiSelectResult(
          result,
          "custrecord_ng_cs_day_of_week",
        ),
        startTime: result.getValue("custrecord_ng_cs_master_start_time"),
        endTime: result.getValue("custrecord_ng_cs_master_end_time"),
        laborType: result.getValue("custrecord_ng_cs_master_labor_type"),
        rateMultiplier: result.getValue(
          "custrecord_ng_cs_master_rate_multiplier",
        ),
        supervisionMarkup: result.getValue(
          "custrecord_ng_cs_master_super_markup",
        ),
      });
    });

    if (masterScheduleResults.length !== 0) {
      log.audit({
        title:
          "➰ Looping through master schedule to create new dates for event",
        details: masterScheduleResults,
      });

      masterScheduleResults.forEach((schedule) => {
        log.debug({ title: "Checking schedule match:", details: schedule });

        // Check if the labor schedule already exists for the current date
        if (schedule.dayOfWeek.find((day) => day.id === eventDayOfWeek)) {
          // Check the event existing labor schedules for the current date
          // const eventSchedulesExist = checkEventSchedules(
          //   { ...schedule, id: eventId },
          //   eventDateFormatted,
          // );

          // Create the labor schedule
          const laborSchedule = record.create({
            type: "customrecord_ng_cs_show_labor_schedule",
            isDynamic: true,
          });

          const times = {
            startTime: moment(schedule.startTime, "hh:mm a").toDate(),
            endTime: moment(schedule.endTime, "hh:mm a").toDate(),
          };

          log.audit({
            title: "📝 Schedule found setting values:",
            details: schedule,
          });

          const supervisionMarkup = Number.isNaN(
            parseFloat(schedule.supervisionMarkup),
          )
            ? 0
            : parseFloat(schedule.supervisionMarkup);

          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_labor_show",
            value: eventId,
          });
          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_labor_date",
            value: new Date(eventDate),
          });
          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_labor_start",
            value: times.startTime,
          });
          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_labor_end",
            value: times.endTime,
          });
          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_labor_type",
            value: schedule.laborType,
          });
          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_labor_multiplier",
            value: schedule.rateMultiplier,
          });
          laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_supervisor_markup",
            value: supervisionMarkup,
          });
          dateType && laborSchedule.setValue({
            fieldId: "custrecord_ng_cs_date_type",
            value: dateType,
          });

          try {
            laborSchedule.save();
          } catch (error) {
            log.error({
              title: "🔴 Error creating labor schedule",
              details: error,
            });
          }
        } else {
          log.debug({
            title:
              "Master schedule does not include event date scheduled day of week:",
            details: eventDayOfWeek,
          });
        }
      });
    } else {
      log.audit({ title: "🙅 No master labor schedules found", details: "" });
    }
  };

  /**
   * A utility function to check if there are any existing labor schedules for a given event.
   *
   * @param {Object} eventData - An object containing the necessary data to check for existing labor schedules.
   * @param date
   * @returns {boolean} - A boolean value indicating whether or not there are existing labor schedules for the given event.
   */
  const checkEventSchedules = (eventData, date) => {
    let eventChecked = false;
    let scheduleFilters = {
      local: [],
      preferred: [],
    };

    const COMPANY_PREFERENCES = config.load({
      type: config.Type.COMPANY_PREFERENCES,
    });

    const dateformat = COMPANY_PREFERENCES.getValue("DATEFORMAT");

    log.debug({
      title: "📝 Checking labor schedules:",
      details: { eventData, date },
    });

    let dateFormatted = moment(date, dateformat).format("L");

    let errors = {
      dateLocal: null,
      datePreferred: null,
    };

    let searchObjects = {
      local: null,
      preferred: null,
    };

    if (
      eventData?.id &&
      date &&
      eventData?.startTime &&
      eventData?.endTime &&
      eventData?.laborType &&
      eventData?.rateMultiplier &&
      eventData?.supervisionMarkup
    ) {
      scheduleFilters.preferred = [
        ["custrecord_ng_cs_labor_show", "is", eventData.id],
        "and",
        ["custrecord_ng_cs_labor_date", "on", date],
        "and",
        ["custrecord_ng_cs_labor_start", "is", eventData.startTime],
        "and",
        ["custrecord_ng_cs_labor_end", "is", eventData.endTime],
        "and",
        ["custrecord_ng_cs_labor_type", "anyof", [eventData.laborType]],
        "and",
        ["custrecord_ng_cs_labor_multiplier", "is", eventData.rateMultiplier],
        "and",
        [
          "custrecord_ng_cs_supervisor_markup",
          "is",
          eventData.supervisionMarkup,
        ],
      ];
      scheduleFilters.local = [
        ["custrecord_ng_cs_labor_show", "is", eventData.id],
        "and",
        ["custrecord_ng_cs_labor_date", "on", dateFormatted],
        "and",
        ["custrecord_ng_cs_labor_start", "is", eventData.startTime],
        "and",
        ["custrecord_ng_cs_labor_end", "is", eventData.endTime],
        "and",
        ["custrecord_ng_cs_labor_type", "anyof", [eventData.laborType]],
        "and",
        ["custrecord_ng_cs_labor_multiplier", "is", eventData.rateMultiplier],
        "and",
        [
          "custrecord_ng_cs_supervisor_markup",
          "is",
          eventData.supervisionMarkup,
        ],
      ];
    } else {
      scheduleFilters.preferred = [
        search.createFilter({
          name: "custrecord_ng_cs_labor_show",
          operator: search.Operator.IS,
          values: eventData.id,
        }),
        search.createFilter({
          name: "custrecord_ng_cs_labor_date",
          operator: search.Operator.ON,
          values: date,
        }),
      ];
      scheduleFilters.local = [
        search.createFilter({
          name: "custrecord_ng_cs_labor_show",
          operator: search.Operator.IS,
          values: eventData.id,
        }),
        search.createFilter({
          name: "custrecord_ng_cs_labor_date",
          operator: search.Operator.ON,
          values: dateFormatted,
        }),
      ];
    }

    searchObjects.preferred = search.create({
      type: "customrecord_ng_cs_show_labor_schedule",
      filters: scheduleFilters.preferred,
      columns: [],
    });

    try {
      const resultsCount = searchObjects.preferred.runPaged().count;
      eventChecked = resultsCount !== 0;

      log.audit({
        title: "📘 Count of schedule items on event:",
        details: resultsCount,
      });
    } catch (error) {
      log.error({
        title:
          "🔴 Error checking event schedules with preferred date format - running with local date format",
        details: error,
      });

      try {
        errors.datePreferred = error;
        searchObjects.local = search.create({
          type: "customrecord_ng_cs_show_labor_schedule",
          filters: scheduleFilters.local,
          columns: [],
        });

        const resultsCount = searchObjects.local.runPaged().count;
        eventChecked = resultsCount !== 0;

        log.audit({
          title: "📘 Count of schedule items on event:",
          details: resultsCount,
        });
      } catch (err) {
        log.error({
          title: "🔴 Error checking event schedules with local date format",
          details: err,
        });
        errors.dateLocal = err;
      }
    }

    return eventChecked;
  };

  /**
   * This function is used to set event start and end dates based on the event show lookup.
   * Then, it sorts the event show lookup by date and updates the event start and end dates
   * in the customrecord_show record. If no event show dates are found, an audit log is emitted.
   *
   * @param {Object} sc - The object that encapsulates the NetSuite record API.
   * @param {Object} CS_SETTINGS - The setting object containing the default show date.
   * @property {string} CS_SETTINGS.custrecord_ng_cs_default_show_date - The default show date.
   *
   * @returns {void}
   *
   * @example
   * const sc = scriptContext;
   * const CS_SETTINGS = settings.useSettings();
   * setEventStartAndEndDates(sc, CS_SETTINGS);
   */
  const setEventStartAndEndDates = (sc, CS_SETTINGS) => {
    let currRec = sc.newRecord;

    let showType = CS_SETTINGS.custrecord_ng_cs_default_show_date;
    let csEvent = currRec.getValue({ fieldId: "custrecord_show_number_date" });

    let eventShowLookup = query
      .runSuiteQL({
        query: `SELECT custrecord_date
                FROM customrecord_show_date WHERE custrecord_show_number_date  = ${csEvent} AND custrecord_date_type = ${showType}`,
      })
      .asMappedResults();

    eventShowLookup.sort((a, b) => {
      return (
        new Date(a.custrecord_date).getTime() -
        new Date(b.custrecord_date).getTime()
      );
    });

    log.audit({ title: "📝 Adjusting event dates:", details: eventShowLookup });

    if (eventShowLookup.length > 0) {
      if (eventShowLookup.length > 1) {
        record.submitFields({
          type: "customrecord_show",
          id: csEvent,
          values: {
            custrecord_ng_cses_event_start_date: new Date(
              eventShowLookup[0].custrecord_date,
            ),
            custrecord_ng_cses_event_end_date: new Date(
              eventShowLookup[eventShowLookup.length - 1].custrecord_date,
            ),
          },
        });
      }

      if (eventShowLookup.length === 1) {
        record.submitFields({
          type: "customrecord_show",
          id: csEvent,
          values: {
            custrecord_ng_cses_event_start_date: new Date(
              eventShowLookup[0].custrecord_date,
            ),
            custrecord_ng_cses_event_end_date: new Date(
              eventShowLookup[0].custrecord_date,
            ),
          },
        });
      }
    } else {
      log.audit({ title: "No Event Show Dates Found", details: "" });
    }
  };

  /* -------------------------------------------
   * ------------ UTILITY FUNCTIONS -------------
   * --------------------------------------------*/

  /**
   * A utility function to fetch all results from a search object in a paged manner.
   *
   * @param {Object} searchObj - The search object to fetch results from.
   * @param {Function} callback - A function to be called for each result fetched.
   *
   * @returns {undefined} - This function does not return a value.
   */
  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  /**
   * Parses a comma-separated string of values into an array of trimmed strings.
   *
   * @param {string} valuesString - A string containing comma-separated values.
   * @returns {Array<string>} - An array of trimmed strings representing the parsed values.
   */
  function parseMultiSelectQueryValues(valuesString) {
    if (!valuesString) {
      return [];
    }

    // If there is only one value, return it as a single-element array
    if (valuesString.indexOf(",") === -1) {
      return [valuesString.trim()];
    }

    // Split the string into individual values
    var valuesArray = valuesString.split(",");

    // Trim any leading or trailing spaces from each value
    valuesArray = valuesArray.map(function (value) {
      return value.trim();
    });

    return valuesArray;
  }

  /**
   * Parse mulitselect field results from a search.Result object
   * @param {search.Result} result - The search result object
   * @param {string} fieldName - The name of the field to parse
   * @returns {Array} - An array of objects with id and name properties
   * */
  function parseMultiSelectResult(result, searchColName) {
    let resultArray = result.getValue(String(searchColName)).split(","); // Split the string using comma as the delimiter
    let parsedArray = [];

    let getText = result.getText; // Cache the function

    let textArray = getText(String(searchColName)).split(",");
    parsedArray = resultArray.map(function (item, i) {
      return {
        id: item,
        name: textArray[i],
      };
    });

    return parsedArray;
  }

  /**
   * This function converts a percentage string into a decimal value.
   *
   * @param {string} percentString - The percentage string to parse. The string can contain whitespace and/or a percentage sign.
   * @throws {Number} Will return 0 if the percentString is undefined, empty, or not a valid number.
   * @returns {number} The decimal representation of the percentage string. For example, an input of "50%" or "50" will return 0.5.
   *
   * @example
   * // returns 0.5
   * parsePercentString('50%');
   *
   * @example
   * // returns 0
   * parsePercentString('invalid');
   */
  function parsePercentString(percentString) {
    // Check if percentString is undefined or empty
    if (!percentString) {
      return 0;
    }

    // Remove any leading/trailing whitespace and percentage sign
    percentString = percentString.trim().replace("%", "");

    // Parse the numeric value
    const numericValue = parseFloat(percentString);

    // Check if numericValue is NaN (not a valid number)
    if (Number.isNaN(numericValue)) {
      return 0;
    }

    // Convert the percent value to a decimal value
    const decimalValue = numericValue / 100;

    return decimalValue;
  }

  // used to get the default end time for an event when no end time is specified
  function getEventDefaultEndTime(eventDate) {
    return moment(eventDate).add(23, "hours").add(59, "minutes").toDate();
  }

  function triggerAutoBlurbMr (eventId) {

    if (!eventId) {
      log.error('No Event ID defined')

      return;
    }

    let mrTask = task.create({
      taskType: task.TaskType.MAP_REDUCE,
      scriptId: 'customscript_ng_cses_mr_rfrsh_aut_blrb',
      params: {
        custscript_blurb_refresh_event_id: eventId
      }
    })

    try {
      let taskId = mrTask.submit();
      log.debug('Auto Blurb Refresh Task ID:', taskId)
    } catch (e) {
      log.error('Error Creating MR Task', e)
    }

  }

  return { beforeLoad, beforeSubmit, afterSubmit };
});
