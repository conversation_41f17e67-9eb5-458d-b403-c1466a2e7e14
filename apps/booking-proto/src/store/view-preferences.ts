import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import type { ViewType, TimeSlotInterval } from "@/src/types/calendar"
import type { TimelineDuration } from "@/src/components/timeline-toolbar"

interface ViewPreferencesState {
  view: ViewType
  timelineDuration: TimelineDuration
  timeSlotInterval: TimeSlotInterval
  setView: (view: ViewType) => void
  setTimelineDuration: (duration: TimelineDuration) => void
  setTimeSlotInterval: (interval: TimeSlotInterval) => void
}

export const useViewPreferences = create<ViewPreferencesState>()(
  persist(
    (set) => ({
      view: "dayGridWeek", // Default view
      timelineDuration: "week", // Default timeline duration
      timeSlotInterval: "1hour", // Default time slot interval
      setView: (view: ViewType) => set({ view }),
      setTimelineDuration: (duration: TimelineDuration) => set({ timelineDuration: duration }),
      setTimeSlotInterval: (interval: TimeSlotInterval) => set({ timeSlotInterval: interval }),
    }),
    {
      name: "calendar-view-preferences",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
)

