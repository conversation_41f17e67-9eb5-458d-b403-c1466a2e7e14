"use client"

import { useState, useEffect, ReactElement } from "react"
import { useRouter } from "next/navigation"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "src/components/ui/dialog"
import { <PERSON><PERSON> } from "src/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "src/components/ui/table"
import { Input } from "src/components/ui/input"
import { ScrollArea } from "src/components/ui/scroll-area"
import { Badge } from "src/components/ui/badge"
import { DateTimePicker } from "src/components/ui/date-time-picker"
import { <PERSON><PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "src/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "src/components/ui/select"
import { Separator } from "src/components/ui/separator"
import { Textarea } from "src/components/ui/textarea"
import { format } from "date-fns"
import { useBookingCart, type CartItem } from "src/store/booking-cart"
import { useCalendar } from "src/context/calendar-context"
import { CalendarPlus, Edit, Check, Trash2, CalendarCheck, Clock, Building, RefreshCw, X, Info, DollarSign } from "lucide-react"
import { useBookingCartSubmit } from "src/hooks/use-booking-cart-submit"
import { useCSEvents, usePricingRates, getRatesForSpace, getStandardRateType, type RateType, type PricingRate } from "src/hooks/use-booking-settings"
import { cn } from "src/lib/utils"

interface BookingCheckoutModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProcessed?: () => void
}

export function BookingCheckoutModal({ open, onOpenChange, onProcessed }: BookingCheckoutModalProps): ReactElement {
  const router = useRouter()
  const { items, updateItem, removeItem, clearCart } = useBookingCart()
  const { deleteGhostBooking } = useCalendar()
  const submitBookingsMutation = useBookingCartSubmit()
  
  // Load settings data
  const { events: csEvents } = useCSEvents()
  const { rates, rateTypes } = usePricingRates()

  console.log('rates', rates)

  const [selectedEvent, setSelectedEvent] = useState<string>("")
  const [editingItemId, setEditingItemId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>("event")
  const [globalRateType, setGlobalRateType] = useState<string>("")

  // Event details form state
  const [eventName, setEventName] = useState("New Event")
  const [eventType, setEventType] = useState<string[]>([])
  const [account, setAccount] = useState<string>("")
  const [contact, setContact] = useState<string>("")
  const [eventCategory, setEventCategory] = useState<string>("")
  const [businessClassification, setBusinessClassification] = useState<string>("")
  const [hideEventDetails, setHideEventDetails] = useState(false)
  const [showNameOnAvails, setShowNameOnAvails] = useState(false)

  // Set global rate type to "standard" by default when rates are loaded
  useEffect(() => {
    if (rateTypes.length > 0 && !globalRateType) {
      const standardRateType = getStandardRateType(rateTypes)
      if (standardRateType) {
        setGlobalRateType(standardRateType.id)
      }
    }
  }, [rateTypes, globalRateType])

  // Sample account data
  const sampleAccounts = [
    { id: "acc1", name: "Acme Corporation" },
    { id: "acc2", name: "Globex Industries" },
    { id: "acc3", name: "Stark Enterprises" },
  ]

  // Sample contact data
  const sampleContacts = [
    { id: "con1", name: "John Smith" },
    { id: "con2", name: "Jane Doe" },
    { id: "con3", name: "Alex Johnson" },
  ]

  // Sample event types
  const sampleEventTypes = [
    { id: "et1", name: "Conference" },
    { id: "et2", name: "Meeting" },
    { id: "et3", name: "Workshop" },
  ]

  // Sample business classifications
  const sampleBusinessClassifications = [
    { id: "bc1", name: "Corporate" },
    { id: "bc2", name: "Non-profit" },
    { id: "bc3", name: "Government" },
  ]

  // Calculate booking summary data
  const totalBookings = items.length

  // Calculate booking total - in a real app this would have complex pricing logic
  const totalHours = items.reduce((acc, item) => {
    const duration = new Date(item.booking.end).getTime() - new Date(item.booking.start).getTime()
    return acc + duration / (1000 * 60 * 60)
  }, 0)

  // Handle editing a booking
  const handleEditBooking = (item: CartItem, field: string, value: any) => {
    const updates: any = {}

    switch (field) {
      case "title":
        updates.title = value
        break
      case "description":
        updates.description = value
        break
      case "start":
        updates.start = value
        break
      case "end":
        updates.end = value
        break
      case "status":
        updates.status = value
        break
      case "rateType":
        updates.rateTypeId = value
        updates.rateId = undefined
        updates.rate = undefined
        break
      case "rate":
        // value is the rate ID, find the rate object
        const selectedRate = rates.find(r => r.id === value)
        if (selectedRate) {
          updates.rateId = value
          updates.rate = selectedRate.custrecord_ng_cs_space_rate_rate
        }
        break
    }

    updateItem(item.id, updates)
  }

  // Handle global rate type change
  const handleGlobalRateTypeChange = (rateTypeId: string) => {
    setGlobalRateType(rateTypeId)
    
    // Update all items with the new rate type
    items.forEach((item) => {
      updateItem(item.id, {
        rateTypeId: rateTypeId,
        rateId: undefined,
        rate: undefined
      })
    })
  }

  // Handle the checkout process
  const handleCheckout = async () => {
    if (items.length === 0) {
      return
    }

    try {
      // Determine CS Event ID
      let csEventId: string | undefined
      
      if (selectedEvent && selectedEvent !== "new-event") {
        // Use existing event
        csEventId = selectedEvent
      } else if (selectedEvent === "new-event") {
        // In a real implementation, you would first create the new event
        // For now, we'll log the new event details
        console.log("Creating new event:", {
          name: eventName,
          eventType: eventType,
          account: account,
          contact: contact,
          eventCategory: eventCategory,
          businessClassification: businessClassification,
          hideEventDetails: hideEventDetails,
          showNameOnAvails: showNameOnAvails,
        })
        // For demo purposes, we'll proceed without a CS Event ID
        csEventId = undefined
      }

      // Submit the bookings
      await submitBookingsMutation.mutateAsync({ 
        csEventId,
        customerId: account // Use the selected account as customer
      })

      // Delete all ghost bookings after successful submission
      items.forEach((item) => {
        if (item.ghostId) {
          deleteGhostBooking(item.ghostId)
        }
      })

      // Close the modal
      onOpenChange(false)

      // Call the onProcessed callback if provided
      if (onProcessed) {
        onProcessed()
      }
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error("Error processing checkout:", error)
    }
  }

  return (
    <>
      <style jsx>{`
        .booking-table-container {
          scroll-behavior: smooth;
          overscroll-behavior-x: contain;
        }
        .booking-table-container::-webkit-scrollbar {
          height: 8px;
        }
        .booking-table-container::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }
        .booking-table-container::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 4px;
        }
        .booking-table-container::-webkit-scrollbar-thumb:hover {
          background: #555;
        }
      `}</style>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="flex max-h-[90vh] flex-col sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle className="text-2xl">Confirm Your Bookings</DialogTitle>
          <DialogDescription>Review and finalize your bookings</DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex h-full flex-col">
            <TabsList className="mb-4 grid w-full grid-cols-2">
              <TabsTrigger value="event" className="flex items-center gap-1">
                <CalendarCheck className="h-4 w-4" />
                Event Details
              </TabsTrigger>
              <TabsTrigger value="bookings" className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                Bookings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="bookings" className="flex-1 overflow-hidden">
              <div className="space-y-4">
                {/* Global Rate Type Selector */}
                <div className="flex items-center gap-4 rounded-lg bg-muted/50 p-4">
                  <label className="text-sm font-medium">Apply Rate Type to All:</label>
                  <Select value={globalRateType} onValueChange={handleGlobalRateTypeChange}>
                    <SelectTrigger className="h-8 w-[200px]">
                      <SelectValue placeholder="Select rate type for all" />
                    </SelectTrigger>
                    <SelectContent>
                      {rateTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="ml-auto text-sm text-muted-foreground">
                    You can also edit individual rates below
                  </div>
                </div>

                <div className="booking-table-container h-[350px] overflow-auto rounded-md border">
                  <div className="min-w-max">
                    <Table>
                      <TableHeader className="sticky top-0 z-10 bg-background">
                        <TableRow>
                          <TableHead className="w-[120px]">Room</TableHead>
                          <TableHead className="min-w-[200px]">Title</TableHead>
                          <TableHead className="w-[120px]">Date</TableHead>
                          <TableHead className="w-[140px]">Time</TableHead>
                          <TableHead className="w-[100px]">Duration</TableHead>
                          <TableHead className="w-[130px]">Rate Type</TableHead>
                          <TableHead className="w-[120px]">Rate</TableHead>
                          <TableHead className="w-[100px] sticky right-0 bg-background">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {items.map((item) => {
                          const isEditing = editingItemId === item.id
                          const start = new Date(item.booking.start)
                          const end = new Date(item.booking.end)
                          const durationMs = end.getTime() - start.getTime()
                          const durationHours = durationMs / (1000 * 60 * 60)

                                                  return (
                          <TableRow key={item.id} className={isEditing ? "bg-blue-50/50" : ""}>
                            <TableCell className="w-[120px] font-medium">
                              <div className="truncate pr-2" title={item.room.name}>
                                {item.room.name}
                              </div>
                            </TableCell>
                            <TableCell className="min-w-[200px]">
                              {isEditing ? (
                                <Input
                                  className="h-8 w-full max-w-[180px]"
                                  value={item.booking.title}
                                  onChange={(e) => handleEditBooking(item, "title", e.target.value)}
                                />
                              ) : (
                                <div className="truncate pr-2" title={item.booking.title}>
                                  {item.booking.title}
                                </div>
                              )}
                            </TableCell>
                                                          <TableCell className="w-[120px]">
                              {isEditing ? (
                                <Input
                                  type="date"
                                  className="h-8 w-[110px]"
                                  value={format(start, "yyyy-MM-dd")}
                                  onChange={(e) => {
                                    const newDate = new Date(e.target.value)
                                    newDate.setHours(start.getHours(), start.getMinutes())
                                    handleEditBooking(item, "start", newDate)
                                  }}
                                />
                              ) : (
                                <div className="text-sm">{format(start, "MMM d, yyyy")}</div>
                              )}
                            </TableCell>
                            <TableCell className="w-[160px]">
                              {isEditing ? (
                                <div className="flex gap-1">
                                  <Input
                                    type="time"
                                    className="h-8 w-full text-xs"
                                    value={format(start, "HH:mm")}
                                    onChange={(e) => {
                                      const [hours, minutes] = e.target.value.split(":")
                                      const newStart = new Date(start)
                                      newStart.setHours(parseInt(hours || "0"), parseInt(minutes || "0"))
                                      handleEditBooking(item, "start", newStart)
                                    }}
                                  />
                                  <Input
                                    type="time"
                                    className="h-8 w-full text-xs"
                                    value={format(end, "HH:mm")}
                                    onChange={(e) => {
                                      const [hours, minutes] = e.target.value.split(":")
                                      const newEnd = new Date(end)
                                      newEnd.setHours(parseInt(hours || "0"), parseInt(minutes || "0"))
                                      handleEditBooking(item, "end", newEnd)
                                    }}
                                  />
                                </div>
                              ) : (
                                <div className="text-sm">
                                  {format(start, "h:mm a")} - {format(end, "h:mm a")}
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="w-[100px]">
                              <div className="text-sm">{durationHours.toFixed(1)} hrs</div>
                            </TableCell>
                                                          <TableCell className="w-[130px]">
                              {isEditing ? (
                                <Select
                                  value={String(item.booking.rateTypeId || "")}
                                  onValueChange={(value) => handleEditBooking(item, "rateType", value)}
                                >
                                  <SelectTrigger className="h-8 w-[120px]">
                                    <SelectValue placeholder="Type" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {rateTypes.map((type) => (
                                      <SelectItem key={type.id} value={String(type.id)}>
                                        {type.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              ) : (
                                <div className="text-sm">
                                  {rateTypes.find(t => String(t.id) === String(item.booking.rateTypeId))?.name || "-"}
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="w-[120px]">
                              {isEditing && item.booking.rateTypeId ? (
                                <Select
                                  value={item.booking.rateId || ""}
                                  onValueChange={(value) => handleEditBooking(item, "rate", value)}
                                >
                                  <SelectTrigger className="h-8 w-[110px]">
                                    <SelectValue placeholder="Rate" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {getRatesForSpace(rates, item.room.id)
                                      .filter(rate => Number(rate.custrecord_ng_cs_space_rate_type) === Number(item.booking.rateTypeId))
                                      .map((rate) => (
                                        <SelectItem key={rate.id} value={rate.id}>
                                          {rate.name}
                                        </SelectItem>
                                      ))}
                                  </SelectContent>
                                </Select>
                              ) : (
                                <div className="text-sm font-medium">
                                  {item.booking.rate ? `$${item.booking.rate}` : "-"}
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="sticky right-0 w-[100px] bg-background">
                              <div className="flex items-center justify-end space-x-1 pr-2">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => setEditingItemId(isEditing ? null : item.id)}
                                    className="h-8 w-8 p-0"
                                  >
                                    {isEditing ? (
                                      <Check className="h-4 w-4 text-green-600" />
                                    ) : (
                                      <Edit className="h-4 w-4" />
                                    )}
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => removeItem(item.id)}
                                    className="h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                <div className="rounded-lg bg-muted/50 p-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Bookings:</span>
                      <span className="font-medium">{totalBookings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Hours:</span>
                      <span className="font-medium">{totalHours.toFixed(1)} hours</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="event" className="flex-1 overflow-hidden">
              <div className="space-y-4">
                <div className="rounded-lg border border-blue-200 bg-blue-50/50 p-4">
                  <div className="flex gap-2 text-blue-800">
                    <Info className="h-5 w-5 shrink-0 text-blue-500" />
                    <div className="text-sm">
                      <p>
                        Create a new CS Event for your bookings. All selected bookings will be assigned to this event.
                      </p>
                    </div>
                  </div>
                </div>

                <ScrollArea className="h-[350px] px-1">
                  <div className="space-y-5">
                    {/* Event Name */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Name</label>
                      <Input
                        value={eventName}
                        onChange={(e) => setEventName(e.target.value)}
                        placeholder="Event name"
                      />
                    </div>

                    {/* Event Type Checkboxes */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="event-type-inquiry"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes("inquiry")}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, "inquiry"])
                            } else {
                              setEventType(eventType.filter((t) => t !== "inquiry"))
                            }
                          }}
                        />
                        <label htmlFor="event-type-inquiry" className="text-sm">
                          Inquiry (No spaces will be booked)
                        </label>
                      </div>

                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="event-type-prospect"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes("prospect")}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, "prospect"])
                            } else {
                              setEventType(eventType.filter((t) => t !== "prospect"))
                            }
                          }}
                        />
                        <label htmlFor="event-type-prospect" className="text-sm">
                          Prospect
                        </label>
                      </div>

                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="event-type-internal"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes("internal")}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, "internal"])
                            } else {
                              setEventType(eventType.filter((t) => t !== "internal"))
                            }
                          }}
                        />
                        <label htmlFor="event-type-internal" className="text-sm">
                          Internal Event
                        </label>
                      </div>

                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="event-type-blackout"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes("blackout")}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, "blackout"])
                            } else {
                              setEventType(eventType.filter((t) => t !== "blackout"))
                            }
                          }}
                        />
                        <label htmlFor="event-type-blackout" className="text-sm">
                          Blackout
                        </label>
                      </div>
                    </div>

                    {/* Account Dropdown */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Account</label>
                      <Select value={account} onValueChange={setAccount}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an account" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleAccounts.map((acc) => (
                            <SelectItem key={acc.id} value={acc.id}>
                              {acc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Contact Dropdown */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Contact</label>
                      <Select value={contact} onValueChange={setContact}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a contact" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleContacts.map((con) => (
                            <SelectItem key={con.id} value={con.id}>
                              {con.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Event Type Dropdown */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Event Type</label>
                      <Select value={eventCategory} onValueChange={setEventCategory}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an event type" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleEventTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              {type.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Business Classification */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Business Classification</label>
                      <Select value={businessClassification} onValueChange={setBusinessClassification}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a classification" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleBusinessClassifications.map((bc) => (
                            <SelectItem key={bc.id} value={bc.id}>
                              {bc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Visibility */}
                    <div className="space-y-3">
                      <label className="text-sm font-medium">Visibility</label>
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="hide-event-details"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={hideEventDetails}
                          onChange={(e) => setHideEventDetails(e.target.checked)}
                        />
                        <label htmlFor="hide-event-details" className="text-sm">
                          Hide event details
                        </label>
                      </div>

                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="show-name-on-avails"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={showNameOnAvails}
                          onChange={(e) => setShowNameOnAvails(e.target.checked)}
                        />
                        <label htmlFor="show-name-on-avails" className="text-sm">
                          Show name on avails (if confirmed)
                        </label>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <Separator className="my-4" />

        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            onClick={handleCheckout}
            className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700"
            disabled={submitBookingsMutation.isPending || items.length === 0}
          >
            {submitBookingsMutation.isPending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Complete Booking
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  )
}
