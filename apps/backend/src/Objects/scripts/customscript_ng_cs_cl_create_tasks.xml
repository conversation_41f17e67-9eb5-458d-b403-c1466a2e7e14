<clientscript scriptid="customscript_ng_cs_cl_create_tasks">
  <description></description>
  <fieldchangedfunction>clientFieldChanged</fieldchangedfunction>
  <isinactive>F</isinactive>
  <lineinitfunction></lineinitfunction>
  <name>NG CS Client Create Tasks</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <pageinitfunction>clientPageInit</pageinitfunction>
  <postsourcingfunction>clientPostSourcing</postsourcingfunction>
  <recalcfunction></recalcfunction>
  <saverecordfunction>clientSaveRecord</saverecordfunction>
  <scriptfile>[/SuiteScripts/CS UI Script Files/client/ng_cs_clientCreateTasks.js]</scriptfile>
  <validatedeletefunction></validatedeletefunction>
  <validatefieldfunction>clientValidateField</validatefieldfunction>
  <validateinsertfunction></validateinsertfunction>
  <validatelinefunction></validatelinefunction>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
  </libraries>
</clientscript>