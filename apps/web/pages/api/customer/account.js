import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders
} from "@event-services/netsuite-http";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const customer = req.cookies.customer;
    const contact = req.cookies.contact;
    const { type, recid } = req.query;
    const { method } = req;

    switch (method) {
      case "GET": {
        // Validate required parameters
        if (!customer) {
          return res.status(400).json({ error: "Customer ID is required" });
        }

        // Build NetSuite URL
        const url = buildNetSuiteUrl(
          "customscript_rcs_rl_get_account_info",
          "customdeploy_rcs_rl_get_account_info",
          { id: customer, contact }
        );

        // Make request to NetSuite
        const { data, duration } = await makeNetSuiteRequest({
          url,
          method: "GET",
        });

        // Check for application-level errors
        if (data?.error) {
          return res.status(406).json({
            error: {
              title: "Error processing account retrieval request",
              cause: data.error,
            },
          });
        }

        // Set optimized response headers
        setOptimizedHeaders(res, { maxAge: 300 });
        
        // Add performance headers
        addPerformanceHeaders(res, requestStart, duration);

        return res.status(200).json(data);
      }

      case "POST": {
        // POST login functionality can be implemented here
        return res.status(501).json({ error: "POST functionality not yet implemented" });
      }

      case "DELETE": {
        // Validate required parameters
        if (!session.user.id || !type || !recid) {
          return res.status(400).json({ 
            error: "User ID, type, and record ID are required for deletion" 
          });
        }

        // Build NetSuite URL
        const url = buildNetSuiteUrl(
          "customscript_rcs_rl_get_account_info",
          "customdeploy_rcs_rl_get_account_info",
          { uid: session.user.id, type, recid }
        );

        // Make request to NetSuite
        const { data, duration } = await makeNetSuiteRequest({
          url,
          method: "DELETE",
        });

        // Add performance headers
        addPerformanceHeaders(res, requestStart, duration);

        return res.status(200).json(data);
      }

      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    return handleApiError(error, { res: res, requestStart: requestStart });
  }
}

// Disable body parsing for this API route as we don't need it for GET/DELETE
export const config = {
  api: {
    bodyParser: false,
  },
};
