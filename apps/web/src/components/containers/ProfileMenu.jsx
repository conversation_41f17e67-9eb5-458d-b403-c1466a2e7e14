import React from "react";
import PropTypes from "prop-types";
import {
  ExpandLess,
  ExpandMore,
  InfoOutlined,
  SettingsOutlined,
  ShoppingBasketOutlined,
} from "@mui/icons-material";
import {
  Card,
  Collapse,
  Divider,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useTheme } from "@mui/material/styles";

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: 45,
  },
  button: {
    fontFamily: "Roboto, sans-serif",
    fontWeight: 300,
  },
  nested: {
    paddingLeft: theme.spacing(4),
  },
  container: {
    fontFamily: "Roboto, sans-serif",
    fontWeight: 300,
  },
  primary: {
    fontFamily: "Roboto, sans-serif",
    fontWeight: 300,
  },
}));

export default function CustomNestedListTabNav({
  getTabState,
  setCurrentTab,
  toggleMobileDrawer,
}) {
  const classes = useStyles();
  const [selectedIndex, setSelectedIndex] = React.useState(1);
  const [open, setOpen] = React.useState(true);
  const [openOrders, setOpenOrders] = React.useState(true);
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleClick = () => {
    setOpen(!open);
  };

  const handleOrdersClick = () => {
    setOpenOrders(!openOrders);
  };

  const handleListItemClick = (event, index) => {
    setCurrentTab(index);
    if (mobile) {
      toggleMobileDrawer(true); // Close menu on mobile after press
    }
  };

  return (
    <List
      gutterBottom
      component={mobile ? "nav" : Card}
      sx={{ width: mobile ? 300 : "100%" }}
      aria-labelledby="nested-list-subheader"
      subheader={
        <ListSubheader component="div" id="nested-list-subheader">
          <Typography className="font-bold" variant="caption">
            My Account
          </Typography>
        </ListSubheader>
      }
    >
      <ListItemButton
        selected={getTabState === 0}
        onClick={(event) => handleListItemClick(event, 0)}
      >
        <ListItemIcon>
          <InfoOutlined />
        </ListItemIcon>
        <ListItemText primary="Information" />
      </ListItemButton>
      <ListItemButton
        selected={getTabState === 1}
        onClick={(event) => handleListItemClick(event, 1)}
      >
        <ListItemIcon>
          <ShoppingBasketOutlined />
        </ListItemIcon>
        <ListItemText primary="Orders" />
      </ListItemButton>
      <Divider />
      <ListItemButton onClick={handleClick}>
        <ListItemIcon>
          <SettingsOutlined />
        </ListItemIcon>
        <ListItemText primary="Settings" />
        {open ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={open} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          <ListItemButton
            selected={getTabState === 2}
            onClick={(event) => handleListItemClick(event, 2)}
            className={classes.nested}
          >
            <ListItemText primary="Address Book" />
          </ListItemButton>
          <ListItemButton
            selected={getTabState === 3}
            onClick={(event) => handleListItemClick(event, 3)}
            className={classes.nested}
          >
            <ListItemText primary="Payment Methods" />
            {/* <ListItemIcon ><MDBBadge color='warning' style={{float: 'right'}} tag='span'>Testing</MDBBadge></ListItemIcon> */}
          </ListItemButton>
          <ListItemButton
            selected={getTabState === 4}
            onClick={(event) => handleListItemClick(event, 4)}
            className={classes.nested}
          >
            <ListItemText primary="Reset My Password" />
            {/* <ListItemIcon ><MDBBadge color='warning' style={{float: 'right'}} tag='span'>Testing</MDBBadge></ListItemIcon> */}
          </ListItemButton>
        </List>
      </Collapse>
    </List>
  );
}
CustomNestedListTabNav.propTypes = {
  getTabState: PropTypes.number.isRequired,
  setCurrentTab: PropTypes.func.isRequired,
};
