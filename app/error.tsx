"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CalendarIcon, RefreshCw } from "lucide-react"
import { useEffect } from "react"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error)
  }, [error])

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-background p-4">
      <div className="text-destructive mb-4">
        <CalendarIcon size={64} />
      </div>
      <h1 className="text-2xl font-bold text-foreground mb-2">Something went wrong</h1>
      <p className="text-muted-foreground mb-6 text-center max-w-md">
        We're sorry, but there was an error loading the calendar application.
      </p>
      <Button onClick={reset} className="flex items-center gap-2">
        <RefreshCw size={16} />
        Try again
      </Button>
    </div>
  )
}

