/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define(['N/query', 'N/record', 'N/task', 'N/ui/serverWidget', 'N/search'],
    /**
 * @param{query} query
 * @param{record} record
 * @param{task} task
 * @param{serverWidget} serverWidget
 * @param{search} search
 */
    (query, record, task, serverWidget, search) => {
        /**
         * Defines the Suitelet script trigger point.
         * @param {Object} scriptContext
         * @param {ServerRequest} scriptContext.request - Incoming request
         * @param {ServerResponse} scriptContext.response - Suitelet response
         * @since 2015.2
         */
        const onRequest = (scriptContext) => {
            const { request: req, response: res } = scriptContext;
            const { method } = req;

            if (method === "GET") {
                displayForm(req, res);
            }

            if (method === 'POST') {
                processRequest(req, res)
            }

        }

        const displayForm = (request, response, status) => {

            let eventId = request.parameters.custscript_event_id || status.eventId;

            let contactsArray = [];

            let customrecord_show_boothsSearchObj = search.create({
                type: "customrecord_show_booths",
                filters:
                    [
                        ["custrecord_booth_show_table","anyof",eventId]
                    ],
                columns:
                    [
                        search.createColumn({
                            name: "name",
                            sort: search.Sort.ASC,
                            label: "Name"
                        }),
                        search.createColumn({name: "custrecord_booth_actual_exhibitor", label: "Booth Exhibitor"}),
                        search.createColumn({name: "custrecord_booth_contact", label: "Booth Contact"}),
                        search.createColumn({name: "internalid", label: "Internal ID"}),
                        search.createColumn({name: "custrecord_booth_number", label: "Booth Number"}),
                        search.createColumn({name: "custrecord_booth_exhibitor", label: "Booth Billing Party"}),
                        search.createColumn({name: "custrecord_booth_actual_exhibitor", label: "Booth Exhibitor"}),
                        search.createColumn({name: "custrecord_booth_width", label: "Booth Width"}),
                        search.createColumn({name: "custrecord_booth_length", label: "Booth Depth"})
                    ]
            });
            let searchResultCount = customrecord_show_boothsSearchObj.runPaged().count;

            if (searchResultCount !== 0) {
                customrecord_show_boothsSearchObj.run().each(function(result){
                    let returnObj = {};

                    let contact = result.getText({name: 'custrecord_booth_contact'})

                    if (contact) {
                        returnObj.contactName = contact
                        returnObj.contactId = result.getValue({name: 'custrecord_booth_contact'})
                        returnObj.boothName = result.getValue({name: 'name'})
                        returnObj.boothId = result.getValue({name: 'internalid'})
                        returnObj.exhibitorName = result.getText({name: 'custrecord_booth_actual_exhibitor'})
                        returnObj.exhibitorId = result.getValue({name: 'custrecord_booth_actual_exhibitor'})
                        returnObj.boothNum = result.getValue({name: 'custrecord_booth_number'})
                        returnObj.boothBillingParty = result.getValue({name: 'custrecord_booth_exhibitor'})
                        returnObj.boothBillingPartyText = result.getText({name: 'custrecord_booth_exhibitor'})
                        returnObj.boothExhibitor = result.getValue({name: 'custrecord_booth_actual_exhibitor'})
                        returnObj.boothExhibitorText = result.getText({name: 'custrecord_booth_actual_exhibitor'})
                        returnObj.boothWidth = result.getValue({name: 'custrecord_booth_width'})
                        returnObj.boothDepth = result.getValue({name: 'custrecord_booth_length'})

                        let contactEmailSearch = search.lookupFields({
                            type: search.Type.CONTACT,
                            id: result.getValue({name: 'custrecord_booth_contact'}),
                            columns: ['email']
                        }).email

                        returnObj.contactEmail = contactEmailSearch

                        contactsArray.push(returnObj)
                    }

                    return true;
                });
            }

            let form = serverWidget.createForm({
                title: "Update Exhibitor Passwords",
            });

            form.clientScriptModulePath = '../cs-client/ng_cses_slc_update_exhibitor_passwords.js'

            if (status) {
                form.addFieldGroup({
                    id : 'status',
                    label : 'Status'
                });

                let statusField = form.addField({
                    id: "custpage_status",
                    type: "text",
                    label: "Status",
                    container: "status"
                });

                if (status.taskId) {
                    statusField.defaultValue = 'Task Successfully Submitted.'

                    statusField.updateDisplayType({
                        displayType: serverWidget.FieldDisplayType.INLINE
                    });

                    let taskIdField = form.addField({
                        id: "custpage_task_id",
                        type: "text",
                        label: "Task ID",
                        container: "status"
                    });

                    let passwordField = form.addField({
                        id: "custpage_password_status",
                        type: "text",
                        label: "Password Updated To",
                        container: "status"
                    });

                    taskIdField.defaultValue = status.taskId;
                    passwordField.defaultValue = status.password;

                    taskIdField.updateDisplayType({
                        displayType: serverWidget.FieldDisplayType.INLINE
                    });

                    passwordField.updateDisplayType({
                        displayType: serverWidget.FieldDisplayType.INLINE
                    });
                }
            }

            let eventIdField = form.addField({
                id: "custpage_event_id",
                type: serverWidget.FieldType.TEXT,
                label: "Event ID",
            });

            eventIdField.defaultValue = eventId;

            eventIdField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.HIDDEN
            })

            form.addField({
                id: "custpage_new_password",
                type: serverWidget.FieldType.TEXT,
                label: "New Password",
            });

            let sublist = form.addSublist({
                id: "custpage_contacts_list",
                type: serverWidget.SublistType.LIST,
                label: "Booth List",
            });

            sublist.addButton({
                id: "custpage_mark_all_tasks",
                label: "Mark All",
                functionName: "markAllContacts()",
            });

            sublist.addButton({
                id: "custpage_unmark_all_tasks",
                label: "Unmark All",
                functionName: "unmarkAllContacts()",
            });

            sublist.addField({
                id: "custpage_selected",
                type: serverWidget.FieldType.CHECKBOX,
                label: "Select",
            });

            sublist.addField({
                id: "custpage_contact",
                type: serverWidget.FieldType.SELECT,
                label: "Contact",
                source: 'contact'
            });

            let contactEmailField = sublist.addField({
                id: "custpage_contact_email",
                type: serverWidget.FieldType.TEXT,
                label: "Contact Email",
            });

            contactEmailField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.ENTRY
            });

            sublist.addField({
                id: "custpage_exhibitor",
                type: serverWidget.FieldType.TEXT,
                label: "Exhibitor",
            });

            let exhibitorIdField = sublist.addField({
                id: "custpage_exhibitor_id",
                type: serverWidget.FieldType.TEXT,
                label: "Exhibitor",
            });
            exhibitorIdField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.HIDDEN
            })

            let contactIdField = sublist.addField({
                id: "custpage_contact_id",
                type: serverWidget.FieldType.TEXT,
                label: "Exhibitor",
            });
            contactIdField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.HIDDEN
            })

            let contactNameField = sublist.addField({
                id: "custpage_contact_name",
                type: serverWidget.FieldType.TEXT,
                label: "Exhibitor",
            });
            contactNameField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.HIDDEN
            })

            sublist.addField({
                id: "custpage_booth",
                type: serverWidget.FieldType.TEXT,
                label: "Booth",
            });

            let boothId = sublist.addField({
                id: "custpage_booth_id",
                type: serverWidget.FieldType.TEXT,
                label: "Booth",
            });
            boothId.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.HIDDEN
            })

            let boothNumField = sublist.addField({
                id: "custpage_booth_num",
                type: serverWidget.FieldType.TEXT,
                label: "Booth Number",
            });

            boothNumField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.ENTRY
            });

            sublist.addField({
                id: "custpage_booth_billing_party",
                type: serverWidget.FieldType.SELECT,
                label: "Booth Billing Party",
                source: 'customer'
            });

            sublist.addField({
                id: "custpage_booth_exhibitor",
                type: serverWidget.FieldType.SELECT,
                label: "Booth Exhibitor",
                source: 'customer'
            });

            let boothWidthField = sublist.addField({
                id: "custpage_booth_width",
                type: serverWidget.FieldType.TEXT,
                label: "Booth Width",
            });

            boothWidthField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.ENTRY
            })

            let boothDepthField = sublist.addField({
                id: "custpage_booth_depth",
                type: serverWidget.FieldType.TEXT,
                label: "Booth Depth",
            });

            boothDepthField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.ENTRY
            })

            for (let i = 0; i < contactsArray.length; i++) {
                let contactName = contactsArray[i].contactName;
                let boothName = contactsArray[i].boothName;
                let boothId = contactsArray[i].boothId;
                let exhibitorName = contactsArray[i].exhibitorName;
                let exhibitorId = contactsArray[i].exhibitorId;
                let contactId = contactsArray[i].contactId;
                let contactEmail = contactsArray[i].contactEmail;
                let boothNum = contactsArray[i].boothNum;
                let boothBillingParty = contactsArray[i].boothBillingParty;
                let boothBillingPartyText = contactsArray[i].boothBillingPartyText;
                let boothExhibitor = contactsArray[i].boothExhibitor;
                let boothExhibitorText = contactsArray[i].boothExhibitorText;
                let boothWidth = contactsArray[i].boothWidth;
                let boothDepth = contactsArray[i].boothDepth;

                sublist.setSublistValue({
                    id: "custpage_contact",
                    line: i,
                    value: contactId,
                });

                contactEmail && sublist.setSublistValue({
                    id: "custpage_contact_email",
                    line: i,
                    value: contactEmail,
                });

                boothName && sublist.setSublistValue({
                    id: "custpage_booth",
                    line: i,
                    value: boothName,
                });

                boothId && sublist.setSublistValue({
                    id: "custpage_booth_id",
                    line: i,
                    value: boothId,
                });

                exhibitorName && sublist.setSublistValue({
                    id: "custpage_exhibitor",
                    line: i,
                    value: exhibitorName,
                });

                exhibitorId && sublist.setSublistValue({
                    id: "custpage_exhibitor_id",
                    line: i,
                    value: exhibitorId,
                });

                contactId && sublist.setSublistValue({
                    id: "custpage_contact_id",
                    line: i,
                    value: contactId,
                });

                contactName && sublist.setSublistValue({
                    id: "custpage_contact_name",
                    line: i,
                    value: contactName,
                });

                boothNum && sublist.setSublistValue({
                    id: "custpage_booth_num",
                    line: i,
                    value: boothNum,
                });

                boothBillingParty && sublist.setSublistValue({
                    id: "custpage_booth_billing_party",
                    line: i,
                    value: boothBillingParty,
                });

                boothExhibitor && sublist.setSublistValue({
                    id: "custpage_booth_exhibitor",
                    line: i,
                    value: boothExhibitor,
                });

                boothWidth && sublist.setSublistValue({
                    id: "custpage_booth_width",
                    line: i,
                    value: boothWidth,
                });

                boothDepth && sublist.setSublistValue({
                    id: "custpage_booth_depth",
                    line: i,
                    value: boothDepth,
                });

            }

            form.addSubmitButton({
                label: 'Submit'
            });

            response.writePage(form);

        }

        const processRequest = (request, response) => {
            let params = request.parameters;

            let newPassword = params.custpage_new_password;

            let status = {};
            status.eventId = request.parameters.custpage_event_id
            status.password = newPassword;

            let lineCount = request.getLineCount(('custpage_contacts_list'));

            let linesToUpdate = [];

            for (let i = 0; i < lineCount; i++) {
                let selected = request.getSublistValue({
                    group: 'custpage_contacts_list',
                    name: 'custpage_selected',
                    line: i
                });

                if (selected === 'T') {
                    let contactData = {};
                    contactData.contact = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_contact',
                        line: i
                    });
                    contactData.contactId = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_contact_id',
                        line: i
                    })
                    contactData.contactName = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_contact_name',
                        line: i
                    })
                    contactData.exhibitor = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_exhibitor',
                        line: i
                    });
                    contactData.exhibitorId = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_exhibitor_id',
                        line: i
                    });
                    contactData.booth = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth',
                        line: i
                    });
                    contactData.boothId = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth_id',
                        line: i
                    });
                    contactData.contactEmail = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_contact_email',
                        line: i
                    });
                    contactData.boothNum = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth_num',
                        line: i
                    });
                    contactData.boothBillingParty = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth_billing_party',
                        line: i
                    });
                    contactData.boothExhibitor = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth_exhibitor',
                        line: i
                    });
                    contactData.boothWidth = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth_width',
                        line: i
                    });
                    contactData.boothDepth = request.getSublistValue({
                        group: 'custpage_contacts_list',
                        name: 'custpage_booth_depth',
                        line: i
                    });

                    linesToUpdate.push(contactData)
                }
            }

            if (linesToUpdate.length !== 0) {
                let mrTask = task.create({
                    taskType: task.TaskType.MAP_REDUCE,
                    scriptId: 'customscript_ng_cs_mr_exhib_pass_upd',
                    deploymentId: 'customdeploy_ng_cs_mr_exhib_pass_upd',
                    params: {
                        custscript_update_data: JSON.stringify(linesToUpdate),
                        custscript_new_password: newPassword
                    }
                });

                let mrTaskId = mrTask.submit();

                if (mrTaskId) {
                    status.taskId = mrTaskId;

                    displayForm(request, response, status)
                }
            }
        }

        return {onRequest}

    });
