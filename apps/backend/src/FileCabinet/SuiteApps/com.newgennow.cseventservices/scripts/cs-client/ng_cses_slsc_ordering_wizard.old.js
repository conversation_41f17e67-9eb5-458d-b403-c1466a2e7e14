/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 * @NAmdConfig ./amdClientModuleConfig.json
 */
define([
  "N/query",
  "N/record",
  "N/currentRecord",
  "N/runtime",
  "N/search",
  "N/ui/dialog",
  "N/ui/message",
  "N/url",
  "../../packages/@settings/ng_cses_cm_settings_hook",
  "../../packages/@cookies/js.cookie.min",
  "../../packages/@logger/ng_cross_env_logger",
  "../../packages/@lodash/lodash.min",
], /**
 * @param{query} query
 * @param{record} record
 * @param{currentRecord} currentRecord
 * @param{runtime} runtime
 * @param{search} search
 * @param{dialog} dialog
 * @param{message} message
 * @param{url} url
 * @param{Object} settings
 * @param{() => Object} settings.useSettings
 * @param{Cookies} Cookies
 * @param {Logger|function} useLogger
 * @param lodash
 */ function (
  query,
  record,
  currentRecord,
  runtime,
  search,
  dialog,
  message,
  url,
  settings,
  Cookies,
  useLogger,
  lodash,
) {
  let RECORD_MODE = "";
  let RELATED_SALES_ORDER_MESSAGE = null;
  let LOADING_BOOTH_INFO_MESSAGE_BOX = null;
  let NO_EXHIBITOR_ON_BOOTH_WARN_MSG = null;
  let EXISTING_BOOTH_INFO = null;
  let ALLOW_RECORD_SAVE = false;
  let CS_SETTINGS = null;
  let DEFAULT_FORMS = null;
  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(scriptContext) {
    let sc = scriptContext;
    let script = runtime.getCurrentScript();

    let action = script.getParameter({
      name: "custscript_ng_cs_order_direct_action",
    });

    let { mode, currentRecord } = sc;
    RECORD_MODE = mode;
    console.log(`Loaded Old: %c${script.id}`, "padding: 10px; color: lime;");
    console.log(`Mode: %c${mode}`, "padding: 10px; color: magenta;");
    CS_SETTINGS = settings.useSettings();
    let boothField = currentRecord.getField({
      fieldId: "custpage_ng_cs_booth",
    });
    let retainLastShowEnabled =
      CS_SETTINGS.custrecord_ng_cs_retain_last_show === "T";

    let lastShow = Cookies.get("lastShow");


    DEFAULT_FORMS = {
      defaultBoothOrderForm: resolveSelectValue(
        CS_SETTINGS.custrecord_ng_cs_dflt_booth_order_form,
      ),
      defaultShowMgmtForm: resolveSelectValue(
        CS_SETTINGS.custrecord_ng_cs_dflt_show_mgmt_ord_form,
      ),
      defaultExhibitorOrderType: resolveSelectValue(
        CS_SETTINGS.custrecord_ng_cs_def_exhb_ord_type,
      ),
      defaultShowMgmtOrderType: resolveSelectValue(
        CS_SETTINGS.custrecord_ng_cs_def_show_mgmt_ord_type,
      ),
    };

    if (CS_SETTINGS.custrecord_ng_cs_prev_adtl_orders === "T" && boothField) {
      message
        .create({
          type: message.Type.INFORMATION,
          title: "Prevent Additional Booth Orders Enabled",
          message: `<html><p>In attempt to create an order, if the booth selected has an existing order a link to the order record will be presented.</p></html>`,
        })
        .show(10000);
    }

    if (retainLastShowEnabled) {
      // Set the event to the last event selected if cookie is present
      if (lastShow) {
        currentRecord.setValue({
          fieldId: "custpage_ng_cs_event",
          value: lastShow,
        });
      }
    }

    // Set message value to show later
    LOADING_BOOTH_INFO_MESSAGE_BOX = message.create({
      title: "Loading Booth Information...",
      message: `<html>
                    <style>
                    /* Progress Bar */
                    .progress {
                      position: relative;
                      height: 4px;
                      display: block;
                      width: 100%;
                      background-color: #acece6;
                      border-radius: 2px;
                      background-clip: padding-box;
                      margin: 0.5rem 0 1rem 0;
                      overflow: hidden; 
                      }
                      .progress .indeterminate {
                        background-color: #26a69a;
                     }
                     
                    .progress .indeterminate:before {
                          content: '';
                          position: absolute;
                          background-color: inherit;
                          top: 0;
                          left: 0;
                          bottom: 0;
                          will-change: left, right;
                          -webkit-animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
                                  animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
                    }
                    .progress .indeterminate:after {
                          content: '';
                          position: absolute;
                          background-color: inherit;
                          top: 0;
                          left: 0;
                          bottom: 0;
                          will-change: left, right;
                          -webkit-animation: indeterminate-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
                                  animation: indeterminate-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
                          -webkit-animation-delay: 1.15s;
                                  animation-delay: 1.15s;
                   }
                    
                    @-webkit-keyframes indeterminate {
                      0% {
                        left: -35%;
                        right: 100%;
                         }
                      60% {
                        left: 100%;
                        right: -90%; 
                        }
                      100% {
                        left: 100%;
                        right: -90%;
                         } 
                    }
                    @keyframes indeterminate {
                      0% {
                        left: -35%;
                        right: 100%;
                         }
                      60% {
                        left: 100%;
                        right: -90%; 
                        }
                      100% {
                        left: 100%;
                        right: -90%; 
                        } 
                    }
                    @-webkit-keyframes indeterminate-short {
                      0% {
                        left: -200%;
                        right: 100%; 
                        }
                      60% {
                        left: 107%;
                        right: -8%;
                         }
                      100% {
                        left: 107%;
                        right: -8%; 
                        } 
                    }
                    @keyframes indeterminate-short {
                      0% {
                        left: -200%;
                        right: 100%; 
                        }
                      60% {
                        left: 107%;
                        right: -8%; 
                        }
                      100% {
                        left: 107%;
                        right: -8%; } 
                        }
                    
                    </style>
                    <div class="progress">
                      <div class="indeterminate"></div>
                    </div>
                </html>`,
      type: message.Type.INFORMATION,
    });
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(scriptContext) {
    let sc = scriptContext;

    let { fieldId, currentRecord } = sc;
    let eventField = currentRecord.getField({
      fieldId: "custpage_ng_cs_event",
    });
    let boothField = currentRecord.getField({
      fieldId: "custpage_ng_cs_booth",
    });

    if (fieldId === "custpage_ng_cs_event" && boothField) {
      let eventId = currentRecord.getValue({ fieldId: "custpage_ng_cs_event" });

      console.log(
        `Event ID Selected: %c${eventId}`,
        "padding: 10px; color: cyan;",
      );
      // Clear current state of select options
      boothField.removeSelectOption({
        value: null,
      });

      // Search for booths with the selected event
      let eventWithBooths = search.create({
        type: "customrecord_show_booths",
        columns: [
          search.createColumn({
            name: "custrecord_booth_show_table",
            label: "Event",
          }),
          search.createColumn({
            name: "name",
            label: "Booth Name",
          }),
        ],
        filters: [["custrecord_booth_show_table", "anyof", eventId]],
      });

      boothField.insertSelectOption({
        value: "",
        text: "",
      });

      // Add booths depending on event selection
      getAllResultsFor(eventWithBooths, (result) => {
        let boothId = result.id;
        let boothName = result.getValue({
          name: "name",
        });

        boothField.insertSelectOption({
          value: boothId,
          text: boothName,
        });
      });
    } else if (boothField && fieldId === "custpage_ng_cs_booth") {
      let boothId = currentRecord.getValue({ fieldId: "custpage_ng_cs_booth" });
      console.log(
        `Booth ID Selected: %c${boothId}`,
        "padding: 10px; color: cyan;",
      );

      handleBoothChange(sc);
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(scriptContext) {}

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(scriptContext) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(scriptContext) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(scriptContext) {}

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(scriptContext) {}

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(scriptContext) {}

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(scriptContext) {}

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(scriptContext) {
    let { currentRecord } = scriptContext;
    let boothField = currentRecord.getField({
      fieldId: "custpage_ng_cs_booth",
    });
    let canSave = true;
    let retainLastShowEnabled =
      CS_SETTINGS.custrecord_ng_cs_retain_last_show === "T";

    // Update last show cookie upon save
    if (retainLastShowEnabled) {
      Cookies.set("lastShow", currentRecord.getValue("custpage_ng_cs_event"), {
        path: "/",
      });
    }

    if (boothField) {
      canSave = !EXISTING_BOOTH_INFO;
    }

    return canSave;
  }

  const getAllResultsFor = (searchObj, callback) => {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  };

  async function handleBoothChange(sc) {
    let { currentRecord, fieldId } = sc;
    let eventId = currentRecord.getValue({ fieldId: "custpage_ng_cs_event" });
    let boothId = currentRecord.getValue({ fieldId: "custpage_ng_cs_booth" });

    if (fieldId === "custpage_ng_cs_booth") {
      let boothId = currentRecord.getValue({ fieldId: "custpage_ng_cs_booth" });
      console.log(
        `Booth ID Selected: %c${boothId}`,
        "padding: 10px; color: cyan;",
      );

      let boothLookup = search.lookupFields({
        type: "customrecord_show_booths",
        id: boothId,
        columns: [
          "custrecord_booth_number",
          "custrecord_booth_actual_exhibitor",
          "custrecord_booth_exhibitor",
        ],
      });

      let boothNumber = boothLookup.custrecord_booth_number;
      let boothActualExhibitor = boothLookup.custrecord_booth_actual_exhibitor;
      let boothExhibitor = boothLookup.custrecord_booth_exhibitor;

      console.log(
        `Booth Number: %c${boothNumber}`,
        "padding: 10px; color: cyan;",
      );
      console.log(
        `Booth Actual Exhibitor: %c${boothActualExhibitor}`,
        "padding: 10px; color: cyan;",
      );
      console.log(
        `Booth Exhibitor: %c${boothExhibitor}`,
        "padding: 10px; color: cyan;",
      );

      console.log("Booth Exhibitor is NaN?", Number(isNaN(boothExhibitor)));

      if (Number(isNaN(boothExhibitor)) === 0) {
        message
          .create({
            type: message.Type.WARNING,
            title: "No Exhibitor Assigned To Booth",
            message: `No Exhibitor is assigned to Booth ${boothNumber}. Please assign an Exhibitor to this Booth before creating a Sales Order.`,
          })
          .show(15000);
      } else {
        let preventAddBoothOrders = search.lookupFields({
          type: 'customrecord_ng_cs_settings',
          id: 1,
          columns: ['custrecord_ng_cs_prev_adtl_orders']
        }).custrecord_ng_cs_prev_adtl_orders

        if (preventAddBoothOrders) {
          await findRelatedBoothOrders(
            sc,
            search,
            message,
            url,
            eventId,
            boothId,
            boothExhibitor,
            boothNumber,
          ).then((results) => {
            console.log("Existing booth order results:", results);
            if (results.length > 0) {
              EXISTING_BOOTH_INFO = results[0]; // Grab the first result
            } else {
              EXISTING_BOOTH_INFO = null;
            }
          });
        }
      }
    }
  }

  async function findRelatedBoothOrders(
    sc,
    search,
    message,
    url,
    eventId,
    boothId,
    entityId,
    boothNumber,
  ) {
    return new Promise((resolve, reject) => {
      console.log("Getting related booth orders...", boothId);
      var currentSalesOrder = sc.currentRecord;
      var currentSalesOrderTranId = currentSalesOrder.getValue("tranid");
      var relatedSalesOrderUrl = "";
      var salesOrderResultsCount = 0;
      var salesOrderResults = [];
      var salesOrderSearchObj = search.create({
        type: search.Type.SALES_ORDER,
        columns: [
          search.createColumn({
            name: "tranid",
            label: "Transaction ID",
          }),
        ],
        filters: [
          search.createFilter({
            name: "custbody_show_table",
            operator: search.Operator.ANYOF,
            values: eventId,
          }),
          search.createFilter({
            name: "custbody_booth",
            operator: search.Operator.ANYOF,
            values: boothId,
          }),
          search.createFilter({
            name: "entity",
            operator: search.Operator.ANYOF,
            values: entityId,
          }),
          search.createFilter({
            name: "mainline",
            operator: search.Operator.IS,
            values: true,
          }),
        ],
      });
      console.log("%cRunning booth sales order search...", "color: cyan", "⌛");
      salesOrderResultsCount = salesOrderSearchObj.runPaged().count;

      console.info("Sales order count:", salesOrderResultsCount);

      if (RELATED_SALES_ORDER_MESSAGE) {
        console.log(
          "Message being hidden initial:",
          RELATED_SALES_ORDER_MESSAGE,
        );
        RELATED_SALES_ORDER_MESSAGE.hide();
        RELATED_SALES_ORDER_MESSAGE = null;
      }

      if (LOADING_BOOTH_INFO_MESSAGE_BOX) {
        LOADING_BOOTH_INFO_MESSAGE_BOX.hide();
      }

      // Do not add results to array if none are found
      if (salesOrderResultsCount !== 0) {
        salesOrderResults = salesOrderSearchObj
          .run()
          .getRange(0, 1000)
          .map((so) => ({
            id: so.id,
            tranid: so.getValue("tranid"),
          }));
      }

      // If sales order count is not 0 check if current order is result - if not allow save of the record
      if (
        salesOrderResultsCount !== 0 &&
        salesOrderResults[0].tranid !== currentSalesOrderTranId
      ) {
        let editSalesOrderUrl = resolveRecordUrl(
          url,
          "salesorder",
          salesOrderResults[0].id,
          true,
        );

        relatedSalesOrderUrl = resolveRecordUrl(
          url,
          "salesorder",
          salesOrderResults[0].id,
        );

        RELATED_SALES_ORDER_MESSAGE = message.create({
          title: `Cannot create a new order as one already exists for booth ${boothNumber}!`,
          message: `<html><p>Please change booth or edit the existing order <a class="font-medium underline" href="${relatedSalesOrderUrl}">${salesOrderResults[0].tranid}</a>&nbsp;<a href="${editSalesOrderUrl}">(EDIT)</a></p></html>`,
          type: message.Type.ERROR,
        });

        // Display booth with order alert
        RELATED_SALES_ORDER_MESSAGE.show();

        ALLOW_RECORD_SAVE = false;

        console.log("Sales order related to booth results:", salesOrderResults);
        LOADING_BOOTH_INFO_MESSAGE_BOX && LOADING_BOOTH_INFO_MESSAGE_BOX.hide();

        resolve(salesOrderResults);
      } else {
        // Clear message if one is present
        console.log("Message defined?:", RELATED_SALES_ORDER_MESSAGE);
        if (RELATED_SALES_ORDER_MESSAGE) {
          console.log("Message being hidden:", RELATED_SALES_ORDER_MESSAGE);
          RELATED_SALES_ORDER_MESSAGE.hide();
          RELATED_SALES_ORDER_MESSAGE = null;
        }

        if (LOADING_BOOTH_INFO_MESSAGE_BOX) {
          LOADING_BOOTH_INFO_MESSAGE_BOX.hide();
        }

        ALLOW_RECORD_SAVE = true;
        console.info(
          "%cSales order count is ZERO no need to render msg.",
          "font-weight: bold; color: orange",
        );
        resolve(salesOrderResults);
      }
    });
  }

  async function handleBoothOrderSaveOperations(sc, search, message, url) {
    console.log("⚡ Running Booth Order Save Operations...");
    let currentSalesOrder = sc.currentRecord;
    let eventId = EVENT_RECORD && EVENT_RECORD.id;
    let boothId = currentSalesOrder.getValue("custbody_booth");
    let entity = currentSalesOrder.getValue("entity");
    let willSave = false;
    let preventAdditionalOrders =
      CS_SETTINGS.custrecord_ng_cs_prev_adtl_orders === "T";
    if (preventAdditionalOrders) {
      await findRelatedBoothOrders(
        sc,
        search,
        message,
        url,
        eventId,
        boothId,
        entity,
      )
        .then((res) => {
          console.log("Ran booth check...", res);
          willSave = !(res && res.length !== 0);
        })
        .catch((err) => {
          console.error("A problem occurred with booth order lookup...", err);
          willSave = false;
        });
    } else {
      ALLOW_RECORD_SAVE = true;
    }

    return willSave;
  }

  function resolveRecordUrl(url, type, id, editMode = false) {
    let scheme = "https://";
    let host = url.resolveDomain({
      hostType: url.HostType.APPLICATION,
    });
    let relativePath = url.resolveRecord({
      recordType: type,
      recordId: id,
      isEditMode: editMode,
    });
    let newUrl = scheme + host + relativePath;

    return newUrl;
  }

  function handleNewPostToOrderPage(action) {
    const currentRec = currentRecord.get();
    let logger = useLogger();
    let EVENT_RECORD = null;
    let addressObject = {
      id: "",
      city: "",
      zip: "",
      state: "",
      addressOne: "",
      addressTwo: "",
      country: "",
      phone: "",
    };

    // const logger = crossLogger.useCrossLog();

    logger.log(
      "⚡ Setting Shipping address upon event change - matching venue address...",
    );

    const eventId = currentRec.getValue("custpage_ng_cs_event");
    const boothId = currentRec.getValue("custpage_ng_cs_booth");

    if (!boothId || !eventId) {
      message
        .create({
          title: "Missing Fields",
          message: "Please select a booth and event before proceeding",
          type: message.Type.ERROR,
        })
        .show(10000);
      return;
    }

    let shippingAddress = {};
    // Change the shipping address to match the venue

    if (eventId) {
      EVENT_RECORD = record.load({
        type: "customrecord_show",
        id: eventId,
      });

      if (EVENT_RECORD) {
        logger.audit("✅ Event loaded: ", EVENT_RECORD.id);
        let venueId = Number(EVENT_RECORD.getValue("custrecord_facility"));

        logger.audit("🔎 Event venue: ", venueId);
        logger.audit("🔎 Event venue is array: ", Array.isArray(venueId));

        // Load Venus address
        let venueRecord = record.load({
          type: "customrecord_facility",
          id: venueId,
          isDynamic: true,
        });

        let boothRecord = null;

        if (venueRecord) {
          logger.audit("✅ Venue loaded: ", venueRecord.id);

          // Set shipping address to facility location
          let state = venueRecord.getText("custrecord_facility_state");
          let zip = venueRecord.getValue("custrecord_facility_zip");
          let addressOne = venueRecord.getValue("custrecord_facility_address1");
          let addressTwo = venueRecord.getValue("custrecord_facility_address2");
          let city = venueRecord.getValue("custrecord_facility_city");
          let country = venueRecord.getValue("custrecord_facility_country");
          let phone = venueRecord.getValue("custrecord_facility_phone");
          let name = venueRecord.getValue("name");

          logger.audit("🏦 Venue address: ", addressOne);
          logger.audit("🏦 Venue address 2: ", addressTwo);
          logger.audit("🏦 Venue city: ", city);
          logger.audit("🏦 Venue state: ", state);
          logger.audit("🏦 Venue zip: ", zip);
          logger.audit("🏦 Venue country: ", country);
          logger.audit("🏦 Venue phone: ", phone);

          addressObject.id = venueId;
          addressObject.country = country || "230"; // Default to US
          addressObject.addressOne = addressOne;
          addressObject.addressTwo = addressTwo;
          addressObject.city = city;
          addressObject.state = state;
          addressObject.zip = zip;
          addressObject.phone = phone;
          addressObject.attention = name;

          log.audit({
            title: "🏦 Address object created!",
            details: "Setting shipping address on Sales order",
          });

          log.audit({
            title: "🏦 Address object: ",
            details: JSON.stringify(addressObject, null, 2),
          });
        } else {
          log.audit({ title: "❌ Venue not loaded: ", details: "" });
        }

        // Set country field first when script uses dynamic mode
        // Setting text expects literal name ie=United States
        // Setting value expects abbreviation ie=US

        let shippingAddressCountryId = query
          .runSuiteQL({
            query: `SELECT id FROM country WHERE uniquekey = '${addressObject.country}'`,
          })
          .asMappedResults();

        shippingAddressCountryId =
          shippingAddressCountryId.length !== 0
            ? shippingAddressCountryId[0].id
            : "";

        let shippingAddressStateQuery = `SELECT shortname FROM state WHERE fullname = '${addressObject.state}' AND country = '${shippingAddressCountryId}'`;

        log.audit({
          title: "🌕 State Query:",
          details: shippingAddressStateQuery,
        });

        let shippingAddressStateId = query
          .runSuiteQL({
            query: shippingAddressStateQuery,
          })
          .asMappedResults();

        shippingAddressStateId =
          shippingAddressStateId.length !== 0
            ? shippingAddressStateId[0].shortname
            : "";

        if (shippingAddressStateId === "" || shippingAddressCountryId === "") {
          throw `An invalid state or country was provided to the venue to apply the appropriate tax group from venue address "${addressObject.attention}". Please check your venue address for the event you selected and try again!`;
        }

        // If anything but the US is selected, don't commit the address
        // But set the nexus field to the state
        let urlSearch = {
          selectedtab: "shipping",
          shipaddresslist: "cust",
          shipisresidential: "F",
          shipaddr1: addressObject.addressOne,
          shipaddr2: encodeURIComponent(addressObject.addressTwo),
          shipcity: addressObject.city,
          shipstate: encodeURIComponent(shippingAddressStateId),
          shipzip: encodeURIComponent(addressObject.zip),
          shipcountry: shippingAddressCountryId,
        };

        // No need to set addressee it will default to John Doe
        /* shippingAddress.setValue({
				   fieldId: 'addressee',
				   value: ship_to_address.addressee_initialvalue
			   })*/

        logger.log("🚚 Shipping address params ready!", urlSearch);

        log.audit({
          title: "🚚 Shipping address set!",
          details: urlSearch,
        });

        const mainUrl = url.resolveDomain({
          hostType: url.HostType.APPLICATION,
        });

        logger.log("🚚 Booth and event information", mainUrl);

        if (action === "booth") {
          boothRecord = record.load({
            type: "customrecord_show_booths",
            id: boothId,
          });

          let billingParty = boothRecord.getValue("custrecord_booth_exhibitor");
          let boothExhibitor = boothRecord.getValue(
            "custrecord_booth_actual_exhibitor",
          );

          const salesOrderUrl = url.format({
            domain: `https://${mainUrl}/app/accounting/transactions/salesord.nl`,
            params: {
              cf: DEFAULT_FORMS.defaultBoothOrderForm,
              entity: billingParty,
              "record.custbody_booth": boothId,
              "record.custbody_show_table": eventId,
              "record.custbody_booth_actual_exhibitor": boothExhibitor,
              shipaddresslist: "cust",
              shipisresidential: "F",
              billingaccount: -1,
              shipcountry: shippingAddressCountryId,
              shipstate: shippingAddressStateId,
              shipaddr1: addressObject.addressOne,
              shipaddr2: addressObject.addressTwo,
              shipcity: addressObject.city,
              shipzip: addressObject.zip,
              shipphone: addressObject.phone,
              shipaddressee: addressObject.addressee,
              shipoverride: "F",
              shipattention: addressObject.attention,
            },
          });

          window.open(salesOrderUrl, "_blank");

          message
            .create({
              title: "New Booth Order Opened",
              message: `<html><p>A new booth order window has been opened for booth ${boothRecord.getValue("name")}.</p></html>`,
              type: message.Type.CONFIRMATION,
            })
            .show(10000);
        } else if (action === "mgmt") {
          const salesOrderUrl = url.format({
            domain: `https://${mainUrl}/app/accounting/transactions/salesord.nl`,
            params: {
              cf: DEFAULT_FORMS.defaultShowMgmtForm,
              entity: boothId,
              "record.custbody_show_table": eventId,
              shipaddresslist: "cust",
              shipisresidential: "F",
              billingaccount: -1,
              shipaddr1: addressObject.addressOne,
              shipaddr2: addressObject.addressTwo,
              shipcity: addressObject.city,
              shipstate: shippingAddressStateId,
              shipzip: addressObject.zip,
              shipcountry: shippingAddressCountryId,
              shipphone: addressObject.phone,
              shipaddressee: addressObject.addressee,
              shipoverride: "F",
              shipattention: addressObject.attention,
            },
          });

          window.open(salesOrderUrl, "_blank");

          message
            .create({
              title: "New Show Management Order Opened",
              message: `<html><p>A new show management order window has been opened for booth ${EVENT_RECORD.getValue("name")}.</p></html>`,
              type: message.Type.CONFIRMATION,
            })
            .show(10000);
        }
      }
    }

    return shippingAddress;
  }

  function resolveSelectValue(objValue) {
    var selVal = null;
    if (!lodash.isEmpty(objValue) && !lodash.isEmpty(objValue[0])) {
      selVal = objValue[0].value;
    }
    return selVal;
  }

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    // sublistChanged: sublistChanged,
    // lineInit: lineInit,
    // validateField: validateField,
    // validateLine: validateLine,
    // validateInsert: validateInsert,
    // validateDelete: validateDelete,
    handleNewPostToOrderPage: handleNewPostToOrderPage,
    saveRecord: saveRecord,
  };
});
