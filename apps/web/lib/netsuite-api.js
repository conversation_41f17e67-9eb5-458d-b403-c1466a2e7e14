import axios from "axios";
import OAuth from "oauth-1.0a";
import crypto from "crypto";
import pRetry from "p-retry";

// OAuth configuration
const oauth = OAuth({
  consumer: {
    key: process.env.OAUTH1_CONSUMER_KEY,
    secret: process.env.OAUTH1_CONSUMER_SECRET,
  },
  signature_method: "HMAC-SHA256",
  hash_function(base_string, key) {
    return crypto
      .createHmac("sha256", key)
      .update(base_string)
      .digest("base64");
  },
  realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
});

// Create axios instance with optimized configuration
const axiosInstance = axios.create({
  timeout: 15000, // 15 second timeout
  headers: {
    "Content-Type": "application/json",
    "Accept-Encoding": "gzip, deflate", // Enable compression
  },
  // Keep connections alive for better performance
  httpAgent: new (require("http").Agent)({ keepAlive: true }),
  httpsAgent: new (require("https").Agent)({ keepAlive: true }),
  maxRedirects: 5,
  validateStatus: (status) => status < 500, // Don't throw on 4xx errors
});

// Performance monitoring helper
const measurePerformance = async (operation, fn) => {
  const startTime = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    
    // Log performance metrics in development
    if (process.env.NODE_ENV === "development") {
      console.log(`[Performance] ${operation} completed in ${duration}ms`);
    }
    
    return { result, duration };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Performance] ${operation} failed after ${duration}ms`);
    throw error;
  }
};

/**
 * Make an optimized request to NetSuite API
 * @param {Object} options - Request options
 * @param {string} options.url - Full NetSuite API URL
 * @param {string} options.method - HTTP method (GET, POST, PUT, DELETE)
 * @param {Object} [options.body] - Request body for POST/PUT requests
 * @param {Object} [options.headers] - Additional headers
 * @param {number} [options.retries=3] - Number of retry attempts
 * @returns {Promise<Object>} Response data
 */
export const makeNetSuiteRequest = async (options) => {
  const {
    url,
    method = "GET",
    body = null,
    headers = {},
    retries = 3,
  } = options;

  // Generate OAuth authorization header
  const request_data = { url, method };
  const token = {
    key: process.env.OAUTH1_ACCESS_TOKEN,
    secret: process.env.OAUTH1_TOKEN_SECRET,
  };

  const authHeader = oauth.toHeader(oauth.authorize(request_data, token));

  // Prepare request configuration
  const requestConfig = {
    method,
    url,
    headers: {
      ...authHeader,
      Authorization: authHeader.Authorization,
      ...headers,
    },
  };

  // Add body for non-GET requests
  if (body && (method === "POST" || method === "PUT" || method === "PATCH")) {
    requestConfig.data = body;
  }

  // Make the request with retry logic
  const fetchData = async () => {
    const response = await axiosInstance(requestConfig);

    // Only accept 200 responses as successful for most NetSuite APIs
    if (response.status !== 200) {
      const error = new Error(`NetSuite API returned status ${response.status}`);
      error.response = response;
      throw error;
    }

    return response;
  };

  // Use p-retry for automatic retries on transient failures
  const { result: response, duration } = await measurePerformance(
    `NetSuite API ${method} ${url}`,
    () => pRetry(fetchData, {
      retries,
      minTimeout: 1000, // Start with 1 second delay
      maxTimeout: 3000, // Max 3 seconds between retries
      onFailedAttempt: (error) => {
        console.log(
          `NetSuite API attempt ${error.attemptNumber} failed. ${error.retriesLeft} retries left.`
        );
      },
      // Don't retry on 4xx errors (client errors)
      shouldRetry: (error) => {
        if (error.response && error.response.status >= 400 && error.response.status < 500) {
          return false;
        }
        return true;
      },
    })
  );

  return {
    data: response.data,
    status: response.status,
    headers: response.headers,
    duration,
  };
};

/**
 * Build NetSuite RESTlet URL with query parameters
 * @param {string} script - Script ID
 * @param {string} deploy - Deploy ID
 * @param {Object} [params={}] - Query parameters
 * @returns {string} Complete URL
 */
export const buildNetSuiteUrl = (script, deploy, params = {}) => {
  const baseUrl = `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl`;
  const queryParams = new URLSearchParams({
    script,
    deploy,
    ...params,
  });
  return `${baseUrl}?${queryParams.toString()}`;
};

/**
 * Handle API errors consistently
 * @param {Error} error - The error object
 * @param {Object} res - Next.js response object
 * @param {number} requestStart - Request start timestamp
 */
export const handleApiError = (error, res, requestStart) => {
  console.error("NetSuite API Error:", error.message);

  // Add performance header even on errors
  if (requestStart) {
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
  }

  // Handle specific error types
  if (error.code === "ECONNABORTED" || error.code === "ETIMEDOUT") {
    return res.status(504).json({
      error: "Request timeout - NetSuite API is taking too long to respond",
    });
  }

  if (error.response) {
    // NetSuite returned an error response
    return res.status(error.response.status || 500).json({
      error: "Error from NetSuite API",
      details: error.response.data,
    });
  }

  // Generic error response
  return res.status(500).json({
    error: "Internal server error while processing request",
    message: process.env.NODE_ENV === "development" ? error.message : undefined,
  });
};

/**
 * Set optimized response headers
 * @param {Object} res - Next.js response object
 * @param {number} [cacheMaxAge=300] - Cache max age in seconds
 * @param {number} [staleWhileRevalidate=600] - Stale while revalidate in seconds
 */
export const setOptimizedHeaders = (res, cacheMaxAge = 300, staleWhileRevalidate = 600) => {
  res.setHeader(
    "Cache-Control",
    `private, max-age=${cacheMaxAge}, stale-while-revalidate=${staleWhileRevalidate}`
  );
  res.setHeader("X-Content-Type-Options", "nosniff");
}; 