<clientscript scriptid="customscript_ng_cs_cl_exhib_wrk_ord_rprt">
  <description></description>
  <fieldchangedfunction>clientFieldChanged</fieldchangedfunction>
  <isinactive>F</isinactive>
  <lineinitfunction>clientLineInit</lineinitfunction>
  <name>NG CS Client Exhibitor Work Order Report</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <pageinitfunction>clientPageInit</pageinitfunction>
  <postsourcingfunction>clientPostSourcing</postsourcingfunction>
  <recalcfunction>clientRecalc</recalcfunction>
  <saverecordfunction>clientSaveRecord</saverecordfunction>
  <scriptfile>[/SuiteScripts/CS UI Script Files/client/ng_cs_clientExhibitorWorkOrderReport.js]</scriptfile>
  <validatedeletefunction>clientValidateDelete</validatedeletefunction>
  <validatefieldfunction>clientValidateField</validatefieldfunction>
  <validateinsertfunction>clientValidateInsert</validateinsertfunction>
  <validatelinefunction>clientValidateLine</validatelinefunction>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.cs.report.library.js]</scriptfile>
    </library>
  </libraries>
</clientscript>