/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function userEventBeforeLoad(type, form, request){
	nlapiLogExecution("AUDIT", "Payment Trans BeforeLoad", type + " -- " + nlapiGetContext().getExecutionContext());
	_scLib.hideCSJobField(form);
	
	if (_tools.isInArray(type, ["create","copy"])) {
		if (!_tools.isEmpty(request)) {
			var invID = request.getParameter("inv");
			if (!_tools.isEmpty(invID)) {
				var invData = null;
				try {
					if (_scLib.UseCustomJob) {
						invData = nlapiLookupField("invoice", invID, ["custbody_show_table","custbody_booth","createdfrom","department","class","custbody_cseg_ng_cs_job"]);
					} else {
						invData = nlapiLookupField("invoice", invID, ["custbody_show_table","custbody_booth","createdfrom","department","class"]);
					}
				} catch (err) {
					_log.logError(err, "Error encountered retrieving invoice data", "Invoice ID: {0}".NG_Format(invID));
				}
				
				if (invData != null) {
					nlapiSetFieldValue("custbody_show_table", invData.custbody_show_table);
					nlapiSetFieldValue("custbody_booth", invData.custbody_booth);
					nlapiSetFieldValue("custbody_booth_order", invData.createdfrom);
					nlapiSetFieldValue("department", invData.department);
					nlapiSetFieldValue("class", invData['class']);
					nlapiSetFieldValue("custbody_cseg_ng_cs_job", invData.custbody_cseg_ng_cs_job);

				} else {
					_log.logInfo("Empty Search Results");
				}
			} else {
				_log.logInfo("Empty Invoice ID");
			}
		} else {
			_log.logInfo("Empty Request");
		}
	}
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      approve, reject, cancel (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF)
 *                      markcomplete (Call, Task)
 *                      reassign (Case)
 *                      editforecast (Opp, Estimate)
 * @returns {Void}
 */
function userEventBeforeSubmit(type){
	if (!_tools.isInArray(type, ["delete"])) {
		try {
			var amount = new Number(nlapiGetFieldValue("payment"));
			var applied = new Number(0);
			var lines = nlapiGetLineItemCount("apply");
			for (var l = 1; l <= lines; l++) {
				var lineApplied = new Number(nlapiGetLineItemValue("apply", "amount", l));
				applied += lineApplied;
			}
			
			var unapplied = amount - applied;
			nlapiSetFieldValue("custbody_unapplied", _M.roundToHundredths(unapplied));
		} catch (err) {
			_log.logError(err, "Error encountered calculating unapplied balance");
		}
		
		var checkNum = nlapiGetFieldValue("checknum");
		if (!_tools.isEmpty(checkNum)) {
			nlapiSetFieldValue("custbody_check_num", checkNum);
		}
	}
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      approve, cancel, reject (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF only)
 *                      dropship, specialorder, orderitems (PO only) 
 *                      paybills (vendor payments)
 * @returns {Void}
 */
function userEventAfterSubmit(type){
	var paymentJobField = nlapiGetFieldValue('custbody_cseg_ng_cs_job')
	var paymentRecordId = nlapiGetRecordId()

	if (_tools.isInArray(type, ["create", "edit"])) {
		nlapiSubmitField('customerpayment', paymentRecordId, 'custbody_cseg_ng_cs_job', paymentJobField)
	}
}
