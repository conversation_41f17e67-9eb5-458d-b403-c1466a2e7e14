import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders
} from "@event-services/netsuite-http";

export default async function itemSearchHandler(req, res) {
  const requestStart = Date.now();

  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const { slug, event, page, limit, sortBy } = req.query;

    // Validate required parameters
    if (!slug || !event) {
      return res.status(400).json({
        error: "Search term and event ID are required"
      });
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_ng_es_rl_search_item_paged",
      "customdeploy_ng_es_rl_search_item_paged",
      {
        search: slug,
        event,
        page,
        limit,
        sortBy
      }
    );

    // Make request to NetSuite
    const { data, duration } = await makeNetSuiteRequest({
      url,
      method: "GET",
    });

    // Set optimized response headers
    setOptimizedHeaders(res, { maxAge: 300 });
    addPerformanceHeaders(res, requestStart, duration);

    return res.status(200).json(data);
  } catch (error) {
    return handleApiError(error, { res, requestStart });
  }
}
