import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

// eslint-disable-next-line consistent-return
export default async (req, res) => {
  const session = await getServerSession(req, res, authOptions);
  const customer = req.cookies.customer;
  const { ceid } = req.query;

  if (session) {
    try {
      switch (req.method) {
        case "GET":
          {
            let { type } = req.query;
            const currRequest = {
              url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_purchase_order_cart&deploy=customdeploy_rcs_purchase_order_cart&ceid=${ceid}&uid=${customer}&type=${type}`,
              method: "GET",
            };

            try {
              const response = await request({
                uri: currRequest.url,
                method: "GET",
                oauth: {
                  consumer_key: process.env.OAUTH1_CONSUMER_KEY,
                  consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
                  token: process.env.OAUTH1_ACCESS_TOKEN,
                  token_secret: process.env.OAUTH1_TOKEN_SECRET,
                  signature_method: "HMAC-SHA256",
                  realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
                  version: "1.0",
                },
                resolveWithFullResponse: true,
                json: true,
              });

              const data = await response.body;
              console.debug('RES: " ', response);
              console.debug('Body: " ', data);
              res.end(JSON.stringify(data));
            } catch (err) {
              res.send({
                error: `Error processing payment request: ${err}`,
              });
              res.status(405).end();
            }
          }
          break;

        case "POST":
          {
            const { type } = req.body;

            switch (type) {
              case "address":
                {
                  try {
                    const response = await request({
                      uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_purchase_order_cart&deploy=customdeploy_rcs_purchase_order_cart`,
                      method: "POST",
                      oauth: {
                        consumer_key: process.env.OAUTH1_CONSUMER_KEY,
                        consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
                        token: process.env.OAUTH1_ACCESS_TOKEN,
                        token_secret: process.env.OAUTH1_TOKEN_SECRET,
                        signature_method: "HMAC-SHA256",
                        realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
                        version: "1.0",
                      },
                      body: req.body,
                      headers: { "Content-Type": "application/json" },
                      resolveWithFullResponse: true,
                      json: true,
                    });

                    const data = await response.body;
                    console.debug('RES: " ', response);
                    console.debug('Body: " ', data);
                    res.end(JSON.stringify(data));
                  } catch (err) {
                    res.send({
                      error: `Error processing address request: ${err}`,
                    });
                    res.status(405).end();
                  }
                }
                break;
              case "card":
                {
                  try {
                    const response = await request({
                      uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_purchase_order_cart&deploy=customdeploy_rcs_purchase_order_cart`,
                      method: "POST",
                      oauth: {
                        consumer_key: process.env.OAUTH1_CONSUMER_KEY,
                        consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
                        token: process.env.OAUTH1_ACCESS_TOKEN,
                        token_secret: process.env.OAUTH1_TOKEN_SECRET,
                        signature_method: "HMAC-SHA256",
                        realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
                        version: "1.0",
                      },
                      body: req.body,
                      headers: { "Content-Type": "application/json" },
                      resolveWithFullResponse: true,
                      json: true,
                    });

                    const data = await response.body;
                    console.debug('RES: " ', response);
                    console.debug('Body: " ', data);
                    res.end(JSON.stringify(data));
                  } catch (err) {
                    res.send({
                      error: `Error processing card request: ${err}`,
                    });
                    res.status(405).end();
                  }
                }
                break;
              case "order":
                {
                  try {
                    const response = await request({
                      uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_purchase_order_cart&deploy=customdeploy_rcs_rl_order_processing`,
                      method: "POST",
                      oauth: {
                        consumer_key: process.env.OAUTH1_CONSUMER_KEY,
                        consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
                        token: process.env.OAUTH1_ACCESS_TOKEN,
                        token_secret: process.env.OAUTH1_TOKEN_SECRET,
                        signature_method: "HMAC-SHA256",
                        realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
                        version: "1.0",
                      },
                      body: req.body,
                      headers: { "Content-Type": "application/json" },
                      resolveWithFullResponse: true,
                      json: true,
                    });

                    const data = await response.body;
                    console.debug('RES: " ', response);
                    console.debug('Body: " ', data);
                    res.end(JSON.stringify(data));
                  } catch (err) {
                    res.send({
                      error: `Error processing order request: ${err}`,
                    });
                    res.status(405).end();
                  }
                }
                break;
              default:
                return 'No type was specified to arg in the request body please ensure an arg of "type" is entered and try again.';
            }
          }
          break;
        default:
          return "No request method type was met. Please ensure the type of method has a type. Ex. GET, POST, PUT, PATCH, etc.";
      }
    } catch (e) {
      // eslint-disable-next-line no-undef
      res.json(error);
      res.status(405).end();
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
    // res.redirect('/auth/login')
  }
};
