'use client'

import React from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

export default function SignIn() {
	const router = useRouter()
	const searchParams = useSearchParams()
	const [loading, setLoading] = useState(true)

	useEffect(() => {
		const checkSession = async () => {
			const session = await getSession()
			if (session) {
				const returnUrl = searchParams.get('returnUrl') || '/booking'
				router.push(returnUrl)
			} else {
				setLoading(false)
			}
		}
		checkSession()
	}, [router, searchParams])

	const handleSignIn = async () => {
		try {
			const returnUrl = searchParams.get('returnUrl') || '/booking'
			await signIn('netsuite', { callbackUrl: returnUrl })
		} catch (error) {
			console.error('Sign in error:', error)
		}
	}

	if (loading) {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
					<p className="mt-2 text-gray-600">Loading...</p>
				</div>
			</div>
		)
	}

	return (
		<div className="flex min-h-screen items-center justify-center bg-gray-50">
			<div className="max-w-md w-full space-y-8">
				<div>
					<h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
						Sign in to Booking System
					</h2>
					<p className="mt-2 text-center text-sm text-gray-600">
						Access your booking dashboard
					</p>
				</div>
				<div>
					<button
						onClick={handleSignIn}
						className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
					>
						Sign in with NetSuite
					</button>
				</div>
			</div>
		</div>
	)
}
