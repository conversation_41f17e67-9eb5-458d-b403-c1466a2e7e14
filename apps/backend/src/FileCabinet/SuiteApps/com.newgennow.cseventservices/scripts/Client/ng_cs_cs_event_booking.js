/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 */
define(["enums", "N/query", "N/record"] /**
 * @param {ES_Enums} enums
 * @param {query} query
 * @param {record} record
 */, function (enums, query, record) {
  // values to save post sourcing
  const POST_SOURCING = {
    paths: {
      custrecord_nges_fun_venue: "custrecord_nges_fun_venue_space",
    },
    values: {
      custrecord_nges_fun_venue_space: null,
    },
  };

  const PATH_KEYS = Object.keys(POST_SOURCING.paths);
  const VALUE_KEYS = Object.keys(POST_SOURCING.values);

  // to prevent a stack overflow we can toggle this variable when we
  // are in the middle of autofilling a sublist row with several values
  let AUTO_FILLING = false;

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(scriptContext) {}

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.line - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.column - Column number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(scriptContext) {
    const { fieldId } = scriptContext;

    updateSpaceReadOnlyField(scriptContext);
    defaultDatesAndTimesFromEvent(scriptContext);

    // When a field relating to the booking name change we update the booking name
    if (
      Object.keys(enums.venueOpsMappings.BOOKING_NAME_FIELDS).includes(fieldId)
    ) {
      const { currentRecord } = scriptContext;

      const [space, status, startDate, startTime, endDate, endTime, event] =
        Object.entries(enums.venueOpsMappings.BOOKING_NAME_FIELDS).map(
          ([fieldId, defaultValue]) =>
            valueOr(currentRecord.getText({ fieldId }), defaultValue),
        );

      currentRecord.setValue({
        fieldId: "name",
        value: `${space} : ${status} : ${startDate} ${startTime} - ${endDate} ${endTime} : ${event}`,
      });
    }

    // We want to update linked functions when a related booking field updates
    if (
      Object.keys(enums.venueOpsMappings.BOOKING_FUNCTION_MAPPING).includes(
        fieldId,
      )
    ) {
      const { currentRecord } = scriptContext;

      const sublistLines = currentRecord.getLineCount({
        sublistId: "recmachcustrecord_nges_fun_booking",
      });

      if (sublistLines > 0) {
        const fieldValue = currentRecord.getValue({ fieldId });
        const sublistFieldId =
          enums.venueOpsMappings.BOOKING_FUNCTION_MAPPING[fieldId];

        for (let i = 0; i < sublistLines; i++) {
          currentRecord.selectLine({
            sublistId: "recmachcustrecord_nges_fun_booking",
            line: i,
          });

          const linkToBooking = currentRecord.getCurrentSublistValue({
            sublistId: "recmachcustrecord_nges_fun_booking",
            fieldId: "custrecord_ng_cs_link_to_booking",
          });

          if (linkToBooking) {
            currentRecord.setCurrentSublistValue({
              sublistId: "recmachcustrecord_nges_fun_booking",
              fieldId: sublistFieldId,
              value: fieldValue,
            });

            currentRecord.commitLine({
              sublistId: "recmachcustrecord_nges_fun_booking",
            });
          }
        }
      }
    }

    if (fieldId === "custrecord_ng_cs_link_to_booking") {
      autoFillFunction(scriptContext);
    }

    if (!AUTO_FILLING) {
      populateFunctionName(scriptContext);
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(scriptContext) {
    const { sublistId, fieldId } = scriptContext;

    if (PATH_KEYS.includes(fieldId)) {
      const updateFieldId = POST_SOURCING.paths[fieldId];
      const value = POST_SOURCING.values[updateFieldId];
      if (value) {
        const { currentRecord } = scriptContext;
        currentRecord.setCurrentSublistValue({
          sublistId,
          fieldId: updateFieldId,
          value,
        });
      }
    }
  }

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(scriptContext) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(scriptContext) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(scriptContext) {}

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(scriptContext) {
    return checkFunctionsDates(scriptContext);
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(scriptContext) {}

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(scriptContext) {}

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(scriptContext) {
    return checkBookingsDates(scriptContext);
  }

  /**
   * Returns the placeholder value if the value is considered blank.
   * Blank includes undefined, null, '', and ' '.
   * @param {string} value - Value to check
   * @param {string} placeholder - Placeholder if value is blank
   * @returns {string} - Returns the value if present or placeholder if blank
   */
  const valueOr = (value, placeholder) => {
    const blank = [undefined, null, "", " "];
    return blank.includes(value) ? placeholder : value;
  };

  const autoFillFunction = (scriptContext) => {
    const { sublistId } = scriptContext;

    if (sublistId === "recmachcustrecord_nges_fun_booking") {
      const { currentRecord } = scriptContext;

      const linkToBookings = currentRecord.getCurrentSublistValue({
        sublistId,
        fieldId: "custrecord_ng_cs_link_to_booking",
      });

      if (linkToBookings) {
        AUTO_FILLING = true;
        Object.entries(enums.venueOpsMappings.BOOKING_FUNCTION_MAPPING).forEach(
          ([bookingFieldId, functionFieldId]) => {
            const bookingFieldValue = currentRecord.getValue({
              fieldId: bookingFieldId,
            });
            if (!bookingFieldValue) {
              return;
            }

            if (VALUE_KEYS.includes(functionFieldId)) {
              POST_SOURCING.values[functionFieldId] = bookingFieldValue;
            } else {
              currentRecord.setCurrentSublistValue({
                sublistId,
                fieldId: functionFieldId,
                value: bookingFieldValue,
              });
            }
          },
        );
        AUTO_FILLING = false;
      }
    }
  };

  const populateFunctionName = (scriptContext) => {
    const { fieldId } = scriptContext;

    if (
      Object.keys(enums.venueOpsMappings.FUNCTION_NAME_FIELDS).includes(fieldId)
    ) {
      const { currentRecord, sublistId } = scriptContext;

      const [space, title, startDate, startTime, endDate, endTime] =
        Object.entries(enums.venueOpsMappings.FUNCTION_NAME_FIELDS).map(
          ([fieldId, defaultValue]) =>
            valueOr(
              currentRecord.getCurrentSublistText({
                sublistId,
                fieldId,
              }),
              defaultValue,
            ),
        );

      currentRecord.setCurrentSublistValue({
        sublistId,
        fieldId: "name",
        value: `${space} : ${title} : ${startDate} ${startTime} - ${endDate} ${endTime}`,
      });
    }
  };

  /**
   * Updates the space read only field if the space field changed
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.line - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.column - Column number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  const updateSpaceReadOnlyField = (scriptContext) => {
    const { fieldId } = scriptContext;

    if (fieldId === "custrecord_ng_cs_eb_space_ui") {
      const { currentRecord } = scriptContext;

      const spaceId = currentRecord.getValue({
        fieldId: "custrecord_ng_cs_eb_space_ui",
      });

      const comboSpaceIds = spaceId
        ? query
            .runSuiteQL({
              query: `
              SELECT
                custrecord_ng_cs_space_bookings_combo_ss
              FROM
                customrecord_exhibition_hall
              WHERE
                id = ${spaceId}
            `,
            })
            .asMappedResults()[0]
            ?.custrecord_ng_cs_space_bookings_combo_ss?.split(", ") || []
        : [];

      currentRecord.setValue({
        fieldId: "custrecord_ng_cs_eb_space",
        value: [spaceId, ...comboSpaceIds],
      });
    }
  };

  /**
   * Ensures that bookings dates are logically valid.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  const checkBookingsDates = (scriptContext) => {
    const { currentRecord } = scriptContext;

    // Get current sublist field values for the start and end dates and times
    const startDate = currentRecord.getText({
      fieldId: "custrecord_ng_cs_eb_start_date",
    });
    const startTime = currentRecord.getText({
      fieldId: "custrecord_ng_cs_eb_start_time",
    });
    const endDate = currentRecord.getText({
      fieldId: "custrecord_ng_cs_eb_end_date",
    });
    const endTime = currentRecord.getText({
      fieldId: "custrecord_ng_cs_eb_end_time",
    });

    // Only proceed if end date or end time is entered
    if (endDate || endTime) {
      if (startDate && startTime) {
        // Combine dates and times into Date objects using string literals
        const startDateTime = new Date(`${startDate} ${startTime}`);
        const endDateTime = new Date(`${endDate} ${endTime}`);

        // Check if end date/time is before start date/time
        if (endDateTime <= startDateTime) {
          alert("Booking end date and time must be after start date and time.");
          return false; // Prevent save
        }
      }
    }

    return true; // Allow save
  };

  /**
   * Ensures that functions dates are logically valid.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  const checkFunctionsDates = (scriptContext) => {
    const { sublistId } = scriptContext;

    if (sublistId === "recmachcustrecord_nges_fun_booking") {
      const { currentRecord } = scriptContext;

      // Get current sublist field values for the start and end dates and times
      const startDate = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_booking",
        fieldId: "custrecord_nges_fun_start_date",
      });
      const startTime = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_booking",
        fieldId: "custrecord_nges_fun_start_time",
      });
      const endDate = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_booking",
        fieldId: "custrecord_nges_fun_end_date",
      });
      const endTime = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_booking",
        fieldId: "custrecord_nges_fun_end_time",
      });

      // Only proceed if end date or end time is entered
      if (endDate || endTime) {
        if (startDate && startTime) {
          // Combine dates and times into Date objects using string literals
          const startDateTime = new Date(`${startDate} ${startTime}`);
          const endDateTime = new Date(`${endDate} ${endTime}`);

          // Check if end date/time is before start date/time
          if (endDateTime <= startDateTime) {
            alert(
              "Function end date and time must be after start date and time.",
            );
            return false; // Prevent line save
          }
        }
      }
    }

    return true; // Allow line save
  };

  /**
   * Defaults the dates and times to the selected event. To be called in the fieldChange function
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.line - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.column - Column number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  const defaultDatesAndTimesFromEvent = (scriptContext) => {
    const { fieldId } = scriptContext;

    if (fieldId === "custrecord_ng_cs_eb_event") {
      const { currentRecord } = scriptContext;

      const eventId = currentRecord.getValue({
        fieldId: "custrecord_ng_cs_eb_event",
      });

      if (eventId) {
        const fieldMapping = {
          custrecord_cses_start_date: "custrecord_ng_cs_eb_start_date",
          custrecord_cses_start_time: "custrecord_ng_cs_eb_start_time",
          custrecord_cses_end_date: "custrecord_ng_cs_eb_end_date",
          custrecord_cses_end_time: "custrecord_ng_cs_eb_end_time",
        };

        const event = record.load({
          type: "customrecord_show",
          id: eventId,
          isDynamic: true,
        });

        Object.entries(fieldMapping).forEach(
          ([eventFieldId, bookingFieldId]) => {
            const value = event.getValue({
              fieldId: eventFieldId,
            });

            if (value) {
              currentRecord.setValue({
                fieldId: bookingFieldId,
                value,
              });
            }
          },
        );
      }
    }
  };

  return {
    // pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    // sublistChanged: sublistChanged,
    // lineInit: lineInit,
    // validateField: validateField,
    validateLine: validateLine,
    // validateInsert: validateInsert,
    // validateDelete: validateDelete,
    saveRecord: saveRecord,
  };
});
