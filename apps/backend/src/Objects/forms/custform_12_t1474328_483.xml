<entryForm scriptid="custform_12_t1474328_483" standard="STANDARDCUSTOMERFORM">
  <name>CS Exhibitor Services Customer Form</name>
  <recordType>CUSTOMER</recordType>
  <inactive>F</inactive>
  <preferred>F</preferred>
  <storedWithRecord>T</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ENTITYID</id>
          <label>Customer ID</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>AUTONAME</id>
          <label>Auto</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ALTNAME</id>
          <label>Name</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ISINDIVIDUAL</id>
          <label>Type</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>SALUTATION</id>
          <label>Mr./Ms...</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>FIRSTNAME</id>
          <label>Name</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>TITLE</id>
          <label>Job Title</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>COMPANYNAME</id>
          <label>Company Name</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>PARENT</id>
          <label>Parent Company</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ENTITYSTATUS</id>
          <label>Status</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>PROBABILITY</id>
          <label>Probability</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>SALESREP</id>
          <label>Sales Rep</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>PARTNER</id>
          <label>Partner</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>URL</id>
          <label>Web Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>CATEGORY</id>
          <label>Category</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>IMAGE</id>
          <label>Image</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>DEFAULTORDERPRIORITY</id>
          <label>Default Order Priority</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>COMMENTS</id>
          <label>Comments</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=custentity_department]</id>
          <label>Department</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=custentity_default_order_type]</id>
          <label>Default Order Type</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="emailphoneaddress">
      <label>Email | Phone | Address</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>EMAIL</id>
          <label/>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ALTEMAIL</id>
          <label>Alt. Email</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>PHONE</id>
          <label>Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>ALTPHONE</id>
          <label>Alt. Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>MOBILEPHONE</id>
          <label>Mobile Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>HOMEPHONE</id>
          <label>Home Phone</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>FAX</id>
          <label>Fax</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>DEFAULTADDRESS</id>
          <label>Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="classification">
      <label>Classification</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>PHONETICNAME</id>
          <label>Furigana</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>SUBSIDIARY</id>
          <label>Subsidiary</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>[scriptid=custentity_naw_trans_need_approval]</id>
          <label>Transactions Need Approval</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=custentity_exhibit_contact]</id>
          <label>Exhibitor Contact</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>ENTITYRELATIONSHIPS</id>
      <label>Relationships</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>OTHERRELATIONSHIPS</id>
              <label>Other Relationships</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_ng_cs_exhib_shows]</id>
              <label>Exhibitor's Events</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYCONTACT</id>
          <label>Contacts</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYSUBS</id>
          <label>Subcustomers</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYPARTNERS</id>
          <label>Partners</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYADDRESS</id>
      <label>Address</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYADDRESSBOOK</id>
          <label>Address</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYSALES</id>
      <label>Sales</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>TERRITORY</id>
              <label>Territory</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYOPPORTUNITIES</id>
          <label>Opportunities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYUPSELL</id>
          <label>Upsell</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subTab>
          <id>ENTITYQUALIFICATION</id>
          <label>Qualification</label>
          <visible>F</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>ESTIMATEDBUDGET</id>
                  <label>Estimated Budget</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>ISBUDGETAPPROVED</id>
                  <label>Budget Approved</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>SALESREADINESS</id>
                  <label>Sales Readiness</label>
                  <visible>T</visible>
                  <mandatory>T</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>BUYINGREASON</id>
                  <label>Buying Reason</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>BUYINGTIMEFRAME</id>
                  <label>Buying Time Frame</label>
                  <visible>T</visible>
                  <mandatory>T</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists/>
        </subTab>
        <subList>
          <id>ENTITYJOBS</id>
          <label>Projects</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=custtab_60_4132987_205]</id>
      <label>Show Info</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>[scriptid=custtabtstdrv148311223]</id>
          <label>Booths</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup/>
          </fieldGroups>
          <subLists/>
        </subTab>
        <subList>
          <id>ENTITYCREDITCARDS</id>
          <label>Credit Cards</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYDEALITEM</id>
          <label>Items Purchased</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYFINHIST</id>
          <label>Transactions</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=custsublist_6_t1474328_794]</id>
          <label>Booths (as Exhibitor only)</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_ng_cs_shipment.custrecord_ng_cs_ship_customer]</id>
          <label>CS Shipment</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[appid=com.newgennow.cseventservices, scriptid=customrecord_show.custrecord_show_customer]</id>
          <label>CS Event</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYCOMMUNICATION</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYMESSAGES</id>
          <label>Messages</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYACTIVITIES</id>
          <label>Activities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYEVENTS</id>
          <label>Events</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYTASKS</id>
          <label>Tasks</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCALLS</id>
          <label>Calls</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYMEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYUSERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYBULKMERGE</id>
          <label>Bulk Merge</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYMARKETING</id>
      <label>Marketing</label>
      <visible>F</visible>
      <fieldGroups>
        <fieldGroup scriptid="leadinformation">
          <label>Lead Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>LEADSOURCE</id>
              <label>Lead Source</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>WEBLEAD</id>
              <label>Web Lead</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SOURCEWEBSITE</id>
              <label>Source Web Site</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="websitevisits">
          <label>Website Visits</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>CAMPAIGNCATEGORY</id>
              <label>Campaign Category</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>VISITS</id>
              <label>Number of Visits</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>CAMPAIGNEVENT</id>
              <label>Campaign Event</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>FIRSTVISIT</id>
              <label>Date of First Visit</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>REFERRER</id>
              <label>Referrer (1st Visit)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>KEYWORDS</id>
              <label>Search Engine Keywords (1st Visit)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>CLICKSTREAM</id>
              <label>Clickstream (1st Visit)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>LASTPAGEVISITED</id>
              <label>Last Page Visited</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>LASTVISIT</id>
              <label>Date of Last Visit</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>ENTITYSUBSCRIPTIONS</id>
          <label>Subscriptions</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>GLOBALSUBSCRIPTIONSTATUS</id>
                  <label>Global Subscription Status</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists>
            <subList>
              <id>ENTITYSUBSCRIPTIONMSGMACH</id>
              <label>Subscription Message History</label>
              <visible>T</visible>
              <neverEmpty>F</neverEmpty>
            </subList>
          </subLists>
        </subTab>
        <subList>
          <id>ENTITYONLINEFORMS</id>
          <label>Online Forms</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYONLINEFORMEVENTS</id>
          <label>Online Forms</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCAMPAIGNS</id>
          <label>Campaigns</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCLICKSTREAMS</id>
          <label>Click-Streams</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYPAGEHITS</id>
          <label>Page Hits</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYHOSTEDHITS</id>
          <label>Hosted Page Hits</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYKEYWORDS</id>
          <label>Keywords</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCARTCONTENTS</id>
          <label>Cart Contents</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYITEMORDERS</id>
          <label>Item Orders</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYREFERRER</id>
          <label>Referrer</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYSUPPORT</id>
      <label>Support</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYCASES</id>
          <label>Cases</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYFINANCIAL</id>
      <label>Financial</label>
      <visible>F</visible>
      <fieldGroups>
        <fieldGroup scriptid="accountinformation">
          <label>Account Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>ACCOUNTNUMBER</id>
              <label>Account</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>RECEIVABLESACCOUNT</id>
              <label>Default Receivables Account</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>STARTDATE</id>
              <label>Start Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>ENDDATE</id>
              <label>End Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>REMINDERDAYS</id>
              <label>Reminder Days</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>PRICELEVEL</id>
              <label>Price Level</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>CURRENCYFORMATSAMPLE</id>
              <label>Format Sample</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>OVERRIDECURRENCYFORMAT</id>
              <label>Override Currency Format</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>DISPLAYSYMBOL</id>
              <label>Currency Symbol</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SYMBOLPLACEMENT</id>
              <label>Symbol Placement</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TERMS</id>
              <label>Terms</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>CREDITLIMIT</id>
              <label>Credit Limit</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>CREDITHOLDOVERRIDE</id>
              <label>Hold</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>PREFCCPROCESSOR</id>
              <label>Pref. CC Processor</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="taxinformation">
          <label>Tax Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>VATREGNUMBER</id>
              <label>Tax Reg. Number</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TAXABLE</id>
              <label>Taxable</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TAXITEM</id>
              <label>Tax Item</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>RESALENUMBER</id>
              <label>Resale Number</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="balanceinformation">
          <label>Balance Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>OPENINGBALANCE</id>
              <label>Opening Balance</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>OPENINGBALANCEDATE</id>
              <label>Opening Balance Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>OPENINGBALANCEACCOUNT</id>
              <label>Opening Balance Account</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>BALANCE</id>
              <label>Balance</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>DEPOSITBALANCE</id>
              <label>Deposit Balance</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>OVERDUEBALANCE</id>
              <label>Overdue Balance</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>UNBILLEDORDERS</id>
              <label>Unbilled Orders</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>DAYSOVERDUE</id>
              <label>Days Overdue</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>DEFAULTBANKACCOUNT</id>
              <label>Bank Account</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>MONTHLYCLOSING</id>
              <label>Monthly Closing Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TAXEXEMPT</id>
              <label>PST Exempt</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TAXFRACTIONUNIT</id>
              <label>Tax Rounding Precision</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TAXROUNDING</id>
              <label>Tax Rounding Method</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>BILLINGSCHEDULE</id>
              <label>Billing Schedule</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_ng_pt_default_profile]</id>
              <label>Default PayTrace Profile</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_ng_paytrace_stmt_link_url]</id>
              <label>Statement Link URL</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_ng_paytrace_conv_fee_exempt]</id>
              <label>Convenience Fee Exempt</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_ng_paytrace_quikbill_link_url]</id>
              <label>Customer Quick Bill Link</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
          <fields position="BOTTOM">
            <field>
              <id>AGING</id>
              <label>Current</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYGROUPPRICING</id>
          <label>Group Pricing</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYITEMPRICING</id>
          <label>Item Pricing</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYTIMEITEM</id>
          <label>Time Tracking</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYPREFERENCES</id>
      <label>Preferences</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>NUMBERFORMAT</id>
              <label>Number Format</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>NEGATIVENUMBERFORMAT</id>
              <label>Negative Number Format</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>EMAILPREFERENCE</id>
              <label>Email Preference</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>PRINTONCHECKAS</id>
              <label>Print on Check As</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SENDTRANSACTIONSVIA</id>
              <label>Send Transactions Via</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SHIPCOMPLETE</id>
              <label>Ship Complete</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SHIPPINGCARRIER</id>
              <label>Shipping Carrier</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SHIPPINGITEM</id>
              <label>Shipping Method</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>ALCOHOLRECIPIENTTYPE</id>
              <label>Alcohol Recipient Type</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYS_SYSINFO</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>DATECREATED</id>
              <label>Date Created</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>ISINACTIVE</id>
              <label>Inactive</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subTab>
          <id>ENTITYACCESS</id>
          <label>Access</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>GIVEACCESS</id>
                  <label>Give Access</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>ASSIGNEDWEBSITE</id>
                  <label>Assigned Web Site</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>ACCESSROLE</id>
                  <label>Role</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>SENDEMAIL</id>
                  <label>Send Notification Email</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>FILLPASSWORD</id>
                  <label>Manually assign or change password</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>PASSWORD</id>
                  <label>Password</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
                <field>
                  <id>PASSWORD2</id>
                  <label>Confirm Password</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <quickAdd>F</quickAdd>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists/>
        </subTab>
        <subList>
          <id>ENTITYACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYCUSTOM</id>
      <label>Custom</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_selected_show]</id>
              <label>Selected Event</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_selected_booth]</id>
              <label>Selected Booth</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custentity_ng_cs_lead_source]</id>
              <label>Lead Source</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311218]</id>
      <label>Partners</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311216]</id>
      <label>Transactions</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311217]</id>
      <label>Items Purchased</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311215]</id>
      <label>Notes &amp; Attachments</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311220]</id>
      <label>Messages</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_40_t1474328_795]</id>
      <label>Notes</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYSUBSIDIARIES</id>
      <label>Subsidiaries</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYSUBMACHINE</id>
          <label>Subsidiaries</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
  </tabs>
  <quickViewFields>
    <field>
      <id>ENTITYID</id>
    </field>
    <field>
      <id>ENTITYSTATUS</id>
    </field>
    <field>
      <id>SALESREP</id>
    </field>
    <field>
      <id>URL</id>
    </field>
    <field>
      <id>EMAIL</id>
    </field>
    <field>
      <id>PHONE</id>
    </field>
    <field>
      <id>DEFAULTADDRESS</id>
    </field>
  </quickViewFields>
  <actionbar>
    <buttons>
      <button>
        <id>MERGE</id>
        <label>Merge</label>
        <visible>T</visible>
      </button>
      <button>
        <id>SEARCH</id>
        <label>Search</label>
        <visible>T</visible>
      </button>
    </buttons>
    <menu>
      <menuitem>
        <id>ACCEPTPAYMENT</id>
        <label>Accept Payment</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GENERATEPRICELIST</id>
        <label>Generate Price List</label>
        <visible>F</visible>
      </menuitem>
      <menuitem>
        <id>GENERATESTATEMENT</id>
        <label>Generate Statement</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEXT</id>
        <label>Save &amp; Next</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITAS</id>
        <label>Save As</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>ACTIVITYHISTORY</id>
        <label>Show Activity</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>VIEWALLTRANSACTIONS</id>
        <label>View All Transactions</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
