/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAMDConfig ./amdUserEventConfig.json
 */
define([
  "N/runtime",
  "N/search",
  "N/ui/serverWidget",
  "N/url",
  "N/cache",
  "ES/enums",
  "settings",
] /**
 * @param{runtime} runtime
 * @param{search} search
 * @param{serverWidget} serverWidget
 * @param{url} url
 * @param{cache} cache
 * @param{Object} enums
 * @param{ES_Enums} enums.item
 * @param{Object} settings
 * @param{() => Object} settings.useSettings
 */, (runtime, search, serverWidget, url, cache, enums, settings) => {
  let CS_SETTINGS = null;
  // Cache name constants from price hooks
  const ITEM_PRICE_CACHE_NAME = "ITEM_PRICE_CACHE";
  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServletRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  const beforeLoad = (scriptContext) => {
    CS_SETTINGS = settings.useSettings();
    const { newRecord, type: mode, form } = scriptContext;
    const graphicOptionsEnabled =
      CS_SETTINGS.custrecord_ng_cs_enable_graphics_option === "T";
    const orientationOptionsEnabled =
      CS_SETTINGS.custrecord_ng_cs_enable_orientation_opt === "T";

    if (["create", "edit", "view", "copy"].includes(mode)) {
      if (!graphicOptionsEnabled) {
        try {
          form.getField({ id: enums.item.Field.GRAPHIC }).updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          });
        } catch (err) {}
        try {
          form.getField({ id: enums.item.Field.GRAPHIC }).updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          });
        } catch (err) {}
      }

      if (!orientationOptionsEnabled) {
        try {
          form.getField({ id: enums.item.Field.VARIANT }).updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          });
        } catch (err) {}
        try {
          form.getField({ id: enums.item.Field.VARIANT }).updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          });
        } catch (err) {}
      }

      // Hide "Material Handling Schedule" if Material handling beta is disabled
      const materialHandlingBeta =
        CS_SETTINGS.custrecord_enable_material_handling_beta === "T";

      if (!materialHandlingBeta) {
        try {
          form
            .getField({ id: "custitem_ng_mat_handling_sched" })
            .updateDisplayType({
              displayType: serverWidget.FieldDisplayType.HIDDEN,
            }).isMandatory = false;
        } catch (err) {
          log.error({
            title: "💥 Material Handling Not Enabled but failed to hide",
            details: "",
          });
        }
      } else {
        let isMaterialHandling = newRecord.getValue("custitem_is_freight");
        let materialHandlingScheduleField = form.getField({
          id: "custitem_ng_mat_handling_sched",
        });
        let materialHandlingSchedule = newRecord.getValue({
          fieldId: "custitem_ng_mat_handling_sched",
        });
        let maximumQuantityField = form.getField({
          id: "maximumquantity",
        });
        let minimumQuantityField = form.getField({
          id: "minimumquantity",
        });

        if (!isMaterialHandling) {
          try {
            materialHandlingScheduleField.isMandatory = false;
            materialHandlingScheduleField.updateDisplayType({
              displayType: serverWidget.FieldDisplayType.NODISPLAY,
            });
          } catch (err) {
            log.audit({ title: "🤫 Material Handling Enabled", details: "" });
          }

          updateFieldDisplayType(maximumQuantityField);
          updateFieldDisplayType(minimumQuantityField);
        } else {
          if (materialHandlingSchedule) {
            let materialHandlingScheduleRecord = search.lookupFields({
              type: "customrecord_ng_mat_handling_sched",
              id: materialHandlingSchedule,
              columns: [
                "custrecord_ng_mat_handle_sch_charge_type",
                "custrecord_ng_mat_handle_sch_min_weight",
                "custrecord_ng_mat_handle_sch_max_weight",
              ],
            });

            let chargeType = getListValueFromLookup(
              materialHandlingScheduleRecord.custrecord_ng_mat_handle_sch_charge_type,
            );

            // If charge type is CWT, show the min and max quantities
            if (mode === "view" && chargeType && chargeType.value === "1") {
              maximumQuantityField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.INLINE,
              });
              minimumQuantityField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.INLINE,
              });
            } else {
              maximumQuantityField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.NODISPLAY,
              });
              minimumQuantityField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.NODISPLAY,
              });
            }
          }
        }
      }
    }
  };

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = (scriptContext) => {
    CS_SETTINGS = settings.useSettings();
    const { newRecord, type: mode } = scriptContext;

    const materialHandlingBeta =
      CS_SETTINGS.custrecord_enable_material_handling_beta === "T";

    if (["create", "edit"].includes(mode)) {
      // Detect if item is a matrix item
      const isMatrixItem = newRecord.getValue({
        fieldId: "matrixitemnametemplate",
      });

      if (isMatrixItem) {
        // Loop through the matrix fields
        let fields = [];
        let values = [];
        let options;

        if (Object.keys(enums.item).length !== 0) {
          for (const field in enums.item.Field) {
            if (
              Object.prototype.hasOwnProperty.call(enums.item.Field, field) &&
              !fields.includes(field)
            ) {
              const element = enums.item.Field[field];
              const hasOption = enums.item.Relationship[element];

              if (element) {
                options = newRecord.getValue({ fieldId: element });
                if (options) {
                  if (options.length > 0) {
                    fields.push(element);
                    values.push(true);
                    newRecord.setValue({ fieldId: hasOption, value: true });
                  } else {
                    fields.push(element);
                    values.push(false);
                    newRecord.setValue({ fieldId: hasOption, value: false });
                  }
                } else {
                  fields.push(element);
                  values.push(false);
                  newRecord.setValue({ fieldId: hasOption, value: false });
                }
              }
            } else {
              const pos = fields.indexOf(field);
              const val = values[pos];
              if (val === false) {
                fields.splice(pos, 1);
                values.splice(pos, 1);
                options = newRecord.getValue({
                  fieldId: enums.item.Field[field],
                });
                let element = enums.item.Field[field];
                const hasOption = enums.item.Relationship[element];

                if (options) {
                  if (options.length > 0) {
                    fields.push(field);
                    values.push(true);
                    newRecord.setValue({
                      fieldId: hasOption,
                      value: true,
                    });
                  } else {
                    fields.push(field);
                    values.push(false);
                    newRecord.setValue({
                      fieldId: hasOption,
                      value: false,
                    });
                  }
                } else {
                  fields.push(field);
                  values.push(false);
                  newRecord.setValue({
                    fieldId: hasOption,
                    value: false,
                  });
                }
              }
            }
          }
        }
      }

      // Run logic for item options
      let selectedOptions = newRecord.getValue({ fieldId: "itemoptions" });

      const isDayCalc = newRecord.getValue({
        fieldId: enums.item.Type.DAYS_CALC,
      });
      const isShowDuration = newRecord.getValue({
        fieldId: enums.item.Type.SHOW_DURATION,
      });
      const isLabor = newRecord.getValue({ fieldId: enums.item.Type.LABOR });
      const days = isDayCalc || isShowDuration;

      if (
        isDayCalc &&
        !selectedOptions.includes(enums.item.Options.LABORDATE.toUpperCase())
      ) {
        selectedOptions.push(enums.item.Options.LABORDATE.toUpperCase());
      } else if (
        isShowDuration &&
        !selectedOptions.includes(enums.item.Options.LABORDATE.toUpperCase())
      ) {
        selectedOptions.push(enums.item.Options.LABORDATE.toUpperCase());
      } else {
        if (!isDayCalc) {
          selectedOptions = selectedOptions.filter(
            (opt) => opt !== enums.item.Options.LABORDATE.toUpperCase(),
          );
        }
      }

      if (isLabor) {
        if (
          !selectedOptions.includes(enums.item.Options.LABORDATE.toUpperCase())
        ) {
          selectedOptions.push(enums.item.Options.LABORDATE.toUpperCase());
        }
        if (
          !selectedOptions.includes(
            enums.item.Options.LABORENDTIME.toUpperCase(),
          )
        ) {
          selectedOptions.push(enums.item.Options.LABORENDTIME.toUpperCase());
        }
        if (
          !selectedOptions.includes(
            enums.item.Options.LABORESTHOURS.toUpperCase(),
          )
        ) {
          selectedOptions.push(enums.item.Options.LABORESTHOURS.toUpperCase());
        }
        if (
          !selectedOptions.includes(
            enums.item.Options.LABORWORKERS.toUpperCase(),
          )
        ) {
          selectedOptions.push(enums.item.Options.LABORWORKERS.toUpperCase());
        }
        if (
          !selectedOptions.includes(enums.item.Options.LABORTIME.toUpperCase())
        ) {
          selectedOptions.push(enums.item.Options.LABORTIME.toUpperCase());
        }
      } else {
        selectedOptions = selectedOptions.filter(
          (opt) =>
            (opt !== enums.item.Options.SUPERVISON.toUpperCase() &&
              !isDayCalc) ||
            (opt !== enums.item.Options.LABORDATE.toUpperCase() && !days) ||
            (opt !== enums.item.Options.LABORENDTIME.toUpperCase() &&
              opt !== enums.item.Options.LABORESTHOURS.toUpperCase() &&
              opt !== enums.item.Options.LABORWORKERS.toUpperCase() &&
              opt !== enums.item.Options.LABORTIME.toUpperCase()),
        );
      }

      if (mode === "edit") {
        if (
          newRecord.id === Number(CS_SETTINGS.custrecord_ng_cs_supervisor_item)
        ) {
          const fieldIds = enums.item.Options;

          for (const field in fieldIds) {
            log.debug({
              title: "➡️ Field scan for supervision",
              details: field,
            });
            if (Object.hasOwn(fieldIds, field)) {
              const element = fieldIds[field];
              if (!selectedOptions.includes(element.toUpperCase())) {
                selectedOptions.push(element.toUpperCase());
              }
            }
          }
        }
      }

      if (materialHandlingBeta) {
        // Check if item is a material handling item and if it has a material handling schedule
        // If it does, lookup the material handling schedule and set the min and max quantities from the schedule

        let isMaterialHandling = newRecord.getValue("custitem_is_freight");
        let materialHandlingSchedule = newRecord.getValue(
          "custitem_ng_mat_handling_sched",
        );

        if (isMaterialHandling && materialHandlingSchedule) {
          // Now if the lookup is successful, set the min and max quantities if the charge type is set to CWT

          let materialHandlingScheduleRecord = search.lookupFields({
            type: "customrecord_ng_mat_handling_sched",
            id: materialHandlingSchedule,
            columns: [
              "custrecord_ng_mat_handle_sch_charge_type",
              "custrecord_ng_mat_handle_sch_min_weight",
              "custrecord_ng_mat_handle_sch_max_weight",
            ],
          });

          let chargeType = getListValueFromLookup(
            materialHandlingScheduleRecord.custrecord_ng_mat_handle_sch_charge_type,
          );
          let minQty =
            materialHandlingScheduleRecord.custrecord_ng_mat_handle_sch_min_weight;
          let maxQty =
            materialHandlingScheduleRecord.custrecord_ng_mat_handle_sch_max_weight;

          if (chargeType?.value === "1") {
            let minCWT = minQty && calculateNearestCWT(minQty);
            let maxCWT = maxQty && calculateNearestCWT(maxQty);
            let minCWTUnits = minCWT && calculateHundredWeightUnits(minQty);
            let maxCWTUnits = maxCWT && calculateHundredWeightUnits(maxQty);

            minCWTUnits &&
            newRecord.setValue({
              fieldId: "minimumquantity",
              value: minCWTUnits,
            });

            maxCWTUnits &&
            newRecord.setValue({
              fieldId: "maximumquantity",
              value: maxCWTUnits,
            });
          }
          if (chargeType?.value === "2") {
            newRecord.setValue({
              fieldId: "minimumquantity",
              value: 1,
            });

            newRecord.setValue({
              fieldId: "maximumquantity",
              value: 99999,
            });
          }
        }
      }
      newRecord.setValue({ fieldId: "itemoptions", value: selectedOptions });
    }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (scriptContext) => {
    CS_SETTINGS = settings.useSettings();
    const { newRecord, type: mode } = scriptContext;
    
    try {
      // Check if we are in create mode or delete mode
      if (mode === "create" || mode === "delete") {
        return;
      }

      // Get the item ID from the updated record
      const itemId = newRecord.id;
      
      if (!itemId) {
        log.debug('Cache clearing', 'No item ID found, skipping cache clear');
        return;
      }
      
      log.audit('Cache clearing', 'Processing cache clear for item: ' + itemId);
      
      // Step 1: Find all Collections associated with this Item
      // Using search to identify all collections associated with this item
      const itemCollectionSearchObj = search.create({
        type: 'customrecord_ng_cs_item_collection',
        filters: [
          ['custrecord_ng_cs_itemcoll_items', 'anyof', [itemId]]
        ],
        columns: [
          search.createColumn({name: 'internalid'}),
          search.createColumn({name: 'custrecord_ng_cs_itemcoll_event'})
        ]
      });
      
      // Execute the search and extract collection IDs
      const collectionResults = itemCollectionSearchObj.run().getRange({
        start: 0,
        end: 1000 // Adjust based on expected number of collections
      });
      
      if (!collectionResults || collectionResults.length === 0) {
        log.debug('Cache clearing', 'No collections found for item: ' + itemId);
        return;
      }
      
      // Extract collection IDs
      const collectionData = collectionResults.map(result => ({
        id: result.id,
        eventId: result.getValue({name: 'custrecord_ng_cs_itemcoll_event'})
      }));

      const collectionIds = collectionData.map(collection => collection.id);
      const eventIds = collectionData.map(collection => collection.eventId);
      
      log.debug('Cache clearing - Found collections:', JSON.stringify(collectionIds));
      
      
      log.debug('Cache clearing', 'Found events: ' + JSON.stringify(eventIds));
      
      // Step 3: Clear cache for each event ID and item ID combination
      // Get access to the cache
      const itemPriceCache = cache.getCache({ 
        name: ITEM_PRICE_CACHE_NAME 
      });
      
      if (!itemPriceCache) {
        log.error('Cache clearing', 'Failed to get cache reference');
        return;
      }
      
      // Clear cache for each event/item combination
      let clearedCount = 0;
      eventIds.forEach(eventId => {
        try {
          // Each cache key is constructed as "{eventId}_{itemId}"
          const cacheKey = `${eventId}_${itemId}`;
          itemPriceCache.remove({
            key: cacheKey
          });
          clearedCount++;
        } catch (removeErr) {
          log.error({
            title: 'Error clearing cache for event: ' + eventId,
            details: removeErr
          });
        }
      });
      
      log.audit('Cache clearing', `Successfully cleared ${clearedCount} cache entries for item ${itemId}`);
      
    } catch (err) {
      log.error({
        title: 'Error in item cache clearing process',
        details: err
      });
    }
  };

  /**
   * Parses a comma-separated string of values into an array of trimmed strings.
   *
   * @param {string} valuesString - A string containing comma-separated values.
   * @returns {Array<string>} - An array of trimmed strings representing the parsed values.
   */
  function parseMultiSelectQueryValues(valuesString) {
    if (!valuesString) {
      return [];
    }

    // If there is only one value, return it as a single-element array
    if (valuesString.indexOf(",") === -1) {
      return [valuesString.trim()];
    }

    // Split the string into individual values
    var valuesArray = valuesString.split(",");

    // Trim any leading or trailing spaces from each value
    valuesArray = valuesArray.map(function (value) {
      return value.trim();
    });

    return valuesArray;
  }

  function calculateNearestCWT(weight) {
    return Math.round(weight / 100) * 100;
  }

  function calculateHundredWeightUnits(weight) {
    return Math.ceil(weight / 100);
  }

  function getListValueFromLookup(array) {
    if (Array.isArray(array) && array.length === 1) {
      const element = array[0];
      if (element && element.value && element.text) {
        return {
          value: element.value,
          text: element.text,
        };
      } else {
        log.audit({
          title:
            "❗Invalid element structure. Element must have 'value' and 'text' properties.",
          details: "",
        });
        return false;
      }
    } else {
      log.audit({
        title: "❗Input array must contain exactly one element.",
        details: "",
      });
      return null;
    }
  }

  function updateFieldDisplayType(
    field,
    displayType = serverWidget.FieldDisplayType.NORMAL,
  ) {
    if (field) {
      field.updateDisplayType({
        displayType: displayType,
      });
    } else {
      log.error({
        title: "Field Missing",
        details: "The provided field is null or undefined.",
      });
    }
  }

  return { beforeLoad, beforeSubmit, afterSubmit };
});
