<suitelet scriptid="customscript_ng_cs_sl_get_search_data">
  <description></description>
  <isinactive>F</isinactive>
  <name>NG CS SL Get Search Data</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/CS UI Script Files SS 2.0/Suitelets/ng_cs_suiteletGetSearchData.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_sl_get_search_data">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <eventtype></eventtype>
      <isdeployed>T</isdeployed>
      <isonline>F</isonline>
      <loglevel>DEBUG</loglevel>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>TESTING</status>
      <title>NG CS SL Get Search Data</title>
    </scriptdeployment>
  </scriptdeployments>
</suitelet>