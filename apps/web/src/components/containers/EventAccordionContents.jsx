import React, { useMemo } from "react";
import { useTheme } from "@mui/material/styles";
import PropTypes from "prop-types";
import tinycolor from "tinycolor2";
import {
  AccordionDetails,
  AccordionSummary,
  Box,
  Card,
  CardContent,
  Container,
} from "@mui/material";
import ReactHtmlParser, { convertNodeToElement } from "react-html-parser";
import { useSettings } from "../../store/zSettingsStore";

const EventAccordionContents = ({
  blurb,
  index,
  subTitle,
  title,
  displayTitle,
}) => {
  const theme = useTheme();
  const { settings } = useSettings();

  // eslint-disable-next-line consistent-return
  function transform(node, index) {
    if (node.type === "tag" && node.name === "head") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    // console.log('Working Nodes: ', node)
    if (node.type === "tag" && node.name === "b") {
      node.name = "strong";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "pdf") {
      node.name = "section";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "body") {
      node.name = "section";
      return convertNodeToElement(node, index, transform);
    }

    // if (node.type === 'tag' && node.name === 'span') {
    // 	return null
    // }

    // if (node.type === 'tag' && node.name === 'ul') {
    // 	node.name = 'ol';
    // 	return convertNodeToElement(node, index, transform);
    // }

    if (node.type === "tag" && node.name === "table") {
      node.name = "div";
      // console.log('table node: ', node)
      node.attribs = {
        ...node.attribs,
        width: null,
        class: "container",
        "data-test": "container",
        style: `padding: 0;`,
      };
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tr") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "td") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tbody") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    return convertNodeToElement(node, index, transform);
  }

  const renderBlurb = (blurb) => {
    return ReactHtmlParser(blurb, { decodeEntities: true, transform });
  };

  const renderProperTextColor = useMemo(() => {
    let classStyles = "antialiased uppercase font-bold text-white";

    if (settings?.custrecord_ng_cs_accent_color) {
      let settingsColor = `${settings.custrecord_ng_cs_accent_color}`;

      if (tinycolor(settingsColor).isDark()) {
        classStyles = "antialiased uppercase font-bold text-white";
      } else {
        classStyles = "antialiased uppercase font-bold text-black";
      }
    }

    return classStyles;
  }, [settings]);

  return (
    <Card key={index} sx={{ marginTop: "3vh" }}>
      <AccordionSummary
        // onClick={props.collapseToggle}
        className="tracking-wide"
        sx={{
          display: displayTitle ? "flex" : "none",
          backgroundColor: theme.palette.secondary[500],
        }}
      >
        <span className={renderProperTextColor}>{title}</span>
      </AccordionSummary>
      <AccordionDetails key={index} id={`collapse${index + 1}`}>
        <CardContent sx={{ overflow: "auto" }}>
          <Container my={4}>
            <Box className="whitespace-pre-wrap">
              <h2 className="font-weight-bold mb-1 black-text">{subTitle}</h2>
              <article className="ck-content whitespace-pre-wrap">
                {renderBlurb(blurb)}
              </article>
            </Box>
          </Container>
        </CardContent>
      </AccordionDetails>
    </Card>
  );
};

EventAccordionContents.propTypes = {
  title: PropTypes.string,
  blurb: PropTypes.oneOfType([PropTypes.string, PropTypes.elementType]),
  displayTitle: PropTypes.bool,
  subTitle: PropTypes.string,
  // eslint-disable-next-line react/no-unused-prop-types
  collapseToggle: PropTypes.func,
};

export default EventAccordionContents;
