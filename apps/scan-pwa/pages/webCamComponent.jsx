import React, {useCallback, useRef} from 'react'
import Webcam from 'react-webcam';
import {IconButton} from '@mui/material';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';

const videoConstraints = {
	facingMode: "environment"
};

function WebcamComponent({ setShowCamera, setPreviewImageUrl }) {
	const webcamRef = useRef(null);

	const capture = useCallback(() => {
		const imageSrc = webcamRef.current.getScreenshot();
		setPreviewImageUrl(imageSrc);
		setShowCamera(false);
	}, [webcamRef, setPreviewImageUrl, setShowCamera]);

	return (
		<div style={{
			display: 'flex',
			flexDirection: 'row',
			alignItems: 'center',
			justifyContent: 'space-between',
			gap: '2rem'
		}}>
			<div style={{
				display: 'flex',
				flexDirection: 'column',
				alignItems: 'center',
				gap: '4px',
			}}>
				<Webcam
					audio={false}
					height={500}
					ref={webcamRef}
					screenshotFormat="image/jpeg"
					width={500}
					videoConstraints={videoConstraints}
				/>
				<IconButton onClick={capture}>
					<PhotoCameraIcon fontSize="large"/>
				</IconButton>
			</div>
		</div>
	)
}

export default WebcamComponent
