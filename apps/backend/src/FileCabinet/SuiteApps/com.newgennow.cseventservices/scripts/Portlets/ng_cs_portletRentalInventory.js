/**
 * @NApiVersion 2.x
 * @NScriptType Portlet
 * @NModuleScope SameAccount
 */
define(['N/ui/serverWidget', 'N/url', '../lib/newgen.library.v2', '../lib/newgen.library.cs', '../lib/newgen.cs.report.library.v2'],

function(widget, url, NG, csLib, REPORT) {
	
	/**
	 * Definition of the Portlet script trigger point.
	 * 
	 * @param {Object} params
	 * @param {Portlet} params.portlet - The portlet object used for rendering
	 * @param {number} params.column - Specifies whether portlet is placed in left (1), center (2) or right (3) column of the dashboard
	 * @param {string} params.entity - (For custom portlets only) references the customer ID for the selected customer
	 * @Since 2015.2
	 */
	function render(params) {
		csLib.settings = csLib.trigger();
		
		var portlet = params.portlet;
		var col = params.column;
		portlet.title = "Inventory Review";
		
		if (csLib.settings.EnableRentals) {
			var buttonHTML = NG.tools.addNSStyleButton("ng_cs_rntl_inv_prtlet_btn", "Get Rental Info", "openRentalWindow()");
			portlet.addField({
					id : "custpage_button_html"
				,	label : "button html"
				,	type : widget.FieldType.INLINEHTML
			}).defaultValue = buttonHTML;
			portlet.addField({
					id : "custpage_location"
				,	label : "Rental Location"
				,	type : widget.FieldType.SELECT
				,	source : "location"
			});
			portlet.addField({
					id : "custpage_rent_start"
				,	label : "Rental Start Date"
				,	type : widget.FieldType.DATE
			});
			portlet.addField({
					id : "custpage_rent_end"
				,	label : "Rental End Date"
				,	type : widget.FieldType.DATE
			});
			var defField = portlet.addField({
					id : "custpage_deficiencies_only"
				,	label : "Only Show Deficiencies"
				,	type : widget.FieldType.CHECKBOX
			});
			var pdfField = portlet.addField({
					id : "custpage_print_pdf"
				,	label : "Print PDF"
				,	type : widget.FieldType.CHECKBOX
			});
			if (col === 2) {
				portlet.addField({
						id : "custpage_spacer_label"
					,	label : " "
					,	type : widget.FieldType.TEXTAREA
				}).updateDisplayType({
						displayType : widget.FieldDisplayType.INLINE
				}).defaultValue = '<br /><br /><br />&nbsp;';
			}
			var itemCatField = portlet.addField({
					id : "custpage_item_category"
				,	label : "Item Category"
				,	type : widget.FieldType.MULTISELECT
			}).updateBreakType({
				breakType : widget.FieldBreakType.STARTCOL
			});
			if (col === 2) {
				defField.padding = 1;
				pdfField.padding = 1;
				itemCatField.padding = 2;
			}
			var itemSubCatField = portlet.addField({
					id : "custpage_item_sub_category"
				,	label : "Item Subcategory"
				,	type : widget.FieldType.MULTISELECT
			}).updateBreakType({
				breakType : widget.FieldBreakType.STARTCOL
			});
			if (col === 2) {
				itemSubCatField.padding = 2;
			}
			
			portlet.addField({
					id : "custpage_service_url"
				,	label : "service url"
				,	type : widget.FieldType.TEXT
			}).updateDisplayType({
					displayType : widget.FieldDisplayType.HIDDEN
			}).defaultValue = url.resolveScript({
					scriptId : "customscript_ng_cs_sl_rental_inv_dsp"
				,	deploymentId : "customdeploy_ng_cs_sl_rental_inv_dsp_dep"
				,	returnExternalUrl: false
			});
			
			REPORT.addTimeZoneOffsetHTML(portlet);
			var itemCats = REPORT.getItemCategoryData(),
				itemSubCats = REPORT.getItemSubCategoryData(),
				i;
			
			for (i = 0; i < itemCats.length; i++) {
				itemCatField.addSelectOption({
						value : itemCats[i].id
					,	text : itemCats[i].text
				});
			}
			for (i = 0; i < itemSubCats.length; i++) {
				itemSubCatField.addSelectOption({
						value : itemSubCats[i].id
					,	text : itemSubCats[i].text
				});
			}
			
			portlet.clientScriptModulePath = '../Client/ng_cs_clientRentalInventoryPortletClient.js';
		} else {
			portlet.addField({
					id : "custpage_rental_label"
				,	label : "Rental Inventory is not enabled on this system"
				,	type : widget.FieldType.LABEL
			});
		}
	}
	
	return {
		render: render
	};
	
});
