/**
 * newgen.library.cs.web.js
 * @NApiVersion 2.x
 * @NModuleScope SameAccount
 */

define([
  "N/format",
  "N/record",
  "N/search",
  "N/url",
  "N/xml",
  "N/https",
  "./newgen.library.v2",
], /**
 * @param {format} format
 * @param {record} record
 * @param {search} search
 * @param {url} url
 * @param {xml} nXML
 * @param {https} https
 */ function (format, record, search, url, nXML, https, NG) {
  var _SettingsFields = new Array(
    "custrecord_ng_cs_gen_rand_email",
    "custrecord_ng_cs_rand_email_domain",
    "custrecord_ng_cs_rand_email_prefix",
    "custrecord_ng_cs_web_img_folder_id",
    "custrecord_ng_cs_exhb_kit_folder_id",
    "custrecord_ng_cs_use_undep_funds",
    "custrecord_ng_cs_def_dep_account",
    "custrecord_ng_cs_use_show_auditing",
    "custrecord_ng_cs_show_audit_form",
    "custrecord_ng_cs_use_pre_invoicing",
    "custrecord_ng_cs_pre_invoicing_form",
    "custrecord_ng_cs_use_alt_forms",
    "custrecord_ng_cs_prev_adtl_orders",
    "custrecord_ng_cs_allow_mult_billng_part",
    "custrecord_ng_cs_use_show_tax",
    "custrecord_ng_cs_use_cancl_charge",
    "custrecord_ng_cs_cancl_charge_item",
    "custrecord_ng_cs_def_canc_chrg_pct",
    "custrecord_ng_cs_canc_threshold",
    "custrecord_ng_cs_booth_ord_forms",
    "custrecord_ng_cs_add_item_forms",
    "custrecord_ng_cs_do_not_prompt_terms",
    "custrecord_ng_cs_prompt_exclusion_roles",
    "custrecord_ng_cs_import_log_record_id",
    "custrecord_ng_cs_import_log_search_id",
    "custrecord_ng_cs_payment_ar_account",
    "custrecord_ng_cs_cc_auth_item",
    "custrecord_ng_cs_pymnt_fail_eml_template",
    "custrecord_ng_cs_use_job_numbering",
    "custrecord_ng_cs_simple_job_numbering",
    "custrecord_ng_cs_job_num_prefix",
    "custrecord_ng_cs_custom_job_numbering",
    "custrecord_ng_cs_use_multi_cc_proc",
    "custrecord_ng_cs_no_prompt_under_zero",
    "custrecord_ng_cs_prompt_for_new_line",
    "custrecord_ng_cs_retain_last_show",
    "custrecord_ng_cs_retain_last_item_cat",
    "custrecord_ng_cs_default_show_subsidiary",
    "custrecord_ng_cs_no_billed_order_editing",
    "custrecord_ng_cs_billed_ord_edit_users",
    "custrecord_ng_cs_use_scripted_pynt_frm",
    "custrecord_ng_cs_clear_order_cc_details",
    "custrecord_ng_cs_send_invoice_fail_email",
    "custrecord_ng_cs_inv_fail_sender",
    "custrecord_ng_cs_inv_fail_recip",
    "custrecord_ng_cs_inv_fail_cc",
    "custrecord_ng_cs_def_exhb_dept",
    "custrecord_ng_cs_def_exhb_ord_type",
    "custrecord_ng_cs_send_exhib_invoice",
    "custrecord_ng_cs_exhib_invoice_sender",
    "custrecord_ng_cs_exhb_inv_email_template",
    "custrecord_ng_cs_inv_email_conditions",
    "custrecord_ng_cs_give_contacts_access",
    "custrecord_ng_cs_allow_mass_booth_delete",
    "custrecord_ng_cs_mass_booth_delete_roles",
    "custrecord_ng_cs_send_web_pymnt_email",
    "custrecord_ng_cs_web_pymnt_notice_sender",
    "custrecord_ng_cs_web_pymnt_fail_recip",
    "custrecord_ng_cs_web_pymnt_fail_cc",
    "custrecord_ng_cs_csv_import_folder_id",
    "custrecord_ng_cs_allow_show_autopay",
    "custrecord_ng_cs_pymt_rcpt_template",
    "custrecord_ng_cs_dpst_rcpt_template",
    "custrecord_ng_cs_log_time_zone",
    "custrecord_ng_cs_freight_minimum",
    "custrecord_ng_cs_prev_bo_redir_alert",
    "custrecord_ng_cs_dflt_shw_tbl_form",
    "custrecord_ng_cs_dflt_exhibtr_form",
    "custrecord_ng_cs_dflt_booth_order_form",
    "custrecord_ng_cs_activity_log_rec_id",
    "custrecord_ng_cs_activity_log_srch_id",
    "custrecord_ng_cs_auto_charge_web_orders",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_autochrg_cat_excl",
    "custrecord_ng_cs_mastercard",
    "custrecord_ng_cs_visa",
    "custrecord_ng_cs_amex",
    "custrecord_ng_cs_discover",
    "custrecord_ng_cs_default_adv_show_price",
    "custrecord_ng_cs_default_std_show_price",
    "custrecord_ng_cs_default_onst_show_price",
    "custrecord_ng_cs_payment_type",
    "custrecord_ng_cs_dflt_d_calc_date_types",
    "custrecord_ng_cs_dflt_labor_date_types",
    "custrecord_ng_cs_supervisor_item",
    "custrecord_ng_cs_exempt_estimated_items",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_default_show_mgmt_price",
    "custrecord_ng_cs_name_number_ordering",
    "custrecord_ng_cs_name_number_separator",
    "custrecord_ng_cs_use_custom_job",
    "custrecord_ng_cs_def_show_mgmt_dept",
    "custrecord_ng_cs_def_show_mgmt_ord_type",
    "custrecord_ng_cs_default_show_date",
    "custrecord_ng_cs_show_mgt_forms",
    "custrecord_ng_cs_enable_freight_opts_opt",
    "custrecord_ng_cs_enable_graphics_option",
    "custrecord_ng_cs_dflt_show_mgmt_ord_form",
    "custrecord_ng_cs_enable_orientation_opt",
    "custrecord_ng_cs_enable_labor_matrix_opt",
    "custrecord_ng_cs_enforce_item_max_qty",
    "custrecord_ng_cs_enable_paytrace",
    "custrecord_ng_cs_show_calendar_id",
    "custrecord_ng_cs_acct_domain_url",
    "custrecord_ng_cs_algolia_application_id",
    "custrecord_ng_cs_algolia_search_key",
    "custrecord_ng_cs_algolia_api_key",
    "custrecord_ng_cs_algolia_index",
    "custrecord_ng_cs_fclty_addy_template",
    "custrecord_ng_cs_wrhs_addy_template",
    "custrecord_ng_cs_name_from_subsidiary",
    "custrecord_ng_cs_booth_num_line_text",
    "custrecord_ng_cs_wo_img",
    "custrecord_ng_cs_wo_logo_img_url",
    "custrecord_ng_cs_inv_transfer_type",
    "custrecord_ng_cs_transfer_count_markup",
    "custrecord_ng_cs_trnsfr_exmpt_cats",
    "custrecord_ng_cs_default_transfer_from",
    "custrecord_ng_cs_default_to_as_st_loc",
    "custrecord_ng_cs_item_rprts_exluded_cats",
    "custrecord_ng_cs_exhb_wo_exluded_cats",
    "custrecord_ng_cs_hide_bthchklst_cnt_info",
    "custrecord_ng_cs_shade_alt_report_lines",
    "custrecord_ng_cs_report_line_shade_hex",
    "custrecord_ng_cs_report_item_display",
    "custrecord_ng_cs_graphics_item_cat",
    "custrecord_ng_cs_canonical_base_url",
    "custrecord_ng_cs_use_cc_conv_fee",
    "custrecord_ng_cs_cc_conv_fee_rate",
    "custrecord_ng_cs_cc_conv_fee_item",
    "custrecord_ng_cs_cc_conv_fee_order_types",
    "custrecord_ng_cs_default_show_move_in",
    "custrecord_ng_cs_default_exhib_move_in",
    "custrecord_ng_cs_default_show_move_out",
    "custrecord_ng_cs_default_exhib_move_out",
    "custrecord_ng_cs_wo_logo_img_dims"
  );

  var _cs_settings = {
    openShowsSearch: "customsearch_open_shows",
    AlgoliaWriteURL: "https://{0}.algolia.net/1/indexes/{1}/",
    AlgoliaReadURL: "https://{0}-dsn.algolia.net/1/indexes/{1}/",
    LogBaseUrl:
      "/app/common/search/searchresults.nl?rectype={0}&searchtype=Custom&CUSTRECORD_NGLOG_DETAILS={1}&style=NORMAL&CUSTRECORD_NGLOG_DETAILStype=CONTAINS&report=&grid=&searchid={2}&sortcol=Custom_OWNER_raw&sortdir=ASC&csv=HTML&OfficeXML=F&pdf=&size=50&twbx=F",

    sqftItems: new Array(),
    daysItems: new Array(),
    durationItems: new Array(),
    sqdItems: new Array(),
    freightItems: new Array(),
    colorItems: new Array(),
    sizeItems: new Array(),
    orientationItems: new Array(),
    laborItems: new Array(),
    barrelItems: new Array(),
    freightOptsA: new Array(),
    freightOptsB: new Array(),
    laborOptItemsA: new Array(),
    laborOptItemsB: new Array(),
    graphicsItems: new Array(),

    rType: {
      booth: "customrecord_show_booths",
      showtable: "customrecord_show",
      boothorder: "salesorder",
      payment: "customerpayment",
      refund: "customerrefund",
      deposit: "customerdeposit",
      exhibitor: "customer",
      invoice: "invoice",
      credit: "creditmemo",
      displayform: "customrecord_display_forms",
      showdisplayform: "customrecord_show_display_forms",
      showdate: "customrecord_show_date",
    },

    scripts: {
      lastShow: {
        scriptid: "customscript_last_show_get_set",
        deployid: "customdeploy_last_show_get_set_dep",
      },
      lastItemCat: {
        scriptid: "customscript_ng_cs_sl_last_item_cat",
        deployid: "customdeploy_ng_cs_sl_last_item_cat_dep",
      },
      other: {},
    },

    lists: {},

    fields: {
      tran: {
        booth: "custbody_booth",
        show_table: "custbody_show_table",
      },

      item: {
        sqft: "custitem_is_sqft",
        days: "custitem_is_days",
        showdur: "custitem_show_duration",
        freight: "custitem_is_freight",
        color: "custitem_has_color_options",
        size: "custitem_has_size_options",
        orientation: "custitem_has_orient_options",
        labor: "custitem_labor_item",
        graphics: "custitem_ng_cs_has_graphic_options",
        colorSel: "custitem27",
        sizeSel: "custitem28",
        orientSel: "custitem_orientation",
        graphicSel: "custitem42",
      },

      itemOpts: {
        supervision: "custcol_labor_sup_required",
        labordate: "custcol_labor_date",
        laborendtime: "custcol_labor_end_time",
        laboresthours: "custcol_labor_est_hours",
        laborworkers: "custcol_labor_workers",
        labortime: "custcol_labor_time",
      },

      entity: {
        lastshow: "",
        lastwebshow: "",
        lastwebbooth: "",
      },

      showTable: {
        complete: "custrecord_cs_st_show_complete",
      },
    },

    matrixOptionMap: {
      custcol_sizeopt: {
        sourcefrom: "custitem28",
        internalid: "custcol_sizeopt",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Size",
        type: "select",
      },
      custcol_coloropt: {
        sourcefrom: "custitem27",
        internalid: "custcol_coloropt",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Color",
        type: "select",
      },
      custcol_graphmat_substrate: {
        sourcefrom: "custitem42",
        internalid: "custcol_graphmat_substrate",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Graphic Material",
        type: "select",
      },
      custcol_orientation: {
        sourcefrom: "custitem_orientation",
        internalid: "custcol_orientation",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Orientation",
        type: "select",
      },
      custcol1: {
        sourcefrom: "custitem43",
        internalid: "custcol1",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Screen Size",
        type: "select",
      },
      custcol_labor_sup_required: {
        internalid: "custcol_labor_sup_required",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Supervision Required",
        type: "text",
      },
      custcol_attach_document: {
        internalid: "custcol_attach_document",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Attach Supporting File",
        type: "text",
      },
      custcol_labor_date: {
        internalid: "custcol_labor_date",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Date",
        type: "text",
      },
      custcol_labor_est_hours: {
        internalid: "custcol_labor_est_hours",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Estimated Hours",
        type: "text",
      },
      custcol_canc_descr: {
        internalid: "custcol_canc_descr",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Cancelled Item",
        type: "text",
      },
      custcol_labor_workers: {
        internalid: "custcol_labor_workers",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Number of Workers",
        type: "text",
      },
      custcol_labor_time: {
        internalid: "custcol_labor_time",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Time",
        type: "text",
      },
      custcol_labor_end_time: {
        internalid: "custcol_labor_end_time",
        ismandatory: true,
        ismatrixdimension: false,
        label: "End Time",
        type: "text",
      },
    },

    separators: {
      1: " ",
      2: " - ",
      3: " -- ",
      4: " | ",
      5: " || ",
      6: " : ",
      7: " :: ",
      8: " <> ",
    },
  };

  function resolveSelectValue(objValue) {
    var selVal = null;
    if (!NG.tools.isEmpty(objValue) && !NG.tools.isEmpty(objValue[0])) {
      selVal = objValue[0].value;
    }
    return selVal;
  }

  function resolveMultiSelectValue(objValue) {
    var arr = new Array();
    if (!NG.tools.isEmpty(objValue) && !NG.tools.isEmpty(objValue[0])) {
      for (var i = 0; i < objValue.length; i++) {
        arr.push(objValue[i].value);
      }
    }
    return arr;
  }

  function _trigger() {
    var _NG_SETTINGS = search.lookupFields({
      type: "customrecord_ng_cs_settings",
      id: "1",
      columns: _SettingsFields,
    });

    _cs_settings.GenRandomEmail = _NG_SETTINGS.custrecord_ng_cs_gen_rand_email;
    _cs_settings.RandEmailDomain =
      _NG_SETTINGS.custrecord_ng_cs_rand_email_domain;
    _cs_settings.RandEmailPrefix =
      _NG_SETTINGS.custrecord_ng_cs_rand_email_prefix;
    _cs_settings.WebImageFolderID =
      _NG_SETTINGS.custrecord_ng_cs_web_img_folder_id || null;
    _cs_settings.ExhibitorKitFolderID =
      _NG_SETTINGS.custrecord_ng_cs_exhb_kit_folder_id || null;
    _cs_settings.ExhibitorKitFolderPath =
      _NG_SETTINGS.custrecord_ng_cs_exhb_kit_path || "";
    _cs_settings.UndepositedFunds =
      _NG_SETTINGS.custrecord_ng_cs_use_undep_funds;
    _cs_settings.DefaultDepositAccount =
      _NG_SETTINGS.custrecord_ng_cs_def_dep_account || null;
    _cs_settings.UseShowAuditing =
      _NG_SETTINGS.custrecord_ng_cs_use_show_auditing;
    _cs_settings.UsePreInvoicing =
      _NG_SETTINGS.custrecord_ng_cs_use_pre_invoicing;
    _cs_settings.UseAlternateForms =
      _NG_SETTINGS.custrecord_ng_cs_use_alt_forms;
    _cs_settings.PreventAdditionalOrders =
      _NG_SETTINGS.custrecord_ng_cs_prev_adtl_orders;
    _cs_settings.AllowMultiBillingParties =
      _NG_SETTINGS.custrecord_ng_cs_allow_mult_billng_part;
    _cs_settings.UseTaxCode = _NG_SETTINGS.custrecord_ng_cs_use_show_tax; // DEPRECATE
    _cs_settings.UseCancellationCharge =
      _NG_SETTINGS.custrecord_ng_cs_use_cancl_charge;
    _cs_settings.CancellationChargePct =
      _NG_SETTINGS.custrecord_ng_cs_def_canc_chrg_pct || "0.0%";
    _cs_settings.DoNotPromptIfTerms =
      _NG_SETTINGS.custrecord_ng_cs_do_not_prompt_terms;
    _cs_settings.ImportLogRecordID =
      _NG_SETTINGS.custrecord_ng_cs_import_log_record_id || null;
    _cs_settings.ImportLogSearchID =
      _NG_SETTINGS.custrecord_ng_cs_import_log_search_id || null;
    _cs_settings.UseJobNumbering =
      _NG_SETTINGS.custrecord_ng_cs_use_job_numbering;
    _cs_settings.SimpleJobNumbering =
      _NG_SETTINGS.custrecord_ng_cs_simple_job_numbering;
    _cs_settings.JobNumberPrefix =
      _NG_SETTINGS.custrecord_ng_cs_job_num_prefix || "";
    _cs_settings.CustomJobNumbering =
      _NG_SETTINGS.custrecord_ng_cs_custom_job_numbering;
    _cs_settings.MultiCCProcessing =
      _NG_SETTINGS.custrecord_ng_cs_use_multi_cc_proc;
    _cs_settings.NoPromptUnderZero =
      _NG_SETTINGS.custrecord_ng_cs_no_prompt_under_zero;
    _cs_settings.PromptForNewLines =
      _NG_SETTINGS.custrecord_ng_cs_prompt_for_new_line;
    _cs_settings.RetainLastShow =
      _NG_SETTINGS.custrecord_ng_cs_retain_last_show;
    _cs_settings.RetainLastItemCat =
      _NG_SETTINGS.custrecord_ng_cs_retain_last_item_cat;
    _cs_settings.BlockedBilledOrderEditing =
      _NG_SETTINGS.custrecord_ng_cs_no_billed_order_editing;
    _cs_settings.UseScriptedPaymentForm =
      _NG_SETTINGS.custrecord_ng_cs_use_scripted_pynt_frm;
    _cs_settings.ClearBoothOrderCCDetails =
      _NG_SETTINGS.custrecord_ng_cs_clear_order_cc_details;
    _cs_settings.SendInvoiceFailureEmail =
      _NG_SETTINGS.custrecord_ng_cs_send_invoice_fail_email;
    _cs_settings.SendExhibitorInvoiceEmail =
      _NG_SETTINGS.custrecord_ng_cs_send_exhib_invoice;
    _cs_settings.GiveContactsWebStoreAccess =
      _NG_SETTINGS.custrecord_ng_cs_give_contacts_access;
    _cs_settings.AllowMassBoothDeletion =
      _NG_SETTINGS.custrecord_ng_cs_allow_mass_booth_delete;
    _cs_settings.SendWebPaymentNotice =
      _NG_SETTINGS.custrecord_ng_cs_send_web_pymnt_email;
    _cs_settings.ImportCSVFolder =
      _NG_SETTINGS.custrecord_ng_cs_csv_import_folder_id || null;
    _cs_settings.AllowShowAutoPayment =
      _NG_SETTINGS.custrecord_ng_cs_allow_show_autopay;
    _cs_settings.PrevntBthOrderRedirectAlert =
      _NG_SETTINGS.custrecord_ng_cs_prev_bo_redir_alert;
    _cs_settings.DefaultShowTableForm =
      _NG_SETTINGS.custrecord_ng_cs_dflt_shw_tbl_form;
    _cs_settings.ActivityLogRecordId =
      _NG_SETTINGS.custrecord_ng_cs_activity_log_rec_id || null;
    _cs_settings.ActivityLogSearchId =
      _NG_SETTINGS.custrecord_ng_cs_activity_log_srch_id || null;
    _cs_settings.AutoChargeWebOrders =
      _NG_SETTINGS.custrecord_ng_cs_auto_charge_web_orders;
    _cs_settings.GetNonWebOrderAuth =
      _NG_SETTINGS.custrecord_ng_cs_auth_non_web_orders;
    _cs_settings.ExemptEstimatedItems =
      _NG_SETTINGS.custrecord_ng_cs_exempt_estimated_items;
    _cs_settings.EnableWorkOrderPrinting =
      _NG_SETTINGS.custrecord_ng_cs_enable_wo_printing;
    _cs_settings.NSSystemDomain = _NG_SETTINGS.custrecord_ng_cs_acct_domain_url;
    _cs_settings.UseCustomJob = _NG_SETTINGS.custrecord_ng_cs_use_custom_job;
    _cs_settings.EnableGraphicOption =
      _NG_SETTINGS.custrecord_ng_cs_enable_graphics_option;
    _cs_settings.EnableOrientationOption =
      _NG_SETTINGS.custrecord_ng_cs_enable_orientation_opt;
    _cs_settings.EnforceItemMaxQuantity =
      _NG_SETTINGS.custrecord_ng_cs_enforce_item_max_qty;
    _cs_settings.ShowCalendar =
      _NG_SETTINGS.custrecord_ng_cs_show_calendar_id || "1";
    _cs_settings.InvoiceFailureCC =
      _NG_SETTINGS.custrecord_ng_cs_inv_fail_cc || null;
    _cs_settings.WebPaymentFailureCC =
      _NG_SETTINGS.custrecord_ng_cs_web_pymnt_fail_cc || null;
    _cs_settings.WarehouseAddyTmplt =
      _NG_SETTINGS.custrecord_ng_cs_wrhs_addy_template || "";
    _cs_settings.FacilityAddyTmplt =
      _NG_SETTINGS.custrecord_ng_cs_fclty_addy_template || "";
    _cs_settings.UseSubsidiaryName =
      _NG_SETTINGS.custrecord_ng_cs_name_from_subsidiary;
    _cs_settings.BoothNumLineText =
      _NG_SETTINGS.custrecord_ng_cs_booth_num_line_text || null;
    _cs_settings.TransferCountMarkup =
      _NG_SETTINGS.custrecord_ng_cs_transfer_count_markup || "0.00%";
    _cs_settings.DefaultTargetFromShow =
      _NG_SETTINGS.custrecord_ng_cs_default_to_as_st_loc;
    _cs_settings.HideChecklistContact =
      _NG_SETTINGS.custrecord_ng_cs_hide_bthchklst_cnt_info;
    _cs_settings.ShadeAltRptLines =
      _NG_SETTINGS.custrecord_ng_cs_shade_alt_report_lines;
    _cs_settings.RptLineShadeHex =
      _NG_SETTINGS.custrecord_ng_cs_report_line_shade_hex || "";
    _cs_settings.UseConvenienceFee =
      _NG_SETTINGS.custrecord_ng_cs_use_cc_conv_fee;
    _cs_settings.ConvenienceFeeRate =
      _NG_SETTINGS.custrecord_ng_cs_cc_conv_fee_rate || "0.00%";
    _cs_settings.EnablePayTrace = _NG_SETTINGS.custrecord_ng_cs_enable_paytrace;
    //	_cs_settings.EnableRentals =				_NG_SETTINGS.custrecord_ng_cs_enable_rentals;

    _cs_settings.FreightMinimum = new Number(
      !isNaN(_NG_SETTINGS.custrecord_ng_cs_freight_minimum)
        ? _NG_SETTINGS.custrecord_ng_cs_freight_minimum || "0"
        : "0"
    );

    _cs_settings.WorkOrderImage =
      _NG_SETTINGS.custrecord_ng_cs_wo_logo_img || "";

    _cs_settings.WorkOrderImageURL = nXML.escape({
      xmlText: _NG_SETTINGS.custrecord_ng_cs_wo_logo_img_url || "",
    });

    _cs_settings.WorkOrderImageDimensions =
      _NG_SETTINGS.custrecord_ng_cs_wo_logo_img_dims && typeof _NG_SETTINGS.custrecord_ng_cs_wo_logo_img_dims === 'string'
        ? JSON.parse(_NG_SETTINGS.custrecord_ng_cs_wo_logo_img_dims)
        : null;

    _cs_settings.DefaultDepositAccount = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_dep_account
    );
    _cs_settings.ShowAuditForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_show_audit_form
    );
    _cs_settings.PreInvoiceForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_pre_invoicing_form
    );
    _cs_settings.CancellationChargeID = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cancl_charge_item
    );
    _cs_settings.CancellationChargeThreshold = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_canc_threshold
    );
    _cs_settings.PaymentARAccount = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_payment_ar_account
    );
    _cs_settings.AuthItem = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cc_auth_item
    );
    _cs_settings.PaymentFailureTemplate = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_pymnt_fail_eml_template
    );
    _cs_settings.DefaultSubsidiary = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_subsidiary
    );
    _cs_settings.InvoiceFailureSender = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_inv_fail_sender
    );
    _cs_settings.InvoiceFailureRecipient = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_inv_fail_recip
    );
    _cs_settings.DefaultExhibitorDepartment = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_exhb_dept
    );
    _cs_settings.DefaultExhibitorOrderType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_exhb_ord_type
    );
    _cs_settings.DefaultShowMgmtDepartment = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_show_mgmt_dept
    );
    _cs_settings.DefaultShowMgmtOrderType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_show_mgmt_ord_type
    );
    _cs_settings.ExhibitorInvoiceSender = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_exhib_invoice_sender
    );
    _cs_settings.ExhibitorInvoiceTemplate = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_exhb_inv_email_template
    );
    _cs_settings.WebPaymentNoticeSender = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_web_pymnt_notice_sender
    );
    _cs_settings.WebPaymentFailureRecipient = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_web_pymnt_fail_recip
    );
    _cs_settings.PaymentReceiptForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_pymt_rcpt_template
    );
    _cs_settings.DepositReceiptForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dpst_rcpt_template
    );
    _cs_settings.TimeZone = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_log_time_zone
    );
    _cs_settings.DefaultExhibitorForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_exhibtr_form
    );
    _cs_settings.DefaultBoothOrderForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_booth_order_form
    );
    _cs_settings.DefaultShowMgmtForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_show_mgmt_ord_form
    );
    _cs_settings.MastercardPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_mastercard
    );
    _cs_settings.VisaPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_visa
    );
    _cs_settings.AmexPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_amex
    );
    _cs_settings.DiscoverPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_discover
    );
    _cs_settings.DefaultAdvPriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_adv_show_price
    );
    _cs_settings.DefaultStdPriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_std_show_price
    );
    _cs_settings.DefaultOnSitePriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_onst_show_price
    );
    _cs_settings.DefaultShowMgmtPriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_mgmt_price
    );
    _cs_settings.PaymentType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_payment_type
    );
    _cs_settings.SupervisorItem = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_supervisor_item
    );
    _cs_settings.NameNumberOrder = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_name_number_ordering
    );
    _cs_settings.NameNumberSeparator = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_name_number_separator
    );
    _cs_settings.DefaultShowDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_date
    );
    _cs_settings.DefaultShowMoveInDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_move_in
    );
    _cs_settings.DefaultShowMoveOutDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_move_out
    );
    _cs_settings.DefaultExhibMoveInDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_exhib_move_in
    );
    _cs_settings.DefaultExhibMoveOutDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_exhib_move_out
    );
    _cs_settings.ContactMassMailCategory = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_contact_camp_cat
    );
    _cs_settings.ContactMassMailAudience = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_contact_camp_aud
    );
    _cs_settings.ContactMassMailVertical = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_contact_camp_vert
    );
    _cs_settings.ContactMassMailBMailChan = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_contact_camp_eml_chan
    );
    _cs_settings.ContactMassMailFMailChan = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_contact_camp_filt_chan
    );
    _cs_settings.DefaultTransferSourceLoc = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_transfer_from
    );
    _cs_settings.RptItemNameDisplay = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_report_item_display
    );
    _cs_settings.GraphicsItemCat = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_graphics_item_cat
    );
    _cs_settings.ConvenienceFeeItem = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cc_conv_fee_item
    );

    _cs_settings.BoothOrderFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_booth_ord_forms
    );
    _cs_settings.AddItemFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_add_item_forms
    );
    _cs_settings.ShowMgtFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_show_mgt_forms
    );
    _cs_settings.NoPromptRoles = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_prompt_exclusion_roles
    );
    _cs_settings.AuthorizedBilledOrderEditors = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_billed_ord_edit_users
    );
    _cs_settings.ShowMgmtOrderTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_show_mgmt_ord_types
    );
    _cs_settings.ExhibitorInvoiceConditions = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_inv_email_conditions
    );
    _cs_settings.MassBoothDeletionRoles = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_mass_booth_delete_roles
    );
    _cs_settings.AutoChargeCategoryExclusions = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_autochrg_cat_excl
    );
    _cs_settings.DCalcDateTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_d_calc_date_types
    );
    _cs_settings.LaborDateTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_labor_date_types
    );
    _cs_settings.TransferExemptCats = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_trnsfr_exmpt_cats
    );
    _cs_settings.ItemReportsExlcudeCats = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_item_rprts_exluded_cats
    );
    _cs_settings.ExhibWOExlcudeCats = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_exhb_wo_exluded_cats
    );
    _cs_settings.ConvenienceFeeOrderTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
    );

    _cs_settings.AlgoliaApplicationID =
      _NG_SETTINGS.custrecord_ng_cs_algolia_application_id || null;
    _cs_settings.AlgoliaSearchKey =
      _NG_SETTINGS.custrecord_ng_cs_algolia_search_key || null;
    _cs_settings.AlgoliaAPIKey =
      _NG_SETTINGS.custrecord_ng_cs_algolia_api_key || null;
    _cs_settings.AlgoliaIndex =
      _NG_SETTINGS.custrecord_ng_cs_algolia_index || null;
    _cs_settings.AlgoliaCanonicalURL =
      _NG_SETTINGS.custrecord_ng_cs_canonical_base_url || "";

    return _cs_settings;
  }

  var _csFunc = {
    getSelectionItems: function (subsidiary) {
      _cs_settings.sqftItems = new Array();
      _cs_settings.daysItems = new Array();
      _cs_settings.durationItems = new Array();
      _cs_settings.sqdItems = new Array();
      _cs_settings.freightItems = new Array();
      _cs_settings.colorItems = new Array();
      _cs_settings.sizeItems = new Array();
      _cs_settings.orientationItems = new Array();
      _cs_settings.laborItems = new Array();
      _cs_settings.graphicsItems = new Array();

      var filtA_sub = new Array(
        [_cs_settings.fields.item.sqft, "is", "T"],
        "or",
        [_cs_settings.fields.item.days, "is", "T"],
        "or",
        [_cs_settings.fields.item.showdur, "is", "T"],
        "or",
        [_cs_settings.fields.item.freight, "is", "T"],
        "or",
        [_cs_settings.fields.item.labor, "is", "T"]
      );
      var filtA = new Array(["isinactive", "is", "F"], "and", filtA_sub);
      if (subsidiary != null) {
        filtA.push("and", ["subsidiary", "anyof", ["@NONE@", subsidiary]]);
      }
      var colsA = new Array(
        search.createColumn({ name: _cs_settings.fields.item.sqft }),
        search.createColumn({ name: _cs_settings.fields.item.days }),
        search.createColumn({ name: _cs_settings.fields.item.showdur }),
        search.createColumn({ name: _cs_settings.fields.item.freight }),
        search.createColumn({ name: _cs_settings.fields.item.labor })
      );
      var searchA = null;
      try {
        searchA = NG.tools.getSearchResults("item", filtA, colsA, null, 1000);
      } catch (err) {}

      if (searchA != null) {
        for (var i = 0; i < searchA.length; i++) {
          if (
            searchA[i].getValue({ name: _cs_settings.fields.item.sqft }) ==
              "T" &&
            searchA[i].getValue({ name: _cs_settings.fields.item.days }) == "T"
          ) {
            _cs_settings.sqdItems.push(searchA[i].id);
          } else if (
            searchA[i].getValue({ name: _cs_settings.fields.item.sqft }) == "T"
          ) {
            _cs_settings.sqftItems.push(searchA[i].id);
          } else if (
            searchA[i].getValue({ name: _cs_settings.fields.item.days }) == "T"
          ) {
            _cs_settings.daysItems.push(searchA[i].id);
          }
          if (
            searchA[i].getValue({ name: _cs_settings.fields.item.showdur }) ==
            "T"
          ) {
            _cs_settings.durationItems.push(searchA[i].id);
          }
          if (
            searchA[i].getValue({ name: _cs_settings.fields.item.freight }) ==
            "T"
          ) {
            _cs_settings.freightItems.push(searchA[i].id);
          }
          if (
            searchA[i].getValue({ name: _cs_settings.fields.item.labor }) == "T"
          ) {
            _cs_settings.laborItems.push(searchA[i].id);
          }
        }
      }

      var filtB_sub = new Array(
        [_cs_settings.fields.item.color, "is", "T"],
        "or",
        [_cs_settings.fields.item.size, "is", "T"]
      );
      if (_cs_settings.EnableOrientationOption) {
        filtB_sub.push("or", [_cs_settings.fields.item.orientation, "is", "T"]);
      }
      if (_cs_settings.EnableGraphicOption) {
        filtB_sub.push("or", [_cs_settings.fields.item.graphics, "is", "T"]);
      }
      var filtB = new Array(
        ["isinactive", "is", "F"],
        "and",
        ["matrix", "is", "T"],
        "and",
        ["matrixchild", "is", "F"],
        "and",
        filtB_sub
      );
      if (subsidiary != null) {
        filtB.push("and", ["subsidiary", "anyof", ["@NONE@", subsidiary]]);
      }
      var colsB = new Array(
        search.createColumn({ name: "itemid" }),
        search.createColumn({ name: _cs_settings.fields.item.color }),
        search.createColumn({ name: _cs_settings.fields.item.size })
      );
      if (_cs_settings.EnableOrientationOption) {
        colsB.push(
          search.createColumn({ name: _cs_settings.fields.item.orientation })
        );
      }
      if (_cs_settings.EnableGraphicOption) {
        colsB.push(
          search.createColumn({ name: _cs_settings.fields.item.graphics })
        );
      }
      var searchB = null;
      try {
        searchB = NG.tools.getSearchResults("item", filtB, colsB, null, 1000);
      } catch (err) {}
      if (searchB != null) {
        for (var i = 0; i < searchB.length; i++) {
          if (
            searchB[i].getValue({ name: _cs_settings.fields.item.color }) == "T"
          ) {
            _cs_settings.colorItems.push(searchB[i].id);
          }
          if (
            searchB[i].getValue({ name: _cs_settings.fields.item.size }) == "T"
          ) {
            _cs_settings.sizeItems.push(searchB[i].id);
          }
          if (
            _cs_settings.EnableOrientationOption &&
            searchB[i].getValue({
              name: _cs_settings.fields.item.orientation,
            }) == "T"
          ) {
            _cs_settings.orientationItems.push(searchB[i].id);
          }
          if (
            _cs_settings.EnableGraphicOption &&
            searchB[i].getValue({ name: _cs_settings.fields.item.graphics }) ==
              "T"
          ) {
            _cs_settings.graphicsItems.push(searchB[i].id);
          }
        }
      }
    },

    getLastShow: function (values, isClient) {
      var path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastShow.scriptid,
        deploymentId: _cs_settings.scripts.lastShow.deployid,
        returnExternalUrl: true,
      });
      var request = https.post({ url: path, body: values });
      return request != null ? request.body : null;
    },

    setLastShow: function (values, isClient) {
      var path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastShow.scriptid,
        deploymentId: _cs_settings.scripts.lastShow.deployid,
        returnExternalUrl: true,
      });
      https.post({ url: path, body: values });
    },

    getLastItemCat: function (values, isClient) {
      var path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastItemCat.scriptid,
        deploymentId: _cs_settings.scripts.lastItemCat.deployid,
        returnExternalUrl: true,
      });
      var request = https.post({ url: path, body: values });
      return request != null ? request.body : null;
    },

    setLastItemCat: function (values, isClient) {
      var path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastItemCat.scriptid,
        deploymentId: _cs_settings.scripts.lastItemCat.deployid,
        returnExternalUrl: true,
      });
      https.post({ url: path, body: values });
    },

    getOpenShows: function (showsField, isMulti, isClient) {
      if (!isMulti) {
        showsField.addSelectOption({ value: "", text: "", isSelected: true });
      }
      var sFilt = new Array(["isinactive", "is", "F"], "and", [
        "custrecord_cs_st_show_complete",
        "anyof",
        ["@NONE@", "2"],
      ]);
      var sCols = new Array(
        search.createColumn({ name: "name" }),
        search.createColumn({ name: "custrecord_show_type" })
      );

      var results = null;
      try {
        results = NG.tools.getSearchResults(
          "customrecord_show",
          sFilt,
          sCols,
          null,
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered getting current show listing");
      }

      if (results != null) {
        var showData = new Array();
        for (var r = 0; r < results.length; r++) {
          showData.push({
            id: results[r].id,
            name: results[r].getValue("name"),
          });
        }
        showData.sort(function (a, b) {
          return a.name > b.name ? 1 : b.name > a.name ? -1 : 0;
        });
        for (var s = 0; s < showData.length; s++) {
          showsField.addSelectOption({
            value: showData[s].id,
            text: showData[s].name,
          });
        }
      }

      return showsField;
    },

    checkDuplicateContact: function (email, fName, lName, cName) {
      //			var filt = new Array();
      //			if (!NG.tools.isEmpty(email)) {
      //				filt.push(new nlobjSearchFilter("email", null, "is", email, null));
      //			} else if (!NG.tools.isEmpty(fName) && !NG.tools.isEmpty(lName)) {
      //				filt.push(new nlobjSearchFilter("firstname", null, "is", fName, null), new nlobjSearchFilter("lastName", null, "is", lName, null));
      //			} else if (!NG.tools.isEmpty(cName)) {
      //				filt.push(new nlobjSearchFilter("entityid", null, "contains", cName, null));
      //			} else {
      //				return null;
      //			}
      //			var dupeSearch = null;
      //			try {
      //				dupeSearch = nlapiSearchRecord("contact", null, filt, null);
      //			} catch (err) {
      //				NG.log.logError(err, "Error encountered searching for duplicate contacts", "Search values: " + JSON.stringify({ email : email , fname : fName , lName : lName }));
      //			}
      //
      //			if (dupeSearch != null) {
      //				return dupeSearch[0].getId();
      //			} else {
      return null;
      //			}
    },

    checkDuplicateContactAlt: function (email, fName, lName, cName, exhbID) {
      //			var filt = new Array(
      //				new nlobjSearchFilter("company", null, "anyof", [exhbID], null)
      //			);
      //			if (!NG.tools.isEmpty(fName) && !NG.tools.isEmpty(lName)) {
      //				filt.push(new nlobjSearchFilter("firstname", null, "is", fName, null), new nlobjSearchFilter("lastName", null, "is", lName, null));
      //			} else if (!NG.tools.isEmpty(cName)) {
      //				filt.push(new nlobjSearchFilter("entityid", null, "contains", cName, null));
      //			} else {
      //				return null;
      //			}
      //			var dupeSearch = null;
      //			try {
      //				dupeSearch = nlapiSearchRecord("contact", null, filt, null);
      //			} catch (err) {
      //				NG.log.logError(err, "Error encountered searching for duplicate contacts", "Search values: " + JSON.stringify({ email : email , fname : fName , lName : lName }));
      //			}
      //
      //			if (dupeSearch != null) {
      //				return dupeSearch[0].getId();
      //			} else {
      return null;
      //			}
    },

    checkDuplicateExhibitor: function (filt, companyName) {
      //			var csvCNameTest = companyName.replace(/[,.()-;!]/g, "").replace(" & ", " and ").toUpperCase();
      var exhbID = null;
      //			var cols = new Array(
      //				new nlobjSearchColumn("companyname", null, null),
      //				new nlobjSearchColumn("entityid", null, null)
      //			);
      //			var exhbSearch = null;
      //			try {
      //				exhbSearch = nlapiCreateSearch("customer", filt, cols);
      //			} catch (err) {
      //				if (err.name == "SSS_INVALID_SRCH_FILTER_EXPR") {
      //					NG.log.logError(err, "Error encountered building exhibitor search", JSON.stringify(filt));
      //					throw err;
      //				} else {
      //					NG.log.logError(err, "Error encountered building exhibitor search", JSON.stringify(filt));
      //				}
      //			}
      //			var search = null;
      //			try {
      //				search = exhbSearch!= null ? exhbSearch.runSearch() : null;
      //			} catch (err) {
      //				NG.log.logError(err, "Error encountered during duplicate exhibitor search");
      //			}
      //			if (search != null) {
      //				var results = NG.tools.getSearchResults(search, false);
      //				if (results != null && results.length > 0) {
      //					for (var i = 0; i < results.length; i++) {
      //						var sCNameTest = !NG.tools.isEmpty(results[i].getValue("companyname")) ? results[i].getValue("companyname").replace(/[,.()-;!]/g, "").replace(" & ", " and ").replace("&amp;", "and").toUpperCase() : "";
      //						var sCEntTest = !NG.tools.isEmpty(results[i].getValue("entityid")) ? results[i].getValue("entityid").replace(/[,.()-;!]/g, "").replace(" & ", " and ").replace("&amp;", "and").toUpperCase() : "";
      //						var m1 = !NG.tools.isEmpty(sCNameTest) ? csvCNameTest.search(sCNameTest) >= 0 : false;
      //						var m2 = !NG.tools.isEmpty(sCNameTest) ? sCNameTest.search(csvCNameTest) >= 0 : false;
      //						var m3 = !NG.tools.isEmpty(sCEntTest) ? csvCNameTest.search(sCEntTest) >= 0 : false;
      //						var m4 = !NG.tools.isEmpty(sCEntTest) ? sCEntTest.search(csvCNameTest) >= 0 : false;
      //						var csvWordCount = csvCNameTest.split(" ").length;
      //						var cNameWordCount = sCNameTest.split(" ").length;
      //						var cEntWordCount = sCEntTest.split(" ").length;
      //						var countMatch = csvWordCount > 1 ? (cNameWordCount > 1 || cEntWordCount > 1) : (csvWordCount == 1 ? (cNameWordCount == 1 || cEntWordCount == 1) : true);
      //						if ((m1 || m2 || m3 || m4) && countMatch) {
      //							exhbID = results[i].getId();
      //							log.audit({ title : "Dupe exhibitor search match" , details : "CSV Name: {0} -- Search Name: {1} ({2})".NG_Format(companyName, results[i].getValue("companyname"), results[i].getValue("entityid")) });
      //							break;
      //						}
      //					}
      //				}
      //			}

      return exhbID;
    },

    CreditCardPayMethodIDs: function () {
      var cards = new Array();
      if (_cs_settings.MastercardPayMethodId != null)
        cards.push(_cs_settings.MastercardPayMethodId);
      if (_cs_settings.VisaPayMethodId != null)
        cards.push(_cs_settings.VisaPayMethodId);
      if (_cs_settings.AmexPayMethodId != null)
        cards.push(_cs_settings.AmexPayMethodId);
      if (_cs_settings.DiscoverPayMethodId != null)
        cards.push(_cs_settings.DiscoverPayMethodId);
      return cards;
    },

    CreditCardPayMethodObj: function () {
      var cards = {};
      if (_cs_settings.MastercardPayMethodId != null)
        cards.m = {
          name: "MasterCard",
          id: _cs_settings.MastercardPayMethodId,
        };
      if (_cs_settings.VisaPayMethodId != null)
        cards.v = { name: "Visa", id: _cs_settings.VisaPayMethodId };
      if (_cs_settings.AmexPayMethodId != null)
        cards.a = { name: "Amex", id: _cs_settings.AmexPayMethodId };
      if (_cs_settings.DiscoverPayMethodId != null)
        cards.d = { name: "Discover", id: _cs_settings.DiscoverPayMethodId };
      return cards;
    },

    /**
     * checks a given string for a valid credit card
     * @returns:
     *		-1	invalid
     *		1	mastercard
     *		2	visa
     *		3	amex
     *		4	discover
     */
    checkCC: function (val, alt) {
      String.prototype.startsWith = function (str) {
        return this.match("^" + str) == str;
      };

      Array.prototype.has = function (v, i) {
        for (var j = 0; j < this.length; j++) {
          if (this[j] == v) return !i ? true : j;
        }
        return false;
      };

      // get rid of all non-numbers (space etc)
      val = val.replace(/[^0-9]/g, "");

      // now get digits
      var d = new Array();
      var a = 0;
      var len = 0;
      var cval = val;
      while (cval != 0) {
        d[a] = cval % 10;
        cval -= d[a];
        cval /= 10;
        a++;
        len++;
      }

      if (len < 13) {
        return -1;
      }

      var cType = -1;

      // mastercard
      if (val.startsWith("5")) {
        if (len != 16) {
          return -1;
        }
        cType = !alt ? _cs_settings.MastercardPayMethodId || -1 : 1;
      }
      // visa
      else if (val.startsWith("4")) {
        if (len != 16 && len != 13) {
          return -1;
        }
        cType = !alt ? _cs_settings.VisaPayMethodId || -1 : 2;
      }
      // amex
      else if (val.startsWith("34") || val.startsWith("37")) {
        if (len != 15) {
          return -1;
        }
        cType = !alt ? _cs_settings.AmexPayMethodId || -1 : 3;
      }
      // discover
      else if (val.startsWith("6011")) {
        if (len != 15 && len != 16) {
          return -1;
        }
        cType = !alt ? _cs_settings.DiscoverPayMethodId || -1 : 4;
      } else {
        return -1; // invalid cc company
      }

      // lets do some calculation
      var sum = 0;
      var i;
      for (i = 1; i < len; i += 2) {
        var s = d[i] * 2;
        sum += s % 10;
        sum += (s - (s % 10)) / 10;
      }

      for (i = 0; i < len; i += 2) {
        sum += d[i];
      }

      // must be %10
      if (sum % 10 != 0) {
        return -1;
      }

      return cType;
    },

    selectCardType: function (cardNumber, isClient, numberField, methodField) {
      var ccType = null;
      switch (this.checkCC(cardNumber, true)) {
        case 1: // Mastercard
          ccType = _cs_settings.MastercardPayMethodId;
          break;
        case 2: // VISA
          ccType = _cs_settings.VisaPayMethodId;
          break;
        case 3: // AmEx
          ccType = _cs_settings.AmexPayMethodId;
          break;
        case 4: // Discover
          ccType = _cs_settings.DiscoverPayMethodId;
          break;
        default:
          if (isClient) {
            console.error(
              "Invalid Credit Card Number - Not setting payment type: " +
                cardNumber
            );
          } else {
            return null;
          }
          break;
      }
      if (ccType != null) {
        if (isClient) {
          require(["N/currentRecord"], function (currentRecord) {
            currentRecord.get().setValue({
              fieldId: methodField,
              value: ccType,
              ignoreFieldChange: false,
            });
          });
        }
      }
      if (!isClient) {
        return ccType;
      }
    },

    getShowDates: function (showTableId, summary, addTimes, dateTypes) {
      var showDateTypeId = _cs_settings.DefaultShowDateType;

      if (showDateTypeId != null) {
        var dateFilt = new Array([
          "custrecord_show_number_date",
          "anyof",
          [showTableId],
        ]);
        if (dateTypes != null && dateTypes.length > 0) {
          dateFilt.push("and", ["custrecord_date_type", "anyof", dateTypes]);
        } else {
          dateFilt.push("and", [
            "custrecord_date_type",
            "anyof",
            [showDateTypeId],
          ]);
        }

        var dateCols = new Array(
          search.createColumn({ name: "custrecord_date", summary: summary })
        );

        if (summary == null) {
          dateCols[0].setSort();
        }
        if (addTimes) {
          if (summary != null) {
            dateCols.push(
              search.createColumn({
                name: "custrecord_start_time",
                summary: "group",
              }),
              search.createColumn({
                name: "custrecord_end_time",
                summary: "group",
              })
            );
          } else {
            dateCols.push(
              search.createColumn({ name: "custrecord_start_time" }),
              search.createColumn({ name: "custrecord_end_time" })
            );
          }
        }
        var dateSearch = null;
        try {
          dateSearch = NG.tools.getSearchResults(
            "customrecord_show_date",
            dateFilt,
            dateCols,
            null,
            25,
            false,
            false
          );
        } catch (err) {
          NG.log.logError(err, "Error encountered searching for show dates");
          dateSearch = new Array();
        }
        if (dateSearch != null && dateSearch.length > 0) {
          var response = null;
          if (summary != null) {
            if (addTimes) {
              response = {
                date: dateSearch[0].getValue(dateCols[0]),
                start: dateSearch[0].getValue(dateCols[1]),
                end: dateSearch[0].getValue(dateCols[2]),
              };
            } else {
              response = dateSearch[0].getValue(dateCols[0]);
            }
          } else {
            var dateList = new Array();
            response = new Array();
            for (var i = 0; i < dateSearch.length; i++) {
              var date = dateSearch[i].getValue(dateCols[0]);
              if (!NG.tools.isInArray(date, dateList)) {
                var data = {
                  date: date,
                };
                if (addTimes) {
                  data.start = dateSearch[i].getValue(dateCols[1]);
                  data.end = dateSearch[i].getValue(dateCols[2]);
                }
                response.push(data);
                dateList.push(date);
              }
            }
          }
          return response;
        }
      } else {
        return null;
      }
    },

    getStartDate: function (showTable) {
      var startDateT = this.getShowDates(showTable, "min", false, null);
      var startDateD = !NG.tools.isEmpty(startDateT)
        ? format.parse({ value: startDateT, type: format.Type.DATE })
        : null;
      var startDate = !NG.tools.isEmpty(startDateD)
        ? startDateD.getTime()
        : null;

      return startDate;
    },

    getRateType: function (showTableId, dateTxt) {
      var date = format
        .parse({ value: dateTxt, type: format.Type.DATE })
        .getTime();
      var advanced = false;
      var standard = false;
      var onsite = false;

      var showData = null;
      try {
        showData = search.lookupFields({
          type: "customrecord_show",
          id: showTableId,
          columns: [
            "custrecord_adv_ord_date",
            "custrecord_adv_price_level",
            "custrecord_std_price_level",
            "custrecord_site_price_level",
          ],
        });
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered getting show table price levels"
        );
      }

      if (!NG.tools.isEmpty(showData)) {
        if (!NG.tools.isEmpty(showData["custrecord_adv_ord_date"])) {
          var date = format
            .parse({
              value: showData["custrecord_adv_ord_date"],
              type: format.Type.DATE,
            })
            .getTime();

          if (date <= advDate) {
            advanced = true;
          } else {
            var startDate = this.getStartDate(showTableId);
            if (startDate != null && date >= startDate) {
              if (!NG.tools.isEmpty(showData["custrecord_site_price_level"])) {
                onsite = true;
              } else {
                standard = true;
              }
            } else {
              standard = true;
            }
          }
        } else {
          var startDate = this.getStartDate(showTableId);
          if (startDate != null && date >= startDate) {
            if (!NG.tools.isEmpty(showData["custrecord_site_price_level"]))
              onsite = true;
            else standard = true;
          } else {
            standard = true;
          }
        }
      } else {
        standard = true;
      }

      return {
        a: advanced,
        s: standard,
        o: onsite,
      };
    },

    getExemptedTotal: function (
      showTableId,
      booth,
      entity,
      orderID,
      catExempt
    ) {

      log.debug('Running getExemptedTotal')
      var billableTotal = new Number(0);

      var filt = new Array(
        ["mainline", "is", "F"],
        "and",
        ["cogs", "is", "F"],
        "and",
        ["taxline", "is", "F"],
        "and",
        ["shipping", "is", "F"],
        "and",
        ["custbody_to_be_deleted", "is", "F"],
        "and",
        ["item.type", "noneof", ["Subtotal"]]
      );
      if (_cs_settings.AllowMultiBillingParties && !NG.tools.isEmpty(entity)) {
        filt.push("and", ["entity", "anyof", [entity]]);
      }
      if (!NG.tools.isEmpty(orderID)) {
        filt.push("and", ["internalid", "anyof", [orderID]]);
      } else {
        filt.push(
          "and",
          ["custbody_show_table", "anyof", [showTableId]],
          "and",
          ["custbody_booth", "anyof", [booth]]
        );
      }

      var cols = new Array(
        search.createColumn({ name: "line", sort: "ASC" }),
        search.createColumn({ name: "item" }),
        search.createColumn({ name: "taxamount" }),
        search.createColumn({ name: "amount" }),
        search.createColumn({ name: "custcol_cost_is_estimated" }),
        search.createColumn({ name: "type", join: "item" })
      );

      var etSearch = null;
      try {
        etSearch = NG.tools.getSearchResults(
          "salesorder",
          filt,
          cols,
          null,
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving order values");
      }
      if (etSearch != null) {
        var itemCatData = {};
        if (catExempt) {
          var itemList = new Array();
          for (var i = 0; i < etSearch.length; i++) {
            itemList.push(etSearch[i].getValue("item"));
          }
          itemCatData = this.getItemCatData(itemList);
        }
        var _IN_GROUP = false;

        for (var i = 0; i < etSearch.length; i++) {
          var itemId = etSearch[i].getValue({ name: "item" });
          var itemType = etSearch[i].getValue({ name: "type", join: "item" });
          if (!_IN_GROUP && itemType == "Group") {
            _IN_GROUP = true;
          } else if (_IN_GROUP && itemType == "EndGroup") {
            _IN_GROUP = false;
            var tax = new Number(etSearch[i].getValue({ name: "taxamount" }));
            if (isNaN(tax)) {
              tax = new Number(0);
            }
            var amount = new Number(etSearch[i].getValue({ name: "amount" }));
            billableTotal = NG.M.roundToHundredths(
              billableTotal + tax + amount
            );
          } else if (!_IN_GROUP) {
            if (
              etSearch[i].getValue({ name: "custcol_cost_is_estimated" }) != "T"
            ) {
              if (
                !catExempt ||
                (catExempt &&
                  !NG.tools.isInArray(
                    itemCatData[itemId],
                    _cs_settings.AutoChargeCategoryExclusions
                  ))
              ) {
                var tax = new Number(
                  etSearch[i].getValue({ name: "taxamount" })
                );
                if (isNaN(tax)) {
                  tax = new Number(0);
                }
                var amount = new Number(
                  etSearch[i].getValue({ name: "amount" })
                );
                billableTotal = NG.M.roundToHundredths(
                  billableTotal + tax + amount
                );
              }
            }
          }
        }
      } else {
        log.audit({ title: "empty total search" });
      }

      return billableTotal;
    },

    getPaymentTotals: function (showTableId, booth, entity, orderID) {
      var paidTotal = new Number(0);

      var filt = new Array();
      if (!NG.tools.isEmpty(orderID)) {
        filt.push(["custbody_booth_order", "anyof", [orderID]]);
      } else if (!NG.tools.isEmpty(showTableId) && !NG.tools.isEmpty(booth)) {
        filt.push(["custbody_show_table", "anyof", [showTableId]], "and", [
          "custbody_booth",
          "is",
          booth,
        ]);
      }
      if (_cs_settings.AllowMultiBillingParties && !NG.tools.isEmpty(entity)) {
        if (filt.length > 0) {
          filt.push("and");
        }
        filt.push(["entity", "anyof", [entity]]);
      }
      var ptSearch = null;
      try {
        ptSearch = NG.tools.getSearchResults(
          "transaction",
          filt,
          null,
          "customsearch_payment_refund_search",
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving order values");
      }
      if (ptSearch != null) {
        for (var i = 0; i < ptSearch.length; i++) {
          paidTotal = NG.M.roundToHundredths(
            paidTotal + new Number(ptSearch[i].getValue({ name: "amount" }))
          );
        }
      }

      return paidTotal;
    },

    getItemCatData: function (itemList) {
      var itemData = {};
      var filt = new Array(["internalid", "anyof", itemList]);
      var cols = new Array(
        search.createColumn({ name: "custitem_item_category_2" }),
        search.createColumn({ name: "internalid" })
      );
      var cdSearch = null;
      try {
        cdSearch = NG.tools.getSearchResults(
          "salesorder",
          filt,
          cols,
          null,
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered getting item category data");
      }
      if (cdSearch != null) {
        for (var i = 0; i < cdSearch.length; i++) {
          itemData[cdSearch[i].id] = cdSearch[i].getValue({
            name: "custitem_item_category_2",
          });
        }
      }

      return itemData;
    },

    calcWithoutExclCats: function (orderId, balance) {
      log.debug('Running calcWithoutExclCats')
      
      var order = record.load({ type: "salesorder", id: orderId });
      var taxRate =
        new Number(order.getValue({ fieldId: "taxrate" }).replace("%", "")) /
        100;
      var totalPaid = new Number(
        order.getValue({ fieldId: "custbody_total_paid" })
      );
      var taxAmount = new Number(0);
      var tTotal = new Number(0);
      var ntTotal = new Number(0);
      var items = new Array();
      var lines = order.getLineCount({ sublistId: "item" });
      for (var l = 1; l <= lines; l++) {
        items.push(
          order.getSublistValue({ sublistId: "item", fieldId: "item", line: l })
        );
      }
      var itemCats = this.getItemCatData(items);
      for (var l = 1; l <= lines; l++) {
        var taxable = order.getSublistValue({
          sublistId: "item",
          fieldId: "istaxable",
          line: l,
        });
        var category =
          itemCats[
            order.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: l,
            })
          ];
        var amount = new Number(
          order.getSublistValue({
            sublistId: "item",
            fieldId: "amount",
            line: l,
          })
        );
        if (
          NG.tools.isEmpty(category) ||
          !NG.tools.isInArray(
            category,
            _cs_settings.AutoChargeCategoryExclusions
          )
        ) {
          if (taxable == "T") {
            tTotal += amount;
          } else {
            ntTotal += amount;
          }
        }
      }

      tTotal = NG.M.roundToHundredths(tTotal);
      ntTotal = NG.M.roundToHundredths(ntTotal);

      if (tTotal > 0) {
        taxAmount += NG.M.roundToHundredths(taxRate * tTotal);
      }

      var FinalValue = NG.M.roundToHundredths(
        tTotal + ntTotal + taxAmount - totalPaid
      );

      return FinalValue > 0 ? FinalValue : 0;
    },

    hideCSJobField: function (context) {
      require(["N/ui/serverWidget"], function (widget) {
        var form = context.form;
        if (!_cs_settings.UseCustomJob) {
          var jobFieldA = form.getField({ id: "custbody_cseg_ng_cs_job" });
          if (jobFieldA != null) {
            jobFieldA.updateDisplayType({
              displayType: widget.FieldDisplayType.HIDDEN,
            });
            jobFieldA.isMandatory = false;
          }
          var jobFieldB = form.getField({ id: "custentity_cseg_ng_cs_job" });
          if (jobFieldB != null) {
            jobFieldB.updateDisplayType({
              displayType: widget.FieldDisplayType.HIDDEN,
            });
            jobFieldB.isMandatory = false;
          }
          var jobFieldC = form.getField({ id: "custrecord_show_job" });
          if (jobFieldC != null) {
            jobFieldC.updateDisplayType({
              displayType: widget.FieldDisplayType.HIDDEN,
            });
            jobFieldC.isMandatory = false;
          }
          var jobFieldD = form.getField({ id: "class" });
          if (jobFieldD != null) {
            jobFieldD.label = "Job";
          }
        } else {
          var jobFieldA = form.getField({ id: "custbody_cseg_ng_cs_job" });
          if (jobFieldA != null) {
            jobFieldA.updateDisplayType({
              displayType: widget.FieldDisplayType.NORMAL,
            });
          }
          var jobFieldB = form.getField({ id: "custentity_cseg_ng_cs_job" });
          if (jobFieldB != null) {
            jobFieldB.updateDisplayType({
              displayType: widget.FieldDisplayType.NORMAL,
            });
          }
          var jobFieldC = form.getField({ id: "custrecord_show_job" });
          if (jobFieldC != null) {
            jobFieldC.updateDisplayType({
              displayType: widget.FieldDisplayType.NORMAL,
            });
          }
          var jobFieldD = form.getField({ id: "class" });
          if (jobFieldD != null) {
            jobFieldD.label = "Job";
          }
        }
      });
    },

    syncToAlgolia: function (batchData) {
      var readBase = _cs_settings.AlgoliaReadURL.NG_Format(
        _cs_settings.AlgoliaApplicationID,
        _cs_settings.AlgoliaIndex
      );
      var writeBase = _cs_settings.AlgoliaWriteURL.NG_Format(
        _cs_settings.AlgoliaApplicationID,
        _cs_settings.AlgoliaIndex
      );
      var headers = {
        "X-Algolia-API-Key": _cs_settings.AlgoliaAPIKey,
        "X-Algolia-Application-Id": _cs_settings.AlgoliaApplicationID,
        "Content-Type": "application/json; charset=UTF-8",
      };

      var batchURL = "{0}batch".NG_Format(writeBase);
      var data = { requests: batchData };
      var request = null;
      try {
        request = https.post({
          url: batchURL,
          body: JSON.stringify(data),
          headers: headers,
        });
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered submitting item batch data to Algolia"
        );
      }

      if (!NG.tools.isEmpty(request)) {
        log.audit({
          title: "Algolia Response",
          details: "{0} : {1}".NG_Format(request.code, request.body),
        });
      }
    },

    deleteFromAlgolia: function (objectId) {
      var readBase = _cs_settings.AlgoliaReadURL.NG_Format(
        _cs_settings.AlgoliaApplicationID,
        _cs_settings.AlgoliaIndex
      );
      var writeBase = _cs_settings.AlgoliaWriteURL.NG_Format(
        _cs_settings.AlgoliaApplicationID,
        _cs_settings.AlgoliaIndex
      );
      var headers = {
        "X-Algolia-API-Key": _cs_settings.AlgoliaAPIKey,
        "X-Algolia-Application-Id": _cs_settings.AlgoliaApplicationID,
        "Content-Type": "application/json; charset=UTF-8",
      };

      var deleteURL = "{0}{1}".NG_Format(writeBase, objectId);
      var request = null;
      try {
        request = https.delete({ url: batchURL, headers: headers });
      } catch (err) {
        NG.log.logError(err, "Error encountered deleting object Algolia");
      }

      if (!NG.tools.isEmpty(request)) {
        log.audit({
          title: "Algolia Response",
          details: "{0} : {1}".NG_Format(request.code, request.body),
        });
      }
    },

    findAlgoliaObjectId: function (rec) {
      var readBase = _cs_settings.AlgoliaReadURL.NG_Format(
        _cs_settings.AlgoliaApplicationID,
        _cs_settings.AlgoliaIndex
      );
      var writeBase = _cs_settings.AlgoliaWriteURL.NG_Format(
        _cs_settings.AlgoliaApplicationID,
        _cs_settings.AlgoliaIndex
      );
      var headers = {
        "X-Algolia-API-Key": _cs_settings.AlgoliaAPIKey,
        "X-Algolia-Application-Id": _cs_settings.AlgoliaApplicationID,
        "Content-Type": "application/json; charset=UTF-8",
      };

      var itemInternalId = rec.id;
      var childRegex = new RegExp(
        "^{0}-\\d{1,3}".NG_Format(itemInternalId),
        "i"
      );
      var catLineCount = rec.getLineCount({ sublistId: "sitecategory" });
      var matchHash = {};
      for (var l = 1; l <= catLineCount; l++) {
        var catId = rec.getLineValue({
          sublistId: "sitecategory",
          fieldId: "category",
          line: l,
        });
        var childId = "CHILD_{0}".NG_Format(catId);
        matchHash[childId] = new Array();
        var data = {
          params: "query={0} {1}".NG_Format(itemInternalId, catId),
        };
        var searchURL = "{0}query".NG_Format(readBase);
        var request = null;
        try {
          request = https.post({
            url: searchURL,
            body: JSON.stringify(data),
            headers: headers,
          });
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered searching for item in Algolia"
          );
        }

        if (!NG.tools.isEmpty(request) && request.code == "200") {
          var results = JSON.parse(request.body);
          var objectId = "";
          if (results.hits.length > 0) {
            for (var h = 0; h < results.hits.length; h++) {
              var hit = results.hits[h];
              if (hit.internalid == itemInternalId && hit.category == catId) {
                objectId = hit.objectID;
              } else if (
                childRegex.test(hit.internalid) &&
                hit.category == catId
              ) {
                matchHash[childId].push({
                  childid: "{0}".NG_Format(hit.internalid),
                  objectid: hit.objectID,
                });
              }
            }
          }

          matchHash[catId] = objectId;
        }
      }

      return matchHash;
    },

    buildAlgoliaBatchObject: function (rec, objectHash) {
      //				require(['N/ui/widget'], function(widget) {
      var catLineCount = rec.getLineCount({ sublistId: "sitecategory" });
      var batchList = [];
      var childList = [];

      var priceLevelHash = {};
      var plSearch = NG.tools.getSearchResults(
        "pricelevel",
        [["isinactive", "is", "F"]],
        [new nlobjSearchColumn({ name: "name" })],
        null,
        1000,
        false,
        false
      );
      if (!NG.tools.isEmpty(plSearch)) {
        for (var pl = 0; pl < plSearch.length; pl++) {
          priceLevelHash[plSearch[pl].id] = plSearch[pl].getValue({
            name: "name",
          });
        }
      }

      var itemOptsSel = rec.getValue({ fieldId: "itemoptions" });
      var itemOptsDetail = {
        fields: [
          {
            internalid: "custcol_custom_carpet_size",
            label: "Custom Size",
            type: "text",
          },
        ],
      };

      for (var ops = 0; ops < itemOptsSel.length; ops++) {
        var fieldKey = itemOptsSel[ops].toLowerCase();
        var optObj = NewGen.sclib.matrixOptionMap[fieldKey];
        if (!NG.tools.isEmpty(optObj)) {
          var optObjB = JSON.parse(JSON.stringify(optObj));
          if (optObjB.type == "select") {
            var fieldId = optObjB.sourcefrom;
            var selVals = rec.getValue({ fieldId: fieldId });
            var fieldObj = rec.getField({ id: fieldId });
            var fieldSelOpts = fieldObj.getSelectOptions();
            var valList = new Array();
            for (var op = 0; op < fieldSelOpts.length; op++) {
              if (NG.tools.isInArray(fieldSelOpts[op].id, selVals)) {
                valList.push({
                  internalid: fieldSelOpts[op].id,
                  label: fieldSelOpts[op].text,
                });
              }
            }
            if (valList.length > 0) {
              optObjB.values = valList;
            }
          }
          itemOptsDetail.fields.push(optObjB);
        }
      }

      var isMatrix = !NG.tools.isEmpty(
        rec.getValue({ fieldId: "matrixitemnametemplate" })
      );
      if (isMatrix) {
        itemOptsDetail.matrixtype = "PARENT";
        var childFilt = new Array(
          ["parent", "anyof", [rec.id]],
          "and",
          ["isinactive", "is", "F"],
          "and",
          ["matrixchild", "is", "T"]
        );
        var childCols = new Array(
          search.createColumn({ name: "custitem27" }),
          search.createColumn({ name: "custitem28" }),
          search.createColumn({ name: "custitem_orientation" }),
          search.createColumn({ name: "custitem42" }),
          search.createColumn({ name: "custitem43" }),
          search.createColumn({ name: "custitem_child_item_image" }),
          search.createColumn({ name: "onlinecustomerprice" }),
          search.createColumn({ name: "baseprice" }),
          search.createColumn({ name: "otherprices" })
        );
        var childSearchResults = null;
        try {
          childSearchResults = NG.tools.getSearchResults(
            rec.type,
            childFilt,
            childCols,
            null,
            1000,
            false,
            false
          );
        } catch (err) {
          NG.log.logError(err, "Error encountered getting child matrix items");
        }

        if (!NG.tools.isEmpty(childSearchResults)) {
          var childImageList = new Array();
          var childImageHash = {};
          for (var c = 0; c < childSearchResults.length; c++) {
            if (
              !NG.tools.isEmpty(
                childSearchResults[c].getValue({
                  name: "custitem_child_item_image",
                })
              )
            ) {
              childImageList.push(
                childSearchResults[c].getValue({
                  name: "custitem_child_item_image",
                })
              );
            }
          }
          if (childImageList.length > 0) {
            try {
              var fileFilt = new Array(
                ["availablewithoutlogin", "is", "T"],
                "and",
                ["internalid", "anyof", childImageList]
              );
              var fileCols = new Array(search.createColumn({ name: "url" }));
              var fileSearch = NG.tools.getSearchResults(
                "file",
                fileFilt,
                fileCols,
                null,
                1000,
                false,
                false
              );
              if (!NG.tools.isEmpty(fileSearch)) {
                for (var f = 0; f < fileSearch.length; f++) {
                  childImageHash[fileSearch[f].id] = fileSearch[f].getValue({
                    name: "url",
                  });
                }
              }
            } catch (err) {
              NG.log.logError(
                err,
                "Error encountered searching for item image URLs"
              );
            }
          }
          for (var c = 0; c < childSearchResults.length; c++) {
            var csr = childSearchResults[c];
            var child = {
              internalid: csr.id,
              onlinecustomerprice_detail: {
                onlinecustomerprice: new Number(
                  csr.getValue({ name: "onlinecustomerprice" })
                ).toFixed(2),
                onlinecustomerprice_formatted: "${0}".NG_Format(
                  new Number(
                    csr.getValue({ name: "onlinecustomerprice" })
                  ).formatMoney()
                ),
                masterPriceLevels: new Array(),
              },
              custitem_child_item_image:
                childImageHash[
                  csr.getValue({ name: "custitem_child_item_image" })
                ] || "",
            };
            if (!NG.tools.isEmpty(csr.getValue({ name: "custitem27" }))) {
              child.custitem27 = csr.getText({ name: "custitem27" });
            }
            if (!NG.tools.isEmpty(csr.getValue({ name: "custitem28" }))) {
              child.custitem28 = csr.getText({ name: "custitem28" });
            }
            if (
              !NG.tools.isEmpty(csr.getValue({ name: "custitem_orientation" }))
            ) {
              child.custitem_orientation = csr.getText({
                name: "custitem_orientation",
              });
            }
            if (!NG.tools.isEmpty(csr.getValue({ name: "custitem42" }))) {
              child.custitem42 = csr.getText({ name: "custitem42" });
            }
            if (!NG.tools.isEmpty(csr.getValue({ name: "custitem43" }))) {
              child.custitem43 = csr.getText({ name: "custitem43" });
            }

            for (plIndex in priceLevelHash) {
              if (plIndex == "1") {
                if (!NG.tools.isEmpty(csr.getValue({ name: "baseprice" }))) {
                  child.onlinecustomerprice_detail.masterPriceLevels.push({
                    id: "1",
                    up: new Number(
                      csr.getValue({ name: "baseprice" })
                    ).formatMoney(),
                  });
                }
              } else {
                if (
                  !NG.tools.isEmpty(
                    csr.getValue({ name: "price{0}".NG_Format(plIndex) })
                  )
                ) {
                  child.onlinecustomerprice_detail.masterPriceLevels.push({
                    id: plIndex,
                    up: new Number(
                      csr.getValue({ name: "price{0}".NG_Format(plIndex) })
                    ).formatMoney(),
                  });
                }
              }
            }

            childList.push(child);
          }
        }
      }

      var onlinePriceDataInit = search.lookupFields({
        type: "item",
        id: rec.id,
        columns: ["onlinecustomerprice"],
      });
      var onlinePriceData = {
        onlinecustomerprice: new Number(
          onlinePriceDataInit.onlinecustomerprice || "0"
        ).toFixed(2),
        onlinecustomerprice_formatted: "${0}".NG_Format(
          new Number(
            onlinePriceDataInit.onlinecustomerprice || "0"
          ).formatMoney()
        ),
      };

      var masterPriceLevels = new Array();
      var pl1 = "";
      var pl1f = "";
      var ipFilt = new Array(["internalid", "anyof", [rec.id]]);
      var ipCols = new Array(
        search.createColumn({ name: "baseprice" }),
        search.createColumn({ name: "otherprices" })
      );
      var itemPriceSearch = NG.tools.getSearchResults(
        "item",
        ipFilt,
        ipCols,
        null,
        1000,
        false,
        false
      );
      if (!NG.tools.isEmpty(itemPriceSearch)) {
        for (var p = 0; p < itemPriceSearch.length; p++) {
          for (plIndex in priceLevelHash) {
            if (plIndex == "1") {
              if (
                !NG.tools.isEmpty(
                  itemPriceSearch[p].getValue({ name: "baseprice" })
                )
              ) {
                masterPriceLevels.push({
                  id: "1",
                  up: new Number(
                    itemPriceSearch[p].getValue({ name: "baseprice" })
                  ).formatMoney(),
                });
                pl1 = new Number(
                  itemPriceSearch[p].getValue({ name: "baseprice" })
                ).toFixed(2);
                pl1f = new Number(
                  itemPriceSearch[p].getValue({ name: "baseprice" })
                ).formatMoney();
              }
            } else {
              if (
                !NG.tools.isEmpty(
                  itemPriceSearch[p].getValue({
                    name: "price{0}".NG_Format(plIndex),
                  })
                )
              ) {
                masterPriceLevels.push({
                  id: plIndex,
                  up: new Number(
                    itemPriceSearch[p].getValue({
                      name: "price{0}".NG_Format(plIndex),
                    })
                  ).formatMoney(),
                });
              }
            }
          }
        }
      }

      var fileIdList = new Array();
      var fileURLHash = {};

      var fileFieldList = [
        "storedisplayimage",
        "custitem_child_item_image",
        "storedisplaythumbnail",
        "custitem_web_image1",
        "custitem_web_image2",
        "custitem_web_image3",
        "custitem_web_image4",
        "custitem_web_image5",
        "custitem_web_image6",
      ];
      for (var ff = 0; ff < fileFieldList.length; ff++) {
        if (!NG.tools.isEmpty(rec.getValue({ fieldId: fileFieldList[ff] }))) {
          fileIdList.push(rec.getValue({ fieldId: fileFieldList[ff] }));
        }
      }

      if (fileIdList.length > 0) {
        try {
          var fileFilt = new Array(
            ["availablewithoutlogin", "is", "T"],
            "and",
            ["internalid", "anyof", fileIdList]
          );
          var fileCols = new Array(search.createColumn({ name: "url" }));
          var fileSearch = NG.tools.getSearchResults(
            "file",
            fileFilt,
            fileCols,
            null,
            1000,
            false,
            false
          );
          if (!NG.tools.isEmpty(fileSearch)) {
            for (var f = 0; f < fileSearch.length; f++) {
              fileURLHash[fileSearch[f].id] = fileSearch[f].getValue({
                name: "url",
              });
            }
          }
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered searching for item image URLs"
          );
        }
      }

      for (var l = 1; l <= catLineCount; l++) {
        var catId = rec.getLineItemValue("sitecategory", "category", l);
        var objectId = objectHash[catId] || "";
        var batch = {
          action: !NG.tools.isEmpty(objectId) ? "updateObject" : "addObject",
          body: {
            internalid: rec.id,
            category: catId,
            canonicalurl: _cs_settings.AlgoliaCanonicalURL.NG_Format(rec.id),
            itemid: rec.getValue({ fieldId: "itemid" }),
            itemtype: rec.getValue({ fieldId: "itemtype" }),
            minimumquantity: new Number(
              rec.getValue({ fieldId: "minimumquantity" })
            ),
            maximumquantity: new Number(
              rec.getValue({ fieldId: "maximumquantity" })
            ),
            custitem_min_freight_weight: new Number(
              rec.getValue({ fieldId: "custitem_min_freight_weight" })
            ),
            custitem_req_addtnl_items: rec.getValue({
              fieldId: "custitem_req_addtnl_items",
            }),
            stockdescription:
              rec.getValue({ fieldId: "stockdescription" }) || "",
            custitem_specifications:
              rec.getValue({ fieldId: "custitem_specifications" }) || "",
            custitem_supervision_item_attach:
              rec.getValue({ fieldId: "custitem_supervision_item_attach" }) ||
              "",
            storedescription:
              rec.getValue({ fieldId: "storedescription" }) || "",
            outofstockmessage:
              rec.getValue({ fieldId: "outofstockmessage" }) || "",
            displayname: rec.getValue({ fieldId: "displayname" }) || "",
            storedisplayname2:
              rec.getValue({ fieldId: "storedisplayname" }) || "",
            storedisplayimage:
              fileURLHash[rec.getValue({ fieldId: "storedisplayimage" })] || "",
            storedisplaythumbnail:
              fileURLHash[rec.getValue({ fieldId: "storedisplaythumbnail" })] ||
              "",
            custitem_child_item_image:
              fileURLHash[
                rec.getValue({ fieldId: "custitem_child_item_image" })
              ] || "",
            custitem_web_image1:
              fileURLHash[rec.getValue({ fieldId: "custitem_web_image1" })] ||
              "",
            custitem_web_image2:
              fileURLHash[rec.getValue({ fieldId: "custitem_web_image2" })] ||
              "",
            custitem_web_image3:
              fileURLHash[rec.getValue({ fieldId: "custitem_web_image3" })] ||
              "",
            custitem_web_image4:
              fileURLHash[rec.getValue({ fieldId: "custitem_web_image4" })] ||
              "",
            custitem_web_image5:
              fileURLHash[rec.getValue({ fieldId: "custitem_web_image5" })] ||
              "",
            custitem_web_image6:
              fileURLHash[rec.getValue({ fieldId: "custitem_web_image6" })] ||
              "",
            inactive: rec.getValue({ fieldId: "isinactive" }) == "T",
            custitem_is_days:
              rec.getValue({ fieldId: "custitem_is_days" }) == "T",
            custitem_24_hr_service:
              rec.getValue({ fieldId: "custitem_24_hr_service" }) == "T",
            custitem_show_duration:
              rec.getValue({ fieldId: "custitem_show_duration" }) == "T",
            custitem_is_freight:
              rec.getValue({ fieldId: "custitem_is_freight" }) == "T",
            custitem_is_sqft:
              rec.getValue({ fieldId: "custitem_is_sqft" }) == "T",
            custitem_labor_item:
              rec.getValue({ fieldId: "custitem_labor_item" }) == "T",
            isinstock: true,
            ispurchasable: true,
            isbackorderable: true,
            showoutofstockmessage: false,
            saleunit: "Each",
            onlinecustomerprice_detail: onlinePriceData,
            matrixchilditems_detail: childList,
            masterPriceLevels: masterPriceLevels,
            itemoptions_detail: itemOptsDetail,
            pricelevel1: pl1 || "",
            pricelevel1_formatted: !_tools.isEmpty(pl1f)
              ? "${0}".NG_Format(pl1f)
              : "",
          },
        };
        if (!NG.tools.isEmpty(objectId)) {
          batch.body.objectID = objectId;
        }
        if (!isMatrix) {
          batch.body.ismatrixparent = false;
          batch.body.ismatrixchild = false;
        }

        batchList.push(batch);
      }

      return batchList;
      //				});
    },
  };

  var _OpenShowIdCol = 0;
  var _OpenShowNameCol = 2;
  var _CS_TimeZones = new Array(null, "EST", "CST", "MST", "PST");

  return {
    settings: _cs_settings, // CS_Settings
    func: _csFunc,
    trigger: _trigger,
  };
});
