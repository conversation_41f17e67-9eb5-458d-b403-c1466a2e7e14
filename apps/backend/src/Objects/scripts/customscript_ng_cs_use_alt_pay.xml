<suitelet scriptid="customscript_ng_cs_use_alt_pay">
  <defaultfunction>suitelet</defaultfunction>
  <description>Flag wether to use Alt Pay or not.</description>
  <isinactive>F</isinactive>
  <name>Use Alt Pay</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/ng_cs_use_alt_pay.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_use_alt_pay">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <eventtype></eventtype>
      <isdeployed>T</isdeployed>
      <isonline>T</isonline>
      <loglevel>DEBUG</loglevel>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>RELEASED</status>
      <title>Use Alt Pay</title>
    </scriptdeployment>
  </scriptdeployments>
</suitelet>