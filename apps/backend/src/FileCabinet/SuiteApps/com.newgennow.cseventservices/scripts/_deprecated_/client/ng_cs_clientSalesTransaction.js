/**
 * Module Description
 *
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015             <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;

var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;
var _UseTaxRate = _scLib.UseTaxCode;
var _UseCnclPct = _scLib.UseCancellationCharge;
var _NoPaymentPrompt = _scLib.DoNotPromptIfTerms;
var _NoPromptRoles = _<PERSON>L<PERSON>.NoPromptRoles;
var _PreventAdtnl = _scLib.PreventAdditionalOrders;
var _BlockBilledOrderEditing = _scLib.BlockedBilledOrderEditing;
var _AdminUserIDs = _scLib.AuthorizedBilledOrderEditors;
var _UseScriptedPaymentForm = _scLib.UseScriptedPaymentForm;
var _UseCSJobs = _scLib.UseCustomJob;

var evType = null;
var exContext = null;
var gpl = null;
var taxItem = null;
var addingToLine01 = false;
var startingLineCount = 0;
var _CancellationPct = null;
var _NewCancellation = null;
var _Today = nlapiStringToDate(nlapiDateToString(new Date(), "date"));
var _CurrentRecord = null;
var _ApplyCnclCharge = false;
var _CurrentRole = nlapiGetRole();
var _ShowID = null;
var _ShowTableID = null;
var _IsPageInit = false;
var _AdvDate = null;
var _PreviousExhib = null;
var _EntityChanged = false;
var _ExistingOrder = { check: false, id: "" };
var _totalPaid = new Number(0);
var _ShipAddySet = false;
var addingToLine02 = false;
var _DATE_DATA = null;
var _TIME_REG = /^(?:0?\d|1[012]):[0-5]\d( ?(am|pm))$/i;
var _CALC_WORKER_COUNT = false;
var _SHOW_DATA = null;
var _MODAL_ITEM = null;

var _RunGlobalBO = _scLib.GlobalBoothOrderScripting;
var _ActiveFormBO = false;
var _TriggerBO = false;
var _RunGlobalAL = _scLib.GlobalAddItemScripting;
var _ActiveFormAL = false;
var _NonBoothFormAL = false;
var _TriggerAL = false;
var _RunGlobalSM = _scLib.GlobalShowMgtScripting;
var _ActiveFormSM = false;
var _TriggerSM = false;

var _TriggerWEB = false;

var _SHOW_TABLE_FIELDS = [
  "custrecord_adv_ord_date",
  "custrecord_adv_price_level",
  "custrecord_std_price_level",
  "custrecord_site_price_level",
  "custrecord_cs_st_return_inventory_date",
  "custrecord_cancellation_pct",
  "custrecord_tax_rate",
  "custrecord_show_job",
  "custrecord_fin_show",
  "custrecord_facility",
  "custrecord_tax_percent",
  "custrecord_show_venue",
  "custrecord_show_subsidiary",
  "custrecord_show_mgmnt_price_lvl",
  "custrecord_show_type",
  "custrecord_cs_st_send_inventory_date",
  "custrecord_ng_cs_evt_gst_pct",
  "custrecord_ng_cs_evt_pst_pct",
];

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function ng_clientPageInit_00(type) {
  evType = type;
  if (_UseSubsidiaries) {
    _SHOW_TABLE_FIELDS.push("custrecord_show_subsidiary");
  }
  if (_UseLocations) {
    _SHOW_TABLE_FIELDS.push("custrecord_show_venue");
  }

  determineTriggers();

  if (_TriggerBO) {
    ng_clientPageInit_01(type);
  }

  if (_TriggerAL) {
    ng_clientPageInit_02(type);
  }

  if (_TriggerSM) {
    ng_clientPageInit_03(type);
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @returns {Boolean} True to continue save, false to abort save
 */
function ng_clientSaveRecord_00() {
  if (_TriggerBO) {
    var goBO = ng_clientSaveRecord_01();

    if (!goBO) {
      return false;
    }
  }

  if (_TriggerAL) {
    var goAL = ng_clientSaveRecord_02();

    if (!goAL) {
      return false;
    }
  }

  if (_TriggerSM) {
    var goSM = ng_clientSaveRecord_03();

    if (!goSM) {
      return false;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function ng_clientValidateField_00(type, name, linenum) {
  if (_TriggerBO) {
    var goBO = ng_clientValidateField_01(type, name, linenum);

    if (!goBO) {
      return false;
    }
  }

  if (_TriggerAL) {
    var goAL = ng_clientValidateField_02(type, name, linenum);

    if (!goAL) {
      return false;
    }
  }

  if (_TriggerSM) {
    var goSM = ng_clientValidateField_03(type, name, linenum);

    if (!goSM) {
      return false;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function ng_clientFieldChanged_00(type, name, linenum) {
  if (_TriggerBO) {
    ng_clientFieldChanged_01(type, name, linenum);
  }

  if (_TriggerAL) {
    ng_clientFieldChanged_02(type, name, linenum);
  }

  if (_TriggerSM) {
    ng_clientFieldChanged_03(type, name, linenum);
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @returns {Void}
 */
function ng_clientPostSourcing_00(type, name) {
  if (_TriggerBO) {
    ng_clientPostSourcing_01(type, name);
  }

  if (_TriggerAL) {
    ng_clientPostSourcing_02(type, name);
  }

  if (_TriggerSM) {
    ng_clientPostSourcing_03(type, name);
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientLineInit_00(type) {
  if (_TriggerBO) {
    ng_clientLineInit_01(type);
  }

  if (_TriggerAL) {
    ng_clientLineInit_02(type);
  }

  if (_TriggerSM) {
    ng_clientLineInit_03(type);
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to save line item, false to abort save
 */
function ng_clientValidateLine_00(type) {
  if (_TriggerBO) {
    var goBO = ng_clientValidateLine_01(type);

    if (!goBO) {
      return false;
    }
  }

  if (_TriggerAL) {
    var goAL = ng_clientValidateLine_02(type);

    if (!goAL) {
      return false;
    }
  }

  if (_TriggerSM) {
    var goSM = ng_clientValidateLine_03(type);

    if (!goSM) {
      return false;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientRecalc_00(type) {
  if (_TriggerBO) {
    ng_clientRecalc_01(type);
  }

  if (_TriggerAL) {
    ng_clientRecalc_02(type);
  }

  if (_TriggerSM) {
    ng_clientRecalc_03(type);
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item insert, false to abort insert
 */
function ng_clientValidateInsert_00(type) {
  if (_TriggerBO) {
    var goBO = ng_clientValidateInsert_01(type);

    if (!goBO) {
      return false;
    }
  }

  if (_TriggerAL) {
    var goAL = ng_clientValidateInsert_02(type);

    if (!goAL) {
      return false;
    }
  }

  if (_TriggerSM) {
    var goSM = ng_clientValidateInsert_03(type);

    if (!goSM) {
      return false;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item delete, false to abort delete
 */
function ng_clientValidateDelete_00(type) {
  if (_TriggerBO) {
    var goBO = ng_clientValidateDelete_01(type);

    if (!goBO) {
      return false;
    }
  }

  if (_TriggerAL) {
    var goAL = ng_clientValidateDelete_02(type);

    if (!goAL) {
      return false;
    }
  }

  if (_TriggerSM) {
    var goSM = ng_clientValidateDelete_03(type);

    if (!goSM) {
      return false;
    }
  }

  return true;
}

function determineTriggers() {
  exContext = nlapiGetContext().getExecutionContext();

  if (exContext == "userinterface") {
    if (!_RunGlobalBO) {
      _ActiveFormBO = _tools.isInArray(
        nlapiGetFieldValue("customform"),
        _scLib.BoothOrderFormIdListing
      );
    }
    if (_RunGlobalBO || _ActiveFormBO) {
      _TriggerBO = true;
      console.log("Booth Order triggers enabled");
    } else {
      console.log("Booth Order triggers NOT enabled");
    }

    if (!_RunGlobalAL) {
      _ActiveFormAL = _tools.isInArray(
        nlapiGetFieldValue("customform"),
        _scLib.AddItemFormIdListing
      );
      _NonBoothFormAL =
        _tools.isInArray(
          nlapiGetFieldValue("customform"),
          _scLib.AddItemFormIdListing
        ) &&
        !_tools.isInArray(
          nlapiGetFieldValue("customform"),
          _scLib.BoothOrderFormIdListing
        );
    }
    if (_RunGlobalAL || _ActiveFormAL) {
      _TriggerAL = true;
      console.log("Add Item triggers enabled");
    } else {
      console.log("Add Item triggers NOT enabled");
    }

    if (!_RunGlobalSM) {
      _ActiveFormSM = _tools.isInArray(
        nlapiGetFieldValue("customform"),
        _scLib.ShowMgtFormIdListing
      );
    }
    if (_RunGlobalSM || _ActiveFormSM) {
      _TriggerSM = true;
      console.log("Show Management triggers enabled");
    } else {
      console.log("Show Management triggers NOT enabled");
    }
  } else if (exContext == "webstore") {
    _TriggerWEB = true;
  }
}

function getShowData() {
  console.log("Getting show data");
  var showData = null;
  _ShowTableID = _ShowTableID || nlapiGetFieldValue("custbody_show_table");
  if (!_tools.isEmpty(_ShowTableID)) {
    console.log("Retrieving show table data");
    try {
      showData = nlapiLookupField(
        "customrecord_show",
        _ShowTableID,
        _SHOW_TABLE_FIELDS
      );
    } catch (err) {
      console.log("Error encountered retrieving show table data\n", err);
    }
  }

  _SHOW_DATA = showData;
  console.log("Show table data set to global variable", _SHOW_DATA);
}

//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//
//	BEGIN BOOTH ORDER CLIENT SCRIPTING
//
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function ng_clientPageInit_01(type) {
  var exhbID = nlapiGetFieldValue("entity");
  _ShowID = nlapiGetFieldValue("custbody_show");
  _ShowTableID = nlapiGetFieldValue("custbody_show_table");
  if (evType != "create") {
    _PreviousExhib = exhbID;
  }

  if (!_tools.isEmpty(exhbID) && !_tools.isEmpty(_ShowTableID)) {
    gpl = nlapiGetFieldValue("custbody_price_level") || null;
  }

  if (_tools.isEmpty(_ShowTableID) && _scLib.RetainLastShow) {
    var values = {};
    values["action"] = "get";
    values["user"] = nlapiGetUser();
    var lastShow = _scLib.getLastShow(values);
    _ShowTableID = lastShow;
    if (!_tools.isEmpty(lastShow)) {
      nlapiSetFieldValue("custbody_show_table", _ShowTableID, false);
    }
  }

  getShowData();
  if (!_tools.isEmpty(_SHOW_DATA)) {
    _AdvDate = nlapiStringToDate(_SHOW_DATA["custrecord_adv_ord_date"]);
    setTimeout(function () {
      setPriceLevel(exhbID);
    }, 1000);

    if (_UseCnclPct) {
      var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
      var pct =
        rawPct.search("%") >= 0
          ? new Number(rawPct.replace("%", ""))
          : new Number(rawPct);
      _CancellationPct = pct / 100;
    }
    if (_UseTaxRate && !_scLib.SalesTaxOnItemLine && evType != "edit") {
      setOrderTax();
    }
  }

  if (evType == "edit") {
    startingLineCount = nlapiGetLineItemCount("item");

    if (_UseCnclPct) {
      _CurrentRecord = nlapiLoadRecord(
        nlapiGetRecordType(),
        nlapiGetRecordId()
      );
      if (!_tools.isEmpty(_ShowTableID)) {
        _ApplyCnclCharge = isOnOrAfterMoveInDate(_ShowTableID);
      }
    }

    if (
      _UseScriptedPaymentForm &&
      _tools.isEmpty(_NoPaymentPrompt ? nlapiGetFieldValue("terms") : null) &&
      !_tools.isInArray(_CurrentRole, _NoPromptRoles)
    ) {
      triggerBackgroundExhibData(true);
    }
  }

  if (
    !_tools.isEmpty(_ShowTableID) &&
    !_tools.isEmpty(_SHOW_DATA) &&
    _tools.isEmpty(nlapiGetFieldValue("custbody_cseg_ng_cs_job")) &&
    _UseCSJobs
  ) {
    nlapiSetFieldValue(
      "custbody_cseg_ng_cs_job",
      _SHOW_DATA["custrecord_show_job"],
      false
    );
    nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"], false);
  } else if (!_tools.isEmpty(_ShowTableID) && !_tools.isEmpty(_SHOW_DATA)) {
    nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"], false);
  } else if (_tools.isEmpty(_SHOW_DATA)) {
    console.log("_SHOW_DATA is empty");
  }

  var showField = document.forms["main_form"].elements["inpt_custbody_show"];
  if (showField != null) {
    if (isNaN(showField.length)) {
      showField.focus();
    } else {
      if (showField.length > 0) {
        showField[0].focus();
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @returns {Boolean} True to continue save, false to abort save
 */
function ng_clientSaveRecord_01() {
  var payPrompt = false;

  if (!checkForAdditionalItems()) {
    return false;
  }

  var booth = nlapiGetFieldValue("custbody_booth");

  if (evType == "create" && !_ShipAddySet) {
    try {
      if (_tools.isEmpty(_SHOW_DATA)) {
        getShowData();
      }
      //setFacilityShipAddress(_SHOW_DATA['custrecord_facility'],_SHOW_DATA['custrecord_tax_rate']);
    } catch (err) {
      console.log("Error getting show data", err);
    }
  }

  if (nlapiGetRecordType() != "estimate") {
    if (evType == "create" && _scLib.PreventAdditionalOrders) {
      if (_ExistingOrder.check) {
        var orderID = _ExistingOrder.id;
        var go = window.confirm(
          "You cannot save this order as one already exists for this booth" +
            (_scLib.AllowMultiBillingParties ? " and billing party" : "") +
            '. Click "OK" to redirect to the existing order, click "Cancel" to stay here and edit the order.'
        );
        if (go) {
          _tools.clearFormChanged();
          setTimeout(function () {
            window.location = nlapiResolveURL(
              "RECORD",
              "salesorder",
              orderID,
              "VIEW"
            );
          }, 1000);
          return false;
        } else {
          return false;
        }
      }
    }

    var totalPaid = new Number(0);
    var totalCredit = new Number(0);
    var finalAmount = new Number(0);

    var terms = _NoPaymentPrompt ? nlapiGetFieldValue("terms") : null;
    if (
      _UseScriptedPaymentForm &&
      _tools.isEmpty(terms) &&
      !_tools.isInArray(_CurrentRole, _NoPromptRoles)
    ) {
      totalPaid = _totalPaid;

      if (
        nlapiGetRecordType() == "invoice" &&
        !_tools.isEmpty(nlapiGetRecordId())
      ) {
        var tFilt = new Array(
          ["custbody_show_table", "anyof", [_ShowTableID]],
          "and",
          ["custbody_booth", "anyof", [booth]],
          "and",
          ["mainline", "is", "T"]
        );
        if (_scLib.AllowMultiBillingParties) {
          tFilt.push("and", [
            "entity",
            "anyof",
            [nlapiGetFieldValue("entity")],
          ]);
        }
        var pCols = new Array(
          new nlobjSearchColumn("internalid", null, null),
          new nlobjSearchColumn("trandate", null, null),
          new nlobjSearchColumn("amount", null, null)
        );
        var cmCols = new Array(
          new nlobjSearchColumn("internalid", null, null),
          new nlobjSearchColumn("trandate", null, null),
          new nlobjSearchColumn("total", null, null)
        );
        var pSearch = null;
        var cmSearch = null;
        if (pSearch == null) {
          var pFilt = new Array([
            "appliedtotransaction",
            "anyof",
            [nlapiGetRecordId()],
          ]);

          try {
            pSearch = nlapiSearchRecord("customerpayment", null, pFilt, pCols);
          } catch (err) {
            _log.logError(
              err,
              "Error encountered searching for related payments"
            );
          }
          if (pSearch != null) {
            for (var p = 0; p < pSearch.length; p++) {
              var payAmount = new Number(pSearch[p].getValue("amount"));
              totalPaid += payAmount;
            }
          }
        }

        try {
          cmSearch = nlapiSearchRecord("creditmemo", null, tFilt, cmCols);
        } catch (err) {
          _log.logError(err, "Error encountered searching for related credits");
        }
        if (cmSearch != null) {
          for (var cm = 0; cm < cmSearch.length; cm++) {
            var cmAmount = new Number(cmSearch[cm].getValue("total"));
            totalCredit += cmAmount;
          }
        }
      }

      var orderVal = _scLib.ExemptEstimatedItems
        ? getOrderTotal()
        : new Number(nlapiGetFieldValue("total"));
      finalAmount += _M.roundToHundredths(orderVal + totalCredit);
      if (
        Math.abs(_M.roundToHundredths(totalPaid)) >=
          Math.abs(_M.roundToHundredths(finalAmount)) ||
        (_scLib.NoPromptUnderZero &&
          Math.abs(_M.roundToHundredths(finalAmount)) -
            Math.abs(_M.roundToHundredths(totalPaid)) <=
            0)
      ) {
        payPrompt = false;
      } else {
        payPrompt = true;
      }
      if (
        _scLib.PromptForNewLines &&
        startingLineCount < nlapiGetLineItemCount("item")
      ) {
        payPrompt = true;
      }
      if (_tools.isInArray(_CurrentRole, _NoPromptRoles)) {
        payPrompt = false;
      }
    } else {
      payPrompt = false;
    }

    if (payPrompt) {
      var msg = "This exhibitor has an unpaid balance. Create new payment?";
      console.log("This exhibitor has an unpaid balance. Create new payment?");
      var go = window.confirm(msg);

      if (go) {
        nlapiSetFieldValue("custbody_create_payment", "T");
      }
    }
  }

  if (_BlockBilledOrderEditing) {
    if (
      nlapiGetFieldValue("status") == "Billed" &&
      nlapiGetRecordType() == "salesorder" &&
      !_tools.isInArray(nlapiGetUser(), _AdminUserIDs)
    ) {
      window.alert(
        "This booth order has already been billed. No further changes can be made. If you need to make a change, please edit the invoice."
      );
      return false;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function ng_clientValidateField_01(type, name, linenum) {
  if (_UseCnclPct && evType == "edit" && _ApplyCnclCharge) {
    if (type == "item") {
      if (name == "isclosed") {
        var isClosed = nlapiGetFieldValue(name);
        if (isClosed == "T") {
          return processCancellationPct();
        }
      }

      if (name == "quantity") {
        return processCancellationPct(linenum);
      }
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function ng_clientFieldChanged_01(type, name, linenum) {
  if (name == "custbody_show_table") {
    _ShowTableID = nlapiGetFieldValue(name);
    if (!_tools.isEmpty(_ShowTableID)) {
      getShowData();

      if (!_tools.isEmpty(_SHOW_DATA)) {
        if (_UseCSJobs) {
          nlapiSetFieldValue(
            "custbody_cseg_ng_cs_job",
            _SHOW_DATA["custrecord_show_job"]
          );
          nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"]);
        } else {
          nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"]);
        }
        if (_UseSubsidiaries) {
          if (!_tools.isEmpty(_SHOW_DATA["custrecord_show_subsidiary"])) {
            nlapiSetFieldValue(
              "subsidiary",
              _SHOW_DATA["custrecord_show_subsidiary"],
              false
            );
          } else {
            if (_tools.isEmpty(nlapiGetFieldValue("subsidiary"))) {
              nlapiSetFieldValue("subsidiary", "1", false);
            }
          }
        }
        if (_UseLocations) {
          nlapiSetFieldValue(
            "location",
            _SHOW_DATA["custrecord_show_venue"],
            false
          );
        }
        nlapiDisableField("entity", false);
        _AdvDate = nlapiStringToDate(_SHOW_DATA["custrecord_adv_ord_date"]);
        nlapiSetFieldValue(
          "custbody_advanced_order_date",
          _SHOW_DATA["custrecord_adv_ord_date"],
          false
        );
        // DEPRECATED TAX FORCE
        if (_UseCnclPct) {
          var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
          var pct =
            rawPct.search("%") >= 0
              ? new Number(rawPct.replace("%", ""))
              : new Number(rawPct);
          _CancellationPct = pct / 100;
        }

        if (_UseCnclPct) {
          _ApplyCnclCharge = isOnOrAfterMoveInDate(_ShowTableID);
        }
      }
    } else {
      nlapiSetFieldValue("class", "");
      nlapiSetFieldValue("location", "");
      if (_UseCSJobs) {
        nlapiSetFieldValue("custbody_cseg_ng_cs_job", "");
      }
      nlapiDisableField("entity", true);
    }

    if (_scLib.RetainLastShow) {
      var values = {};
      values["action"] = "set";
      values["user"] = nlapiGetUser();
      values["show"] = _ShowTableID;
      _scLib.setLastShow(values);
    }
  }

  if (name == "custbody_booth") {
    if (_tools.isEmpty(nlapiGetFieldValue("custbody_booth"))) {
      nlapiSetFieldValue("entity", "");
      nlapiSetFieldValue("custbody_booth_actual_exhibitor", "");
    }
  }

  ///////////////////////////////////
  /////////////////////////////////// START
  ///////////////////////////////////

  if (
    _tools.isInArray(name, [
      "custbody_booth",
      "custbody_show_table",
      "entity",
      "custbody_booth_actual_exhibitor",
    ])
  ) {
    console.log("BIG FIELD TRIGGER");
    _EntityChanged = false;
    if (name == "entity") {
      _EntityChanged = true;
    }
    if (_tools.isEmpty(_ShowTableID)) {
      _ShowTableID = nlapiGetFieldValue("custbody_show_table");
    }
    if (!_tools.isEmpty(_ShowTableID) && _tools.isEmpty(_SHOW_DATA)) {
      getShowData();
    } else {
      _SHOW_DATA = null;
    }

    var boothID = nlapiGetFieldValue("custbody_booth");
    var exhib_ID = nlapiGetFieldValue("entity");
    var actualExhib = nlapiGetFieldValue("custbody_booth_actual_exhibitor");

    if (
      !_tools.isEmpty(_ShowTableID) &&
      (!_tools.isEmpty(boothID) || !_tools.isEmpty(exhib_ID))
    ) {
      if (_scLib.PreventAdditionalOrders) {
        if (!_tools.isEmpty(boothID)) {
          var boothData = nlapiLookupField(
            "customrecord_show_booths",
            boothID,
            [
              "custrecord_booth_exhibitor",
              "custrecord_booth_actual_exhibitor",
              "name",
            ]
          );
          var exhbID = boothData.custrecord_booth_exhibitor;
          var actualExhbID = boothData.custrecord_booth_actual_exhibitor;
          if (_tools.isEmpty(exhbID)) {
            boothWithoutExhibitor();
            _EntityChanged = false;
            return;
          }

          if (_scLib.AllowMultiBillingParties && _tools.isEmpty(exhib_ID)) {
            nlapiSetFieldValue("entity", exhbID, false);
            nlapiSetFieldValue(
              "custbody_booth_actual_exhibitor",
              actualExhbID,
              false
            );
            exhib_ID = exhbID;
            selectExhibitor(
              _ShowTableID,
              boothID,
              exhib_ID,
              exhbID,
              actualExhbID
            );
          } else {
            selectExhibitor(
              _ShowTableID,
              boothID,
              exhib_ID,
              exhbID,
              actualExhbID
            );
          }
          setTimeout(function () {
            setOrderTax();
          }, 3000);
        } else if (!_tools.isEmpty(exhib_ID)) {
          selectBooth(_ShowTableID, exhib_ID);
        }

        if (_tools.isEmpty(gpl) || name == "custbody_show_table") {
          setTimeout(function () {
            setPriceLevel(exhib_ID, _ShowTableID);
          }, 2000);
        }
        if (
          !_tools.isEmpty(_ShowTableID) &&
          _UseTaxRate &&
          (_tools.isEmpty(taxItem) || name == "custbody_show_table")
        ) {
          setTimeout(function () {
            setOrderTax();
          }, 3000);
        }
      } else {
        if (
          !_tools.isEmpty(_ShowTableID) &&
          (!_tools.isEmpty(boothID) || !_tools.isEmpty(exhib_ID))
        ) {
          if (!_tools.isEmpty(boothID)) {
            var boothData = nlapiLookupField(
              "customrecord_show_booths",
              boothID,
              [
                "custrecord_booth_exhibitor",
                "custrecord_booth_actual_exhibitor",
                "name",
              ]
            );
            var exhbID = boothData.custrecord_booth_exhibitor;
            var actualExhbID = boothData.custrecord_booth_actual_exhibitor;
            if (
              _tools.isEmpty(exhbID) ||
              (_tools.isEmpty(actualExhbID) && _scLib.AllowMultiBillingParties)
            ) {
              boothWithoutExhibitor();
              _EntityChanged = false;
              return;
            } else {
              if (!_EntityChanged && !_scLib.AllowMultiBillingParties) {
                nlapiSetFieldValue("entity", exhbID, false);
              }
              nlapiSetFieldValue(
                "custbody_booth_actual_exhibitor",
                actualExhbID,
                false
              );
            }
          } else if (!_tools.isEmpty(exhib_ID)) {
            selectBooth(_ShowTableID, exhib_ID);
          }

          if (_tools.isEmpty(gpl) || name == "custbody_show_table") {
            setTimeout(function () {
              setPriceLevel(exhib_ID, _ShowTableID);
            }, 2000);
          }
          if (
            !_tools.isEmpty(_ShowTableID) &&
            _UseTaxRate &&
            (_tools.isEmpty(taxItem) || name == "custbody_show_table")
          ) {
            setTimeout(function () {
              setOrderTax();
            }, 3000);
          }
        }
      }

      if (!_tools.isEmpty(exhib_ID)) {
        if (!_tools.isEmpty(_SHOW_DATA)) {
          if (_UseCSJobs) {
            nlapiSetFieldValue(
              "custbody_cseg_ng_cs_job",
              _SHOW_DATA["custrecord_show_job"]
            );
            nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"]);
          } else {
            nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"]);
          }
        }
      }
    } else if (
      !_tools.isEmpty(_ShowTableID) &&
      name == "custbody_booth_actual_exhibitor" &&
      !_tools.isEmpty(actualExhib) &&
      _scLib.AllowMultiBillingParties
    ) {
      var bFilt = new Array(
        ["custrecord_booth_show_table", "anyof", [_ShowTableID]],
        "and",
        ["custrecord_booth_actual_exhibitor", "anyof", [actualExhib]]
      );
      var bCols = new Array(
        new nlobjSearchColumn("custrecord_booth_exhibitor", null, null),
        new nlobjSearchColumn("name", null, null)
      );
      var bSearch = null;
      var hasErr = false;
      try {
        bSearch = nlapiSearchRecord(
          "customrecord_show_booths",
          null,
          bFilt,
          bCols
        );
      } catch (err) {
        _log.logError(err, "Error encountered getting exhibitor booths");
        console.log("Error encountered getting exhibitor booths", err);
        hasErr = true;
      }

      if (bSearch != null) {
        if (bSearch.length == 1) {
          nlapiSetFieldValue("custbody_booth", bSearch[0].getId(), false);
          nlapiSetFieldValue(
            "entity",
            bSearch[0].getValue("custrecord_booth_exhibitor"),
            true
          );
        } else if (bSearch.length > 1) {
          var boothList = new Array();
          for (var b = 0; b < bSearch.length; b++) {
            boothList.push(bSearch[b].getValue("name"));
          }
          window.alert(
            'This exhibitor has multiple booths associated with this show. Please select one of the following in the "Booth" field.\n\n{0}'.NG_Format(
              boothList.join("\n")
            )
          );
          return;
        }
      } else if (!hasErr) {
        window.alert(
          "This exhibitor does not apear to have any booths associated with this show."
        );
        nlapiSetFieldValue("custbody_booth_actual_exhibitor", "", false);
      }
    }
    _EntityChanged = false;

    if (
      !_tools.isEmpty(_ShowTableID) &&
      !_tools.isEmpty(boothID) &&
      (_scLib.AllowMultiBillingParties ? !_tools.isEmpty(exhib_ID) : true)
    ) {
      setTimeout(function () {
        triggerBackgroundExhibData();
      }, 4000);
    }
    if (
      !_tools.isEmpty(_ShowTableID) &&
      !_tools.isEmpty(boothID) &&
      !_tools.isEmpty(exhib_ID)
    ) {
      try {
        if (_tools.isEmpty(_SHOW_DATA)) {
          getShowData();
        }
        if (!_tools.isEmpty(_SHOW_DATA)) {
          setFacilityShipAddress(
            _SHOW_DATA["custrecord_facility"],
            _SHOW_DATA["custrecord_tax_rate"]
          );
        }
      } catch (err) {}
    }

    if (!_tools.isEmpty(_SHOW_DATA)) {
    }
  }

  if (name == "taxitem") {
    nlapiSetFieldValue(
      "custbody_ng_cs_tax_temp",
      nlapiGetFieldValue("taxitem")
    );
  }

  ///////////////////////////////////
  /////////////////////////////////// END
  ///////////////////////////////////

  if (type == "item") {
    if (name == "quantity") {
      addingToLine02 = addingToLine02 || false;
      if (!addingToLine01 && !addingToLine02) {
        addingToLine01 = true;
        var parentItem = nlapiGetCurrentLineItemValue(type, "item");
        if (!_tools.isEmpty(parentItem)) {
          var currQty = new Number(nlapiGetCurrentLineItemValue(type, name));
          var booth = nlapiGetFieldValue("custbody_booth");
          var minQ = new Number(
            nlapiLookupField("item", parentItem, "minimumquantity")
          );
          if (isNaN(minQ)) {
            minQ = new Number(0);
          }
          if (minQ > 1 && currQty < minQ) {
            nlapiSetCurrentLineItemValue(name, minQ, true);
          } else if (
            (_tools.isInArray(parentItem, _scLib.sqftItems) ||
              _tools.isInArray(parentItem, _scLib.sqdItems)) &&
            !_tools.isEmpty(booth)
          ) {
            var boothDims = nlapiLookupField(
              "customrecord_show_booths",
              booth,
              ["custrecord_booth_length", "custrecord_booth_width"]
            );
            var boothL = new Number(boothDims.custrecord_booth_length);
            var boothW = new Number(boothDims.custrecord_booth_length);
            if (isNaN(boothL) || boothL < 10) {
              boothL = new Number(10);
            }
            if (isNaN(boothW) || boothW < 10) {
              boothW = new Number(10);
            }
            minQ = boothL * boothW;
            if (currQty < minQ) {
              nlapiSetCurrentLineItemValue(name, minQ, true);
            }
          }
          addingToLine01 = false;
        }
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @returns {Void}
 */
function ng_clientPostSourcing_01(type, name) {
  if (name == "entity") {
    setTimeout(function () {
      if (_tools.isEmpty(gpl)) {
        gpl = nlapiGetFieldValue("custbody_price_level");
      }
      setOrderTax();
    }, 250);
  }

  if (type == "item") {
    if (name == "item") {
      if (!addingToLine01 && !addingToLine02) {
        if (!_tools.isEmpty(nlapiGetFieldValue("custbody_price_level"))) {
          gpl = nlapiGetFieldValue("custbody_price_level");
        }
        if (!_tools.isEmpty(gpl)) {
          nlapiSetCurrentLineItemValue(type, "price", gpl);
        }
      }

      if (_scLib.SalesTaxOnItemLine) {
        if (!_tools.isEmpty(_SHOW_DATA)) {
          if (
            !_tools.isEmpty(_SHOW_DATA.custrecord_tax_rate) &&
            !_tools.isEmpty(_SHOW_DATA.custrecord_tax_percent)
          ) {
            setTimeout(function () {
              nlapiSetCurrentLineItemValue(
                type,
                "taxcode",
                _SHOW_DATA.custrecord_tax_rate
              );
              nlapiSetCurrentLineItemValue(
                type,
                "taxrate1",
                _SHOW_DATA.custrecord_tax_percent.replace("%", "")
              );
            }, 500);
          }
        }
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientLineInit_01(type) {}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to save line item, false to abort save
 */
function ng_clientValidateLine_01(type) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientRecalc_01(type) {
  if (addingToLine01) {
    return;
  }

  if (_NewCancellation != null) {
    addingToLine01 = true;

    try {
      setTimeout(function () {
        nlapiSelectNewLineItem("item");
        nlapiSetCurrentLineItemValue(
          "item",
          "item",
          _scLib.CancellationChargeID
        );
        setTimeout(function () {
          nlapiSetCurrentLineItemValue("item", "quantity", "1", false);
          nlapiSetCurrentLineItemValue("item", "price", "-1");
          setTimeout(function () {
            nlapiSetCurrentLineItemValue(
              "item",
              "rate",
              new Number(_NewCancellation.rate).toFixed(2)
            );
            nlapiSetCurrentLineItemValue(
              "item",
              "description",
              _NewCancellation.description
            );
            nlapiSetCurrentLineItemValue(
              "item",
              "custcol_canc_descr",
              _NewCancellation.description
            );

            setTimeout(function () {
              nlapiCommitLineItem("item");
              _NewCancellation = null;
              addingToLine01 = false;
            }, 250);
          }, 500);
        }, 250);
      }, 500);
    } catch (err) {
      _log.logError(
        err,
        "Error encountered adding cancellation charge to line item"
      );
      window.alert(
        "There was a problem with adding the cancellation charge to this order"
      );
      addingToLine01 = false;
      _NewCancellation = null;
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item insert, false to abort insert
 */
function ng_clientValidateInsert_01(type) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item delete, false to abort delete
 */
function ng_clientValidateDelete_01(type) {
  if (_UseCnclPct && evType == "edit" && _ApplyCnclCharge) {
    if (type == "item") {
      var lineCode = nlapiGetCurrentLineItemValue("item", "custcol_linecode");
      if (_tools.isEmpty(lineCode)) {
        return true;
      }
      var lineItemID = nlapiGetCurrentLineItemValue("item", "item");
      if (lineItemID == _scLib.CancellationChargeID) {
        return window.confirm(
          "Are you certain you want to remove this cancellation charge from the order?"
        );
      }
      return processCancellationPct();
    }
  }

  return true;
}

function setPriceLevel(exhbID) {
  if (_tools.isEmpty(_SHOW_DATA)) {
    getShowData();
  }
  var orderType = nlapiGetFieldValue("custbody_ng_cs_order_type");
  if (
    (_tools.isEmpty(_scLib.ShowMgmtOrderTypes) ||
      (_scLib.ShowMgmtOrderTypes || []).length == 0) &&
    !_tools.isEmpty(_scLib.DefaultShowMgmtOrderType)
  ) {
    _scLib.ShowMgmtOrderTypes = [_scLib.DefaultShowMgmtOrderType];
  }

  if (!_tools.isInArray(orderType, _scLib.ShowMgmtOrderTypes)) {
    var today = nlapiStringToDate(
      nlapiDateToString(new Date(), "date")
    ).getTime();
    if (!_tools.isEmpty(_SHOW_DATA)) {
      if (!_tools.isEmpty(_SHOW_DATA["custrecord_adv_ord_date"])) {
        _AdvDate = nlapiStringToDate(_SHOW_DATA["custrecord_adv_ord_date"]);
        var advDate = _AdvDate.getTime();

        if (today <= advDate) {
          gpl = _SHOW_DATA["custrecord_adv_price_level"];
        } else {
          var startDate = _scLib.getStartDate(_ShowTableID);
          if (startDate != null && today >= startDate) {
            gpl =
              _SHOW_DATA["custrecord_site_price_level"] ||
              _SHOW_DATA["custrecord_std_price_level"] ||
              gpl;
          } else {
            gpl = _SHOW_DATA["custrecord_std_price_level"] || gpl;
          }
        }
      } else {
        var startDate = _scLib.getStartDate(_ShowTableID);
        if (startDate != null && today >= startDate) {
          gpl =
            _SHOW_DATA["custrecord_site_price_level"] ||
            _SHOW_DATA["custrecord_std_price_level"] ||
            gpl;
        } else {
          gpl = _SHOW_DATA["custrecord_std_price_level"] || gpl;
        }
      }
      gpl = gpl || "1";
      nlapiSetFieldValue("custbody_price_level", gpl);
    }
  } else {
    if (!_tools.isEmpty(_SHOW_DATA)) {
      if (!_tools.isEmpty(_SHOW_DATA["custrecord_show_mgmnt_price_lvl"])) {
        nlapiSetFieldValue(
          "custbody_price_level",
          _SHOW_DATA["custrecord_show_mgmnt_price_lvl"]
        );
        gpl = _SHOW_DATA["custrecord_show_mgmnt_price_lvl"];
      }
    }
  }
}

function getStartDate() {
  if (_tools.isEmpty(_ShowTableID)) {
    getShowData();
  }
  var startDateT = _scLib.getShowDates(_ShowTableID, "min", false);
  var startDateD = !_tools.isEmpty(startDateT)
    ? nlapiStringToDate(startDateT)
    : null;
  var startDate = !_tools.isEmpty(startDateD) ? startDateD.getTime() : null;

  return startDate;
}

function processCancellationPct(linenum) {
  if (linenum == null) {
    var cnclCharge = _M.roundToHundredths(
      _CancellationPct *
        new Number(nlapiGetCurrentLineItemValue("item", "amount"))
    );
    var go = window.confirm(
      "This action will incur a $" +
        cnclCharge.toFixed(2) +
        " cancellation charge. Continue?"
    );
    if (go) {
      _NewCancellation = {
        rate: cnclCharge,
        description:
          "Cancellation of " +
          nlapiGetCurrentLineItemText("item", "item") +
          " x " +
          nlapiGetCurrentLineItemValue("item", "quantity"),
      };
    }

    return go;
  } else {
    var currItem = nlapiGetCurrentLineItemValue("item", "item");
    var recItem = _CurrentRecord.getLineItemValue("item", "item", linenum);
    var currQty = new Number(nlapiGetCurrentLineItemValue("item", "quantity"));
    var oldQty = 0;
    if (currItem == recItem) {
      oldQty = new Number(
        _CurrentRecord.getLineItemValue("item", "quantity", linenum)
      );
    } else {
      var lineCode = nlapiGetCurrentLineItemValue("item", "custcol_linecode");
      var recLines = _CurrentRecord.getLineItemCount("item");
      for (var l = 1; l <= recLines; l++) {
        var recLineCode = _CurrentRecord.getLineItemValue(
          "item",
          "custcol_linecode",
          l
        );
        if (recLineCode == lineCode) {
          oldQty = new Number(
            _CurrentRecord.getLineItemValue("item", "quantity", l)
          );
          break;
        }
      }
    }

    if (oldQty == 0 || (!(oldQty < 0) && !(oldQty > 0))) {
      return true;
    } else {
      if (currQty < oldQty) {
        var diff = _M.roundToHundredths(oldQty - currQty);
        var rate = new Number(nlapiGetCurrentLineItemValue("item", "rate"));
        var cnclCharge = _M.roundToHundredths(_CancellationPct * (diff * rate));
        var go = window.confirm(
          "This action will incur a $" +
            cnclCharge.toFixed(2) +
            " cancellation charge. Continue?"
        );
        if (go) {
          _NewCancellation = {
            rate: cnclCharge,
            description:
              "Cancellation of " +
              nlapiGetCurrentLineItemText("item", "item") +
              " x " +
              diff,
          };
        }

        return go;
      } else {
        return true;
      }
    }
  }
}

function isOnOrAfterMoveInDate(_ShowTableID) {
  var threshold = _scLib.CancellationChargeThreshold;
  if (_tools.isInArray(threshold, ["2", "3", "4"])) {
    var map = [
      null,
      null,
      _scLib.DefaultShowDateType,
      _scLib.DefaultShowMoveInDateType,
      _scLib.DefaultExhibMoveInDateType,
    ];
    var filt = new Array(
      ["custrecord_show_number_date", "anyof", [_ShowTableID]],
      "and",
      ["custrecord_date_type", "anyof", [parseInt(map[threshold])]]
    );
    var cols = new Array(new nlobjSearchColumn("custrecord_date", null, "min"));
    var search = null;
    try {
      search = nlapiSearchRecord("customrecord_show_date", null, filt, cols);
    } catch (err) {
      _log.logError(err, "Error encounterd searching for show move-in dates");
    }

    if (search != null) {
      var showDateRaw = search[0].getValue(cols[0]);
      if (!_tools.isEmpty(showDateRaw)) {
        var showDate = nlapiStringToDate(showDateRaw);
        if (_Today.getTime() >= showDate.getTime()) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  } else {
    if (_tools.isEmpty(_AdvDate)) {
      return false;
    } else {
      if (_Today.getTime() <= _AdvDate.getTime()) {
        return false;
      } else {
        return true;
      }
    }
  }
}

function setFacilityShipAddress(facility, taxItem) {
  /*if (!_tools.isEmpty(facility)) {
		setTimeout(function() {
			var facAddrRef = "facAddrRef";
			if (!_tools.isEmpty(facAddrRef)) {
				var shipAddrValues = nlapiLookupField("customrecord_facility", facility, ["custrecord_address_ref.shipaddressee","custrecord_address_ref.shipaddress1","custrecord_address_ref.shipaddress2","custrecord_address_ref.shipcity","custrecord_address_ref.shipcountry","custrecord_address_ref.shipphone","custrecord_address_ref.shipstate","custrecord_address_ref.shipzip","custrecord_address_ref.addressinternalid"]);
				if (!_tools.isEmpty(shipAddrValues)) {
					nlapiSetFieldValue("shipaddresslist", "", true);
					nlapiSetFieldValue("shipoverride", "T");
					nlapiSetFieldValue("shipcountry", shipAddrValues['custrecord_address_ref.shipcountry']);
					nlapiSetFieldValue("shipaddr1", shipAddrValues['custrecord_address_ref.shipaddress1']);
					nlapiSetFieldValue("shipaddr2", shipAddrValues['custrecord_address_ref.shipaddress2']);
					nlapiSetFieldValue("shipphone", shipAddrValues['custrecord_address_ref.shipphone']);
					nlapiSetFieldValue("shipcity", shipAddrValues['custrecord_address_ref.shipcity']);
					nlapiSetFieldValue("shipstate", shipAddrValues['custrecord_address_ref.shipstate']);
					nlapiSetFieldValue("shipzip", shipAddrValues['custrecord_address_ref.shipzip']);
					nlapiSetFieldValue("shipaddress", "{0}{1}\n{2}{3}, {4} {5}{6}".NG_Format(
						!_tools.isEmpty(shipAddrValues['custrecord_address_ref.shipaddressee']) ? "{0}\n".NG_Format(shipAddrValues['custrecord_address_ref.shipaddressee']) : "",
						shipAddrValues['custrecord_address_ref.shipaddress1'],
						!_tools.isEmpty(shipAddrValues['custrecord_address_ref.shipaddress2']) ? "{0}\n".NG_Format(shipAddrValues['custrecord_address_ref.shipaddress2']) : "",
						shipAddrValues['custrecord_address_ref.shipcity'],
						shipAddrValues['custrecord_address_ref.shipstate'],
						shipAddrValues['custrecord_address_ref.shipzip'],
						!_tools.isEmpty(shipAddrValues['custrecord_address_ref.shipphone']) ? "\n{0}".NG_Format(shipAddrValues['custrecord_address_ref.shipphone']) : ""
					));
					_ShipAddySet = true;
					if (!_tools.isEmpty(taxItem)) {
						setTimeout(function() {
							nlapiSetFieldValue("taxitem", taxItem);
						}, 2000);
					}
				}
			}
		}, 1500);
	}*/
  return;
}

function setOrderTax() {
  if (_tools.isEmpty(_SHOW_DATA)) {
    getShowData();
  }
  if (!_tools.isEmpty(_SHOW_DATA)) {
    if (!_scLib.SalesTaxOnItemLines) {
      taxItem = null;
      var taxRate = null;
      try {
        taxItem = _SHOW_DATA["custrecord_tax_rate"];
        taxRate = _SHOW_DATA["custrecord_tax_percent"];
      } catch (err) {
        console.log(err);
      }
      if (!_tools.isEmpty(taxItem)) {
        setTimeout(function () {
          nlapiSetFieldValue("istaxable", "T", true);
          nlapiSetFieldValue("taxitem", taxItem, true);
          nlapiSetFieldValue("taxrate", taxRate, true);
        }, 250);
      }
    } else {
      var itemLineCount = nlapiGetLineItemCount("item");
      if (itemLineCount > 0) {
        setTimeout(function () {
          /*if (window.confirm("Update sales tax on item lines now?")) {
						setTimeout(function() {
							updateLineSalesTax(1, itemLineCount);
						}, 2000);
					}*/
        }, 1000);
      }
    }
  }
}

function updateLineSalesTax(line, itemLineCount) {
  nlapiSelectLineItem("item", line);
  nlapiSetCurrentLineItemValue(
    "item",
    "taxcode",
    _SHOW_DATA.custrecord_tax_rate
  );
  if (
    nlapiGetCurrentLineItemValue("item", "item") != _scLib.ConvenienceFeeItem
  ) {
    nlapiSetCurrentLineItemValue(
      "item",
      "taxcode",
      _SHOW_DATA.custrecord_tax_rate
    );
    if (!_scLib.UseCanadianSalesTax) {
      nlapiSetCurrentLineItemValue(
        "item",
        "taxrate1",
        _SHOW_DATA.custrecord_tax_percent.replace("%", "")
      );
    } else {
      nlapiSetCurrentLineItemValue(
        "item",
        "taxrate1",
        _SHOW_DATA.custrecord_ng_cs_evt_gst_pct.replace("%", "")
      );
      nlapiSetCurrentLineItemValue(
        "item",
        "taxrate2",
        _SHOW_DATA.custrecord_ng_cs_evt_pst_pct.replace("%", "")
      );
    }
  } else {
    nlapiSetCurrentLineItemValue("item", "taxcode", _scLib.ConvFeeTax);
    if (!_scLib.UseCanadianSalesTax) {
      nlapiSetCurrentLineItemValue("item", "taxrate1", "0");
    } else {
      nlapiSetCurrentLineItemValue("item", "taxrate1", "0");
      nlapiSetCurrentLineItemValue("item", "taxrate2", "0");
    }
  }

  setTimeout(function () {
    nlapiCommitLineItem("item");
    setTimeout(function () {
      var nextLine = Math.round(line + 1);
      if (nextLine <= itemLineCount) {
        updateLineSalesTax(nextLine, itemLineCount);
      } else {
        return true;
      }
    }, 500);
  }, 500);
}

function selectExhibitor(
  _ShowTableID,
  boothID,
  exhib_ID,
  exhbID,
  actualExhbID,
  setBooth
) {
  var orderNum = nlapiGetFieldValue("tranid");
  var filt = new Array(
    ["custbody_show_table", "anyof", [_ShowTableID]],
    "and",
    ["custbody_booth", "anyof", [boothID]]
  );
  if (_scLib.AllowMultiBillingParties) {
    filt.push("and", ["entity", "anyof", [exhib_ID]]);
  }
  var search = null;
  try {
    search = nlapiSearchRecord("salesorder", null, filt, null);
  } catch (err) {
    console.log(err);
  }
  if (search != null) {
    var soID = search[0].getId();
    try {
      if (_scLib.PrevntBthOrderRedirectAlert) {
        _tools.clearFormChanged();
      }
      console.log("triggering redirect with delay (A)");
      setTimeout(function () {
        var go = window.confirm(
          "You cannot save this order as one already exists for this booth" +
            (_scLib.AllowMultiBillingParties ? " and billing party" : "") +
            '. Click "OK" to redirect to the existing order, click "Cancel" to stay here and edit the order.'
        );
        if (go) {
          if (_scLib.PrevntBthOrderRedirectAlert) {
            _tools.clearFormChanged();
            if (NS.form.isChanged()) NS.form.setChanged(false);
          }
          console.log(
            "initiating redirect (A) -- Is Changed: {0}".NG_Format(
              NS.form.isChanged()
            )
          );
          window.location = nlapiResolveURL(
            "RECORD",
            "salesorder",
            soID,
            "EDIT"
          );
        }
      }, 1000);
    } catch (err) {
      console.log(err);
    }
  } else {
    if (_tools.isEmpty(orderNum) || orderNum == "To Be Generated") {
      if (setBooth) {
        nlapiSetFieldValue("custbody_booth", boothID, false);
      } else if (!_EntityChanged && !_scLib.AllowMultiBillingParties) {
        nlapiSetFieldValue("entity", exhbID, false);
      }
      nlapiSetFieldValue(
        "custbody_booth_actual_exhibitor",
        actualExhbID,
        false
      );
    } else {
      if (
        _EntityChanged &&
        !_tools.isEmpty(_PreviousExhib) &&
        _PreviousExhib != exhbID
      ) {
        if (
          window.confirm(
            "Are you changing the {0} on this order?".NG_Format(
              _scLib.AllowMultiBillingParties ? "billing party" : "exhibitor"
            )
          )
        ) {
          if (setBooth) {
            nlapiSetFieldValue("custbody_booth", boothID, false);
          } else {
            nlapiSetFieldValue("entity", exhib_ID, false);
          }
          nlapiSetFieldValue(
            "custbody_booth_actual_exhibitor",
            actualExhbID,
            false
          );
          return;
        }
      }

      var loc = nlapiResolveURL("RECORD", "salesorder", null, "EDIT");
      if (loc.search(/\?/g) >= 0) {
        loc += "&";
      } else {
        loc += "?";
      }
      loc +=
        "cf={0}&custbody_show_table={1}&custbody_booth={2}&entity={3}&custbody_booth_actual_exhibitor={4}".NG_Format(
          nlapiGetFieldValue("customform"),
          _ShowTableID,
          boothID,
          _scLib.AllowMultiBillingParties ? exhib_ID : exhbID,
          actualExhbID
        );
      console.log("triggering redirect with delay (B)");
      setTimeout(function () {
        if (_scLib.PrevntBthOrderRedirectAlert) {
          _tools.clearFormChanged();
          if (NS.form.isChanged()) NS.form.setChanged(false);
        }
        console.log("initiating redirect (B)");
        window.location = loc;
      }, 1000);
    }
  }
}

function selectBooth(_ShowTableID, exhib_ID) {
  var bfilt = new Array(
    ["custrecord_booth_show_table", "anyof", [_ShowTableID]],
    "and",
    ["custrecord_booth_exhibitor", "anyof", [exhib_ID]]
  );
  var bcols = new Array(
    new nlobjSearchColumn("custrecord_booth_number", null, null),
    new nlobjSearchColumn("custrecord_booth_actual_exhibitor", null, null)
  );
  var bsearch = null;
  try {
    bsearch = nlapiSearchRecord("customrecord_show_booths", null, bfilt, bcols);
  } catch (err) {
    window.alert("Search error: [{0}] {1}".NG_Format(err.name, err.message));
  }
  if (bsearch != null) {
    var boothID = null;
    var actualExhbID = null;
    if (bsearch.length == 1) {
      boothID = bsearch[0].getId();
      actualExhbID = bsearch[0].getValue("custrecord_booth_actual_exhibitor");
    } else if (bsearch.length > 1) {
      var booths = {};
      for (var i = 0; i < bsearch.length; i++) {
        booths[bsearch[i].getValue("custrecord_booth_number").toUpperCase()] =
          bsearch[i].getId();
      }

      var prompt =
        "Please enter one of the following booth numbers in the field below:\n\n";
      for (var key in booths) {
        prompt += key + "\n";
      }

      var error = false;
      var errmsg = "You have entered an invalid selection.\n\n";
      while (true) {
        var message = "";
        if (error) {
          message = errmsg + prompt;
        } else {
          message = prompt;
        }

        var boothSel = window.prompt(message);
        if (_tools.isEmpty(boothSel)) {
          return;
        }

        if (booths[boothSel.toUpperCase()] != null) {
          boothID = booths[boothSel.toUpperCase()];
          for (var i = 0; i < bsearch.length; i++) {
            if (bsearch[i].getId() == boothID) {
              actualExhbID = bsearch[i].getValue(
                "custrecord_booth_actual_exhibitor"
              );
              break;
            }
          }
          break;
        } else {
          error = true;
        }
      }
    }

    if (!_tools.isEmpty(boothID) && _scLib.PreventAdditionalOrders) {
      selectExhibitor(
        _ShowTableID,
        boothID,
        exhib_ID,
        exhib_ID,
        actualExhbID,
        true
      );
      setTimeout(function () {
        setOrderTax();
      }, 3000);
    } else {
      nlapiSetFieldValue("custbody_booth", boothID, false);
      nlapiSetFieldValue(
        "custbody_booth_actual_exhibitor",
        actualExhbID,
        false
      );
    }
  } else {
    window.alert(
      "This exhibitor does not appear to have any booths for this show."
    );
  }
}

function boothWithoutExhibitor() {
  window.alert("This booth does not yet have an associated exhibitor.");
  nlapiSetFieldValue("entity", "", false);
  nlapiSetFieldValue("custbody_booth_actual_exhibitor", "", false);
}

function triggerBackgroundExhibData(go) {
  console.log("triggerBackgroundExhibData");
  setTimeout(function () {
    if (!go) {
      console.log("triggering existing order function");
      getExistingOrders();
    } else {
      console.log("triggering payment totaling function");
      getPayments();
    }
  }, 2000);
}

function triggerBackgroundExhibData_ALT() {
  var dataURL = nlapiResolveURL(
    "SUITELET",
    "customscript_ng_cs_sl_bth_data_init",
    "customdeploy_ng_cs_sl_bth_data_init_dep"
  );
  var params = {
    sti: _ShowTableID,
    bi: nlapiGetFieldValue("custbody_booth"),
    ei: nlapiGetFieldValue("entity"),
  };
  nlapiRequestURL(
    dataURL,
    params,
    { "User-Agent-x": "SuiteScript-Call" },
    handleDataCallback,
    "POST"
  );
}

function handleDataCallback(response) {
  if (response.getCode() == 200) {
    var data = JSON.parse(response.getBody());
    _ExistingOrder = data.existingOrder;
    _totalPaid = new Number(data.totalPaid);
  }
}

function getExistingOrders() {
  console.log("getExistingOrders");
  var booth = nlapiGetFieldValue("custbody_booth");
  var exhib = nlapiGetFieldValue("entity");

  var filt = new Array(
    ["custbody_show_table", "anyof", [_ShowTableID]],
    "and",
    ["custbody_booth", "anyof", [booth]],
    "and",
    ["custbody_to_be_deleted", "is", "F"],
    "and",
    ["mainline", "is", "T"]
  );
  if (_scLib.AllowMultiBillingParties) {
    filt.push("and", ["entity", "anyof", [exhib]]);
  }
  var search = null;
  console.log("searching for existing orders");
  try {
    search = nlapiSearchRecord("salesorder", null, filt, null);
  } catch (err) {}
  if (search != null) {
    _ExistingOrder.check = true;
    _ExistingOrder.id = search[0].getId();
  }
  console.log("search complete");
  triggerBackgroundExhibData(true);
}

function getPayments() {
  console.log("getPayments");
  var booth = nlapiGetFieldValue("custbody_booth");
  var exhib = nlapiGetFieldValue("entity");
  var pSearch = null;
  var tFilt = new Array(
    ["custbody_show_table", "anyof", [_ShowTableID]],
    "and",
    ["custbody_booth", "anyof", [booth]],
    "and",
    ["mainline", "is", "T"],
    "and",
    ["type", "anyof", ["CustPymt", "CustDep"]]
  );
  if (_scLib.AllowMultiBillingParties) {
    tFilt.push("and", ["entity", "anyof", [exhib]]);
  }
  var pCols = new Array(
    new nlobjSearchColumn("internalid", null, null),
    new nlobjSearchColumn("trandate", null, null),
    new nlobjSearchColumn("amount", null, null)
  );
  console.log("searching for payments");
  try {
    pSearch = nlapiSearchRecord("transaction", null, tFilt, pCols);
  } catch (err) {
    _log.logError(err, "Error encountered searching for related payments");
  }
  if (pSearch != null) {
    for (var p = 0; p < pSearch.length; p++) {
      var payAmount = new Number(pSearch[p].getValue("amount"));
      _totalPaid += payAmount;
    }
  }
  console.log("search complete");
  setTimeout(function () {
    getRefunds();
  }, 2000);
}

function getRefunds() {
  console.log("getRefunds");
  var booth = nlapiGetFieldValue("custbody_booth");
  var exhib = nlapiGetFieldValue("entity");
  var rSearch = null;
  var tFilt = new Array(
    ["custbody_show_table", "anyof", [_ShowTableID]],
    "and",
    ["custbody_booth", "anyof", [booth]],
    "and",
    ["mainline", "is", "T"]
  );
  if (_scLib.AllowMultiBillingParties) {
    tFilt.push("and", ["entity", "anyof", [exhib]]);
  }
  var pCols = new Array(
    new nlobjSearchColumn("internalid", null, null),
    new nlobjSearchColumn("trandate", null, null),
    new nlobjSearchColumn("amount", null, null)
  );
  console.log("searching for refunds");
  try {
    rSearch = nlapiSearchRecord("customerrefund", null, tFilt, pCols);
  } catch (err) {
    _log.logError(err, "Error encountered searching for related refunds");
  }
  if (rSearch != null) {
    for (var r = 0; r < rSearch.length; r++) {
      var refAmount = new Number(rSearch[r].getValue("amount"));
      _totalPaid += refAmount;
    }
  }
  console.log("search complete");
}

function checkForAdditionalItems() {
  var itemList = new Array();
  for (var l = 1; l <= nlapiGetLineItemCount("item"); l++) {
    itemList.push(nlapiGetLineItemValue("item", "item", l));
  }
  var itemFilt = new Array(["internalid", "anyof", itemList]);
  var itemCols = new Array(
    new nlobjSearchColumn("internalid", "custitem_req_addtnl_items", null),
    new nlobjSearchColumn("itemid", "custitem_req_addtnl_items", null),
    new nlobjSearchColumn("itemid", null, null)
  );
  var itemSearch = null;
  try {
    itemSearch = nlapiSearchRecord("item", null, itemFilt, itemCols);
  } catch (err) {
    _log.logError(err, "Error encountered finding required additional items");
  }

  if (itemSearch != null) {
    var itemMap = {};
    for (var i = 0; i < itemSearch.length; i++) {
      if (!_tools.isEmpty(itemSearch[i].getValue(itemCols[0]))) {
        if (_tools.isEmpty(itemMap[itemSearch[i].getId()])) {
          itemMap[itemSearch[i].getId()] = {};
          itemMap[itemSearch[i].getId()].items = new Array();
          itemMap[itemSearch[i].getId()].name = itemSearch[i].getValue(
            itemCols[2]
          );
        }
        itemMap[itemSearch[i].getId()].items.push({
          id: itemSearch[i].getValue(itemCols[0]),
          name: itemSearch[i].getValue(itemCols[1]),
        });
      }
    }

    if (_tools.getKeyCount(itemMap) > 0) {
      for (var key in itemMap) {
        var mainMap = itemMap[key];
        var missingList = new Array();

        for (var i = 0; i < mainMap.items.length; i++) {
          if (!_tools.isInArray(mainMap.items[i].id, itemList)) {
            missingList.push(mainMap.items[i].name);
          }
        }

        if (missingList.length > 0) {
          var msg =
            "The following required additional items are missing for item {0}:\n\n{1}".NG_Format(
              mainMap.name,
              missingList.join("\n")
            );
          window.alert(msg);
          return false;
        }
      }
    }
  }

  return true;
}

function getOrderTotal() {
  var taxRate = new Number(nlapiGetFieldValue("taxrate").replace("%", ""));
  if (isNaN(taxRate)) {
    taxRate = new Number(0);
  }
  var lines = nlapiGetLineItemCount("item");
  var taxable = new Number(0);
  var nontaxable = new Number(0);
  var orderTaxable = nlapiGetFieldValue("istaxable") == "T";
  for (var l = 1; l <= lines; l++) {
    if (nlapiGetLineItemValue("item", "custcol_cost_is_estimated", l) != "T") {
      if (
        nlapiGetLineItemValue("item", "istaxable", l) == "T" &&
        orderTaxable
      ) {
        taxable += new Number(nlapiGetLineItemValue("item", "amount", l));
      } else {
        nontaxable += new Number(nlapiGetLineItemValue("item", "amount", l));
      }
    }
  }
  taxable = _M.roundToHundredths(taxable);
  nontaxable = _M.roundToHundredths(nontaxable);
  var salestax = _M.roundToHundredths(taxable * (taxRate / 100));
  return _M.roundToHundredths(taxable + nontaxable + salestax);
}

//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//
//	BEGIN ADD ITEM LINE CLIENT SCRIPTING
//
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function ng_clientPageInit_02(type) {
  if (_tools.isEmpty(_SHOW_DATA)) {
    getShowData();
  }
  var subsidiary = null;
  if (_UseSubsidiaries) {
    subsidiary = !_tools.isEmpty(nlapiGetFieldValue("subsidiary"))
      ? nlapiGetFieldValue("subsidiary")
      : null;
  }

  var itemsListingsRAW = nlapiGetFieldValue("custpage_ng_items_list");
  if (!_tools.isEmpty(itemsListingsRAW)) {
    var itemsListings = JSON.parse(itemsListingsRAW);
    for (var key in itemsListings) {
      _scLib[key] = itemsListings[key];
    }
  } else {
    _scLib.initialize.getSelectionItems(subsidiary);
  }

  if (_scLib.RetainLastItemCat) {
    var itemCategory = nlapiGetFieldValue("custbody_item_category");
    if (!_tools.isEmpty(itemCategory)) {
      ngItemCategoryDisable(false);
      nlapiSetFieldValue("custbody_carpet_width", 0);
      nlapiSetFieldValue("custbody_carpet_length", 0);
      nlapiSetFieldValue("custbody_freight_weight", 0);
    } else {
      setTimeout(function () {
        var values = {};
        values["action"] = "get";
        values["user"] = nlapiGetUser();
        var lastItemCat = _scLib.getLastItemCat(values);
        if (!_tools.isEmpty(lastItemCat)) {
          nlapiSetFieldValue("custbody_item_category", lastItemCat, true);
        }
      }, 2000);
    }
  }

  var orderType = nlapiGetFieldValue("custbody_ng_cs_order_type");
  if (_tools.isInArray(orderType, _scLib.ShowMgmtOrderTypes)) {
    if (!_tools.isEmpty(_ShowTableID)) {
      var smpl = nlapiLookupField(
        "customrecord_show",
        _ShowTableID,
        "custrecord_show_mgmnt_price_lvl"
      );
      if (!_tools.isEmpty(smpl)) {
        nlapiSetFieldValue("custbody_price_level", smpl);
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @returns {Boolean} True to continue save, false to abort save
 */
function ng_clientSaveRecord_02() {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function ng_clientValidateField_02(type, name, linenum) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function ng_clientFieldChanged_02(type, name, linenum) {
  if (name == "custbody_item_category") {
    var itemCategory = nlapiGetFieldValue(name);
    if (!_tools.isEmpty(itemCategory)) {
      ngItemCategoryDisable(false);
      nlapiSetFieldValue("custbody_carpet_width", 0);
      nlapiSetFieldValue("custbody_carpet_length", 0);
      nlapiSetFieldValue("custbody_freight_weight", 0);

      if (_scLib.RetainLastItemCat) {
        try {
          var values = {};
          values["action"] = "set";
          values["user"] = nlapiGetUser();
          values["itemcat"] = itemCategory;
          _scLib.setLastItemCat(values);
        } catch (err) {}
      }
    } else {
      ngItemCategoryDisable(true);
    }
  }

  if (name == "custbody_item") {
    ngClearItemSelectionFields(true);
    var parentItem = nlapiGetFieldValue(name);
    if (!_tools.isEmpty(parentItem)) {
      ngEnableSelectionFields(parentItem);
      ngValidateMinQty(parentItem);
    } else {
      nlapiSetFieldValue("custbody_carpet_width", 0);
      nlapiSetFieldValue("custbody_carpet_length", 0);
      nlapiSetFieldValue("custbody_freight_weight", 0);
      var itemCategory = nlapiGetFieldValue("custbody_item_category");
      if (_tools.isEmpty(itemCategory)) {
        ngItemCategoryDisable(true);
      } else {
        ngItemCategoryDisable(false);
      }
    }
  }

  if (name == "custbody_quantity") {
    var parentItem = nlapiGetFieldValue("custbody_item");
    var currQty = new Number(nlapiGetFieldValue(name));
    if (!_tools.isEmpty(parentItem)) {
      ngValidateMinQty(parentItem, currQty);
    }
  }

  if (
    _tools.isInArray(name, ["custbody_carpet_width", "custbody_carpet_length"])
  ) {
    var parentItem = nlapiGetFieldValue("custbody_item");
    if (!_tools.isEmpty(parentItem)) {
      var sqftItem = _tools.isInArray(parentItem, _scLib.sqftItems);
      var sqdItem = _tools.isInArray(parentItem, _scLib.sqdItems);
      if (sqftItem || sqdItem) {
        var w = new Number(nlapiGetFieldValue("custbody_carpet_width"));
        var l = new Number(nlapiGetFieldValue("custbody_carpet_length"));
        var q = Math.ceil(w * l);
        nlapiSetFieldValue("custbody_quantity", q);
      }
    }
  }

  if (name == "custbody_ng_cs_order_type") {
    var orderType = nlapiGetFieldValue(name);
    if (_tools.isInArray(orderType, _scLib.ShowMgmtOrderTypes)) {
      if (!_tools.isEmpty(_ShowTableID)) {
        var smpl = nlapiLookupField(
          "customrecord_show",
          _ShowTableID,
          "custrecord_show_mgmnt_price_lvl"
        );
        if (!_tools.isEmpty(smpl)) {
          nlapiSetFieldValue("custbody_price_level", smpl);
        }
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @returns {Void}
 */
function ng_clientPostSourcing_02(type, name) {}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientLineInit_02(type) {}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to save line item, false to abort save
 */
function ng_clientValidateLine_02(type) {
  if (type == "item") {
    if (!addingToLine01 && !addingToLine02) {
      var item = nlapiGetCurrentLineItemValue("item", "item");
      if (!_tools.isEmpty(item)) {
        var qty = new Number(nlapiGetCurrentLineItemValue("item", "quantity"));
        if (!ValidateMaxQty(item, qty)) {
          return false;
        }
      }
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientRecalc_02(type) {}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item insert, false to abort insert
 */
function ng_clientValidateInsert_02(type) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item delete, false to abort delete
 */
function ng_clientValidateDelete_02(type) {
  return true;
}

function addItemToNewLine() {
  addingToLine02 = true;
  try {
    if (!_tools.isEmpty(nlapiGetCurrentLineItemValue("item", "item"))) {
      nlapiCommitLineItem("item");
    }
  } catch (err) {}

  if (_TriggerBO) {
    if (_tools.isEmpty(_ShowTableID) && !_NonBoothFormAL) {
      window.alert("Please select a show before adding items to the order.");
      addingToLine02 = false;
      return;
    }
    var booth = nlapiGetFieldValue("custbody_booth");
    if (_tools.isEmpty(booth) && !_NonBoothFormAL) {
      window.alert("Please select a booth before adding items to the order.");
      addingToLine02 = false;
      return;
    }
  }
  var exhibitor = nlapiGetFieldValue("entity");
  if (_tools.isEmpty(exhibitor)) {
    window.alert(
      "Please select an exhibitor before adding items to the order."
    );
    addingToLine02 = false;
    return;
  }

  var parentItem = nlapiGetFieldValue("custbody_item");
  var itemCategory = nlapiGetFieldValue("custbody_item_category");
  var color = nlapiGetFieldValue("custbody_color");
  var size = nlapiGetFieldValue("custbody_size");
  var orientation = nlapiGetFieldValue("custbody_variant");
  var cWidth = new Number(nlapiGetFieldValue("custbody_carpet_width"));
  var cLength = new Number(nlapiGetFieldValue("custbody_carpet_length"));
  var qty = new Number(nlapiGetFieldValue("custbody_quantity"));
  var fWeight = new Number(0);
  var freightWeight = new Number(nlapiGetFieldValue("custbody_freight_weight"));
  var origFreightWeight = freightWeight;
  var dayCalc = nlapiGetFieldValue("custbody_days");
  var substrate = nlapiGetFieldValue("custbody_substrate");
  var sqftItem = _tools.isInArray(parentItem, _scLib.sqftItems);
  var dItem = _tools.isInArray(parentItem, _scLib.daysItems);
  var durItem = _tools.isInArray(parentItem, _scLib.durationItems);
  var sqdItem = _tools.isInArray(parentItem, _scLib.sqdItems);
  var fItem = _tools.isInArray(parentItem, _scLib.freightItems);
  var cItem = _tools.isInArray(parentItem, _scLib.colorItems);
  var sItem = _tools.isInArray(parentItem, _scLib.sizeItems);
  var oItem = _tools.isInArray(parentItem, _scLib.orientationItems);
  var lItem = _tools.isInArray(parentItem, _scLib.laborItems);
  var gItem = _tools.isInArray(parentItem, _scLib.graphicsItems);
  var priceLevel = nlapiGetFieldValue("custbody_price_level");
  var matrixFilt = new Array();

  if (_tools.isEmpty(parentItem)) {
    window.alert("Please choose an item to add, first.");
    return;
  }
  if (
    !sqftItem &&
    !dItem &&
    !fItem &&
    !sqdItem &&
    !lItem &&
    !durItem &&
    (isNaN(new Number(qty)) || !(qty > 0))
  ) {
    window.alert("Please enter a quantity greater than zero.");
    return;
  }

  if (cItem) {
    if (_tools.isEmpty(color)) {
      window.alert("Please select a color.");
      return;
    } else {
      matrixFilt.push(
        new nlobjSearchFilter("custitem27", null, "is", color, null)
      );
    }
  }
  if (sItem) {
    if (_tools.isEmpty(size)) {
      window.alert("Please select a size.");
      return;
    } else {
      matrixFilt.push(
        new nlobjSearchFilter("custitem28", null, "is", size, null)
      );
    }
  }
  if (oItem) {
    if (_tools.isEmpty(orientation)) {
      window.alert("Please select an orientation option.");
      return;
    } else {
      matrixFilt.push(
        new nlobjSearchFilter(
          "custitem_orientation",
          null,
          "is",
          orientation,
          null
        )
      );
    }
  }
  if (gItem) {
    if (_tools.isEmpty(substrate)) {
      window.alert("Please select a graphic material option.");
      return;
    } else {
      matrixFilt.push(
        new nlobjSearchFilter("custitem42", null, "is", substrate, null)
      );
    }
  }

  if (fItem) {
    if (freightWeight <= 0) {
      window.alert("Please enter the freight weight.");
      addingToLine02 = false;
      return;
    } else {
      if (freightWeight < _scLib.FreightMinimum) {
        freightWeight = _scLib.FreightMinimum;
      }
      fWeight = Math.ceil(freightWeight / 100);
    }
  }

  if (sqdItem || sqftItem) {
    if (parseFloat(cWidth) <= 0 || _tools.isEmpty(cWidth) || isNaN(cWidth)) {
      window.alert("Please enter a value for width");
      addingToLine02 = false;
      return;
    }
    if (parseFloat(cLength) <= 0 || _tools.isEmpty(cLength) || isNaN(cLength)) {
      window.alert("Please enter a vlaue for length");
      addingToLine02 = false;
      return;
    }
  }
  if (sqdItem || dItem || durItem) {
    if (parseFloat(dayCalc) <= 0 || _tools.isEmpty(dayCalc) || isNaN(dayCalc)) {
      window.alert("Please enter a value for days");
      addingToLine02 = false;
      return;
    }
  }

  if (sqdItem && !durItem) {
    qty = Math.ceil(cWidth * cLength * dayCalc);
  } else if (sqftItem && itemCategory != _scLib.GraphicsItemCat) {
    qty = Math.ceil(cWidth * cLength);
  } else if (sqftItem && itemCategory == _scLib.GraphicsItemCat) {
    qty = Math.ceil((cWidth * cLength) / 144);
  } else if (sqftItem && !durItem) {
    qty = Math.ceil(cWidth * cLength);
  } else if (dItem && !durItem) {
    qty = dayCalc;
  } else if (durItem && (sqftItem || sqdItem)) {
    qty = Math.ceil(cWidth * cLength);
  }

  console.log(fItem);
  if (fItem) {
    var rate = null;

    var maxQty = new Number(
      nlapiLookupField("item", parentItem, "maximumquantity")
    );
    if (
      origFreightWeight < _scLib.FreightMinimum &&
      Math.ceil(_scLib.FreightMinimum / 100) > maxQty &&
      maxQty > 0
    ) {
      fWeight = Math.ceil(origFreightWeight / 100);
    } else if (origFreightWeight < _scLib.FreightMinimum) {
      fWeight = Math.ceil(_scLib.FreightMinimum / 100);
    }

    if (!ValidateMaxQty(parentItem, fWeight)) {
      return;
    }

    if (!_tools.isEmpty(_ShowTableID)) {
      var ftFilt = new Array(
        ["custrecord_show_freight", "anyof", [_ShowTableID]],
        "and",
        ["custrecord_freight_item", "anyof", [parentItem]]
      );
      var ftCols = new Array(
        new nlobjSearchColumn("custrecord_freight_rate", null, null),
        new nlobjSearchColumn("custrecord_pre_show_rate", null, null),
        new nlobjSearchColumn("custrecord_inbetween_rate", null, null),
        new nlobjSearchColumn("custrecord_on_site_rate", null, null)
      );
      var ftSearch = null;
      try {
        ftSearch = nlapiSearchRecord(
          "customrecord_freight_table",
          null,
          ftFilt,
          ftCols
        );
      } catch (err) {
        _log.logError(err, "Error encountered getting freight table data");
      }
      if (ftSearch != null) {
        var today = nlapiStringToDate(
          nlapiDateToString(new Date(), "date")
        ).getTime();
        var advDateT = null;
        try {
          advDateT = _SHOW_DATA["custrecord_adv_ord_date"];
        } catch (err) {}
        var advDateD = !_tools.isEmpty(advDateT)
          ? nlapiStringToDate(advDateT)
          : null;
        var advDate = !_tools.isEmpty(advDateD) ? advDateD.getTime() : null;
        var startDateT = _scLib.getShowDates(_ShowTableID, "min", false);
        var startDateD = !_tools.isEmpty(startDateT)
          ? nlapiStringToDate(startDateT)
          : null;
        var startDate = !_tools.isEmpty(startDateD)
          ? startDateD.getTime()
          : null;
        if (
          advDate != null &&
          today <= advDate &&
          !_tools.isEmpty(ftSearch[0].getValue("custrecord_advance_rate")) &&
          new Number(ftSearch[0].getValue("custrecord_advance_rate") || "0") > 0
        ) {
          priceLevel = "-1";
          rate = new Number(
            ftSearch[0].getValue("custrecord_advance_rate")
          ).toFixed(2);
        } else if (
          startDate != null &&
          today >= startDate &&
          !_tools.isEmpty(ftSearch[0].getValue("custrecord_on_site_rate")) &&
          new Number(ftSearch[0].getValue("custrecord_on_site_rate") || "0") > 0
        ) {
          priceLevel = "-1";
          rate = new Number(
            ftSearch[0].getValue("custrecord_on_site_rate")
          ).toFixed(2);
        } else if (
          !_tools.isEmpty(ftSearch[0].getValue("custrecord_inbetween_rate")) &&
          new Number(ftSearch[0].getValue("custrecord_inbetween_rate") || "0") >
            0
        ) {
          priceLevel = "-1";
          rate = new Number(
            ftSearch[0].getValue("custrecord_inbetween_rate")
          ).toFixed(2);
        } else if (
          !_tools.isEmpty(ftSearch[0].getValue("custrecord_freight_rate")) &&
          new Number(ftSearch[0].getValue("custrecord_freight_rate") || "0") > 0
        ) {
          priceLevel = "-1";
          rate = new Number(
            ftSearch[0].getValue("custrecord_freight_rate")
          ).toFixed(2);
        } else {
          priceLevel = nlapiGetFieldValue("custbody_price_level");
        }
      }
    }

    pushToLine({
      item: parentItem,
      qty: fWeight,
      pl: priceLevel,
      fitem: fItem,
      fweight: origFreightWeight,
      rate: rate /*, foItem : foItem*/,
    });
  } else if (
    (lItem || dItem || sqdItem) &&
    !durItem &&
    !((cItem || sItem || oItem) /*|| loItem*/) &&
    _DATE_DATA != null
  ) {
    BuildDaysCalcModal({
      item: parentItem,
      qty: qty,
      pl: priceLevel,
      sqftitem: sqftItem,
      length: cLength,
      width: cWidth,
      itemcat: itemCategory,
      ditem: dItem,
      sqditem: sqdItem,
      days: dayCalc,
      litem: lItem /*, foItem : foItem*/,
    });
  } else if (durItem && _DATE_DATA != null) {
    if (!ValidateMaxQty(parentItem, qty)) {
      return;
    }

    var data = new Array();
    for (var d = 0; d < _DATE_DATA.length; d++) {
      data.push({
        item: parentItem,
        qty: qty,
        pl: priceLevel,
        sqftitem: sqftItem,
        length: cLength,
        width: cWidth,
        itemcat: itemCategory,
        ditem: dItem,
        sqditem: sqdItem,
        days: dayCalc,
        date: _DATE_DATA[d].date,
        dur: durItem /*, foItem : foItem*/,
      });
    }
    pushToLine(data, 0);
  } else if (
    (sqdItem || sqftItem || dItem || lItem) &&
    !(cItem || sItem || oItem || gItem)
  ) {
    if (!ValidateMaxQty(parentItem, qty)) {
      return;
    }

    pushToLine({
      item: parentItem,
      qty: qty,
      pl: priceLevel,
      sqftitem: sqftItem,
      length: cLength,
      width: cWidth,
      itemcat: itemCategory,
      ditem: dItem,
      sqditem: sqdItem,
      days: dayCalc /*, foItem : foItem*/,
    });
  } else {
    if (parseFloat(qty) <= 0 || _tools.isEmpty(qty) || isNaN(qty)) {
      window.alert("Please enter a value for quantity");
      addingToLine02 = false;
      return;
    } else {
      if (cItem || sItem || oItem || gItem) {
        matrixFilt.push(
          new nlobjSearchFilter("parent", null, "is", parentItem, null)
        );
        var matrixSearch = null;
        try {
          matrixSearch = nlapiSearchRecord("item", null, matrixFilt, null);
        } catch (err) {
          _log.logError(
            err,
            "Error encountered searching for matrix child item"
          );
        }

        if (matrixSearch != null) {
          parentItem = matrixSearch[0].getId();
        } else {
          window.alert("Could not find matrix child item with these options");
          addingToLine02 = false;
          return;
        }
      }

      if ((dItem || sqdItem) && !durItem && _DATE_DATA != null) {
        BuildDaysCalcModal({
          item: parentItem,
          qty: qty,
          pl: priceLevel,
          fitem: fItem,
          fweight: origFreightWeight,
          sqftitem: sqftItem,
          length: cLength,
          width: cWidth,
          itemcat: itemCategory,
          ditem: dItem,
          sqditem: sqdItem,
          days: dayCalc,
          litem: lItem /*, foitem : foItem*/,
        });
      } else {
        if (!ValidateMaxQty(parentItem, qty)) {
          return;
        }

        pushToLine({
          item: parentItem,
          qty: qty,
          pl: priceLevel,
          fitem: fItem,
          fweight: origFreightWeight,
          sqftitem: sqftItem,
          length: cLength,
          width: cWidth,
          itemcat: itemCategory,
          ditem: dItem,
          sqditem: sqdItem,
          days: dayCalc,
          litem: lItem /*, foitem : foItem*/,
        });
      }
    }
  }
}

function ValidateMaxQty(item, qty) {
  if (_scLib.EnforceItemMaxQuantity) {
    var maxQty = new Number(nlapiLookupField("item", item, "maximumquantity"));
    if (maxQty > 0 && qty > maxQty) {
      window.alert(
        "You have exceeded this item's maximum ordering quantity: {0}".NG_Format(
          maxQty
        )
      );
      return false;
    }
  }
  return true;
}

function ngItemCategoryDisable(isSet) {
  nlapiDisableField("custbody_item", isSet);
  nlapiDisableField("custbody_price_level", true);
  nlapiDisableField("custbody_quantity", true);
  nlapiDisableField("custbody_size", true);
  nlapiDisableField("custbody_color", true);
  nlapiDisableField("custbody_variant", true);
  nlapiDisableField("custbody_carpet_width", true);
  nlapiDisableField("custbody_carpet_length", true);
  nlapiDisableField("custbody_days", true);
  nlapiDisableField("custbody_freight_weight", true);
  nlapiDisableField("custbody_substrate", true);
}

function ngItemDisable(isSet) {
  nlapiDisableField("custbody_price_level", isSet);
  nlapiDisableField("custbody_size", isSet);
  nlapiDisableField("custbody_quantity", isSet);
  nlapiDisableField("custbody_color", isSet);
  nlapiDisableField("custbody_variant", isSet);
  nlapiDisableField("custbody_carpet_width", isSet);
  nlapiDisableField("custbody_carpet_length", isSet);
  nlapiDisableField("custbody_days", isSet);
  nlapiDisableField("custbody_freight_weight", isSet);
  nlapiDisableField("custbody_substrate", isSet);
}

function ngClearItemSelectionFields(bool) {
  if (!bool) {
    nlapiSetFieldValue("custbody_item_category", "");
  }

  nlapiSetFieldValue("custbody_quantity", 0, false);
  nlapiSetFieldValue("custbody_carpet_width", 0, false);
  nlapiSetFieldValue("custbody_carpet_length", 0, false);
  nlapiSetFieldValue("custbody_days", 0);
  nlapiSetFieldValue("custbody_freight_weight", 0);
  nlapiSetFieldValue("custbody_timeframe", "");
  _DATE_DATA = null;
}

function ngEnableSelectionFields(parentItem) {
  var sqftItem = _tools.isInArray(parentItem, _scLib.sqftItems);
  var dItem = _tools.isInArray(parentItem, _scLib.daysItems);
  var durItem = _tools.isInArray(parentItem, _scLib.durationItems);
  var sqdItem = _tools.isInArray(parentItem, _scLib.sqdItems);
  var fItem = _tools.isInArray(parentItem, _scLib.freightItems);
  var cItem = _tools.isInArray(parentItem, _scLib.colorItems);
  var sItem = _tools.isInArray(parentItem, _scLib.sizeItems);
  var oItem = _tools.isInArray(parentItem, _scLib.orientationItems);
  var lItem = _tools.isInArray(parentItem, _scLib.laborItems);
  var gItem = _tools.isInArray(parentItem, _scLib.graphicsItems);

  ngItemDisable(true);
  nlapiDisableField("custbody_price_level", false);

  if (fItem) {
    nlapiDisableField("custbody_freight_weight", false);
  } else if (sqdItem) {
    nlapiDisableField("custbody_carpet_width", false);
    nlapiDisableField("custbody_carpet_length", false);
    ngGetShowDays(_scLib.DCalcDateTypes);
    getBoothDims();
  } else if (sqftItem) {
    nlapiDisableField("custbody_carpet_width", false);
    nlapiDisableField("custbody_carpet_length", false);
    getBoothDims();
    if (durItem) {
      ngGetShowDays([_scLib.DefaultShowDateType]);
    }
  } else if (dItem || durItem) {
    nlapiDisableField("custbody_quantity", false);
    nlapiSetFieldValue("custbody_quantity", 1, false);
    if (dItem) {
      ngGetShowDays(_scLib.DCalcDateTypes);
    } else {
      ngGetShowDays([_scLib.DefaultShowDateType]);
    }
  } else {
    nlapiDisableField("custbody_quantity", false);
    nlapiSetFieldValue("custbody_quantity", 1, false);
  }

  if (cItem) {
    nlapiDisableField("custbody_color", false);
  }
  if (sItem) {
    nlapiDisableField("custbody_size", false);
  }
  if (oItem) {
    nlapiDisableField("custbody_variant", false);
  }
  if (lItem) {
    nlapiDisableField("custbody_days", false);
    console.log("getting date data");
    ngGetShowDays(_scLib.LaborDateTypes);
  }
  if (gItem) {
    nlapiDisableField("custbody_substrate", false);
  }
}

function getBoothDims() {
  var boothID = nlapiGetFieldValue("custbody_booth");
  if (!_tools.isEmpty(boothID)) {
    var boothData = nlapiLookupField("customrecord_show_booths", boothID, [
      "custrecord_booth_length",
      "custrecord_booth_width",
    ]);
    nlapiSetFieldValue(
      "custbody_carpet_length",
      new Number(boothData["custrecord_booth_length"])
    );
    nlapiSetFieldValue(
      "custbody_carpet_width",
      new Number(boothData["custrecord_booth_width"])
    );
  } else {
    nlapiSetFieldValue("custbody_carpet_length", 0);
    nlapiSetFieldValue("custbody_carpet_width", 0);
  }
}

function ngValidateMinQty(parentItem, currQty) {
  currQty = currQty || new Number(0);
  var minQ = new Number(
    nlapiLookupField("item", parentItem, "minimumquantity")
  );
  var booth = nlapiGetFieldValue("custbody_booth");
  if (isNaN(minQ)) {
    minQ = new Number(0);
  }
  if (minQ > 1 && currQty < minQ) {
    nlapiSetFieldValue("custbody_quantity", minQ, false);
  } else if (
    (_tools.isInArray(parentItem, _scLib.sqftItems) ||
      _tools.isInArray(parentItem, _scLib.sqdItems)) &&
    !_tools.isEmpty(booth)
  ) {
    var boothDims = nlapiLookupField("customrecord_show_booths", booth, [
      "custrecord_booth_length",
      "custrecord_booth_width",
    ]);
    var boothL = new Number(boothDims.custrecord_booth_length);
    var boothW = new Number(boothDims.custrecord_booth_length);
    if (isNaN(boothL) || boothL < 10) {
      boothL = new Number(10);
    }
    if (isNaN(boothW) || boothW < 10) {
      boothW = new Number(10);
    }
    minQ = boothL * boothW;
    if (currQty < minQ) nlapiSetFieldValue("custbody_quantity", minQ, false);
  }
}

function ngGetShowDays(type) {
  _DATE_DATA = _scLib.getShowDates(_ShowTableID, null, true, type || null);
  nlapiSetFieldValue(
    "custbody_days",
    !_tools.isEmpty(_DATE_DATA) ? _DATE_DATA.length : 0,
    false
  );
}

function pushToLine(data, pos) {
  if (_tools.isEmpty(pos)) {
    console.log(data);
    nlapiSelectNewLineItem("item");
    nlapiSetCurrentLineItemValue("item", "item", data.item, true, true);

    setTimeout(function () {
      var itemDescr = nlapiGetCurrentLineItemValue("item", "description");
      var adtlDescr = "";
      nlapiSetCurrentLineItemValue("item", "quantity", data.qty, true);
      if (!_tools.isEmpty(data.pl)) {
        nlapiSetCurrentLineItemValue("item", "price", data.pl, true);
      }
      if (data.fitem) {
        if (data.pl == "-1") {
          nlapiSetCurrentLineItemValue("item", "rate", data.rate, true);
        }
      }
      if (data.fitem || data.foitem) {
        itemDescr = "{0}{1}LB".NG_Format(
          !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
          data.fweight
        );
        console.log("updating item description with freight data", itemDescr);
      }
      if ((data.ditem || data.sqditem) && !data.dur) {
        itemDescr = "{0}{1} Days".NG_Format(
          !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
          data.days
        );

        if (data.sqditem && !_tools.isEmpty(data.date)) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !_tools.isEmpty(adtlDescr) ? "\n" : "",
            data.width,
            data.length
          );
          itemDescr = "{0}Service Date: {1}".NG_Format(
            !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data.date
          );
          nlapiSetCurrentLineItemValue(
            "item",
            "custcol_labor_date",
            data.date,
            true
          );
        } else if (data.sqditem) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !_tools.isEmpty(adtlDescr) ? "\n" : "",
            data.width,
            data.length
          );
        } else if (!_tools.isEmpty(data.date)) {
          itemDescr = "{0}Service Date: {1}".NG_Format(
            !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data.date
          );
          nlapiSetCurrentLineItemValue(
            "item",
            "custcol_labor_date",
            data.date,
            true
          );
        }
      }
      if (data.dur && !_tools.isEmpty(data.date)) {
        itemDescr = "{0}Service Date: {1}".NG_Format(
          !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
          data.date
        );
        nlapiSetCurrentLineItemValue(
          "item",
          "custcol_labor_date",
          data.date,
          true
        );
        if (data.sqditem) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !_tools.isEmpty(adtlDescr) ? "\n" : "",
            data.width,
            data.length
          );
        }
      }
      if (
        data.sqftitem &&
        !data.sqditem &&
        data.itemcat != _scLib.GraphicsItemCat
      ) {
        adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
          !_tools.isEmpty(adtlDescr) ? "\n" : "",
          data.width,
          data.length
        );
      } else if (
        data.sqftitem &&
        !data.sqditem &&
        data.itemcat == _scLib.GraphicsItemCat
      ) {
        adtlDescr += '{0}Size: {1}" X {2}"'.NG_Format(
          !_tools.isEmpty(adtlDescr) ? "\n" : "",
          data.width,
          data.length
        );
      }
      nlapiSetCurrentLineItemValue("item", "description", itemDescr, true);
      console.log("final item description", itemDescr);
      nlapiSetCurrentLineItemValue(
        "item",
        "custcol_custom_carpet_size",
        adtlDescr,
        true
      );
      setTimeout(function () {
        nlapiCommitLineItem("item");
        addingToLine02 = false;

        setTimeout(function () {
          var itemField = document.getElementById("custbody_item_display");
          if (itemField != null) {
            if (isNaN(itemField.length)) {
              itemField.focus();
            } else {
              if (itemField.length > 0) {
                itemField[0].focus();
              }
            }
          }
        }, 500);
      }, 500);
    }, 500);

    nlapiSetFieldValue("custbody_item", "");
  } else {
    nlapiSelectNewLineItem("item");
    nlapiSetCurrentLineItemValue("item", "item", data[pos].item, true, true);

    setTimeout(function () {
      var itemDescr = nlapiGetCurrentLineItemValue("item", "description");
      var adtlDescr = "";
      nlapiSetCurrentLineItemValue("item", "quantity", data[pos].qty, true);
      if (!_tools.isEmpty(data[pos].pl)) {
        nlapiSetCurrentLineItemValue("item", "price", data[pos].pl, true);
      }
      if (data[pos].fitem) {
        if (data[pos].pl == "-1") {
          nlapiSetCurrentLineItemValue("item", "rate", data[pos].rate, true);
        }
      }
      if (data[pos].fitem) {
        itemDescr = "{0}{1}LB".NG_Format(
          !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
          data[pos].fweight
        );
      }
      if ((data[pos].ditem || data[pos].sqditem) && !data[pos].dur) {
        itemDescr = "{0}{1} Days".NG_Format(
          !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
          data[pos].days
        );

        if (data[pos].sqditem && !_tools.isEmpty(data[pos].date)) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !_tools.isEmpty(adtlDescr) ? "\n" : "",
            data[pos].width,
            data[pos].length
          );
          itemDescr = "{0}Service Date: {1}".NG_Format(
            !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data[pos].date
          );
          nlapiSetCurrentLineItemValue(
            "item",
            "custcol_labor_date",
            data[pos].date,
            true
          );
        } else if (data[pos].sqditem) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !_tools.isEmpty(adtlDescr) ? "\n" : "",
            data[pos].width,
            data[pos].length
          );
        } else if (!_tools.isEmpty(data[pos].date)) {
          itemDescr = "{0}Service Date: {1}".NG_Format(
            !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data[pos].date
          );
          nlapiSetCurrentLineItemValue(
            "item",
            "custcol_labor_date",
            data[pos].date,
            true
          );
        }
      }
      if (data[pos].dur && !_tools.isEmpty(data[pos].date)) {
        itemDescr = "{0}Service Date: {1}".NG_Format(
          !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
          data[pos].date
        );
        nlapiSetCurrentLineItemValue(
          "item",
          "custcol_labor_date",
          data[pos].date,
          true
        );
        if (data[pos].sqditem) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !_tools.isEmpty(adtlDescr) ? "\n" : "",
            data[pos].width,
            data[pos].length
          );
        }
      }
      if (
        data[pos].sqftitem &&
        !data[pos].sqditem &&
        data[pos].itemcat != _scLib.GraphicsItemCat
      ) {
        adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
          !_tools.isEmpty(adtlDescr) ? "\n" : "",
          data[pos].width,
          data[pos].length
        );
      } else if (
        data[pos].sqftitem &&
        !data[pos].sqditem &&
        data[pos].itemcat == _scLib.GraphicsItemCat
      ) {
        adtlDescr += '{0}Size: {1}" X {2}"'.NG_Format(
          !_tools.isEmpty(adtlDescr) ? "\n" : "",
          data[pos].width,
          data[pos].length
        );
      }
      nlapiSetCurrentLineItemValue("item", "description", itemDescr, true);
      nlapiSetCurrentLineItemValue(
        "item",
        "custcol_custom_carpet_size",
        adtlDescr,
        true
      );
      setTimeout(function () {
        nlapiCommitLineItem("item");
        if (Math.round(pos + 1) < data.length) {
          pushToLine(data, Math.round(pos + 1));
        } else {
          addingToLine02 = false;

          setTimeout(function () {
            var itemField = document.getElementById("custbody_item_display");
            if (itemField != null) {
              if (isNaN(itemField.length)) {
                itemField.focus();
              } else {
                if (itemField.length > 0) {
                  itemField[0].focus();
                }
              }
            }
          }, 500);
        }
      }, 500);
    }, 500);

    if (pos == 0) {
      nlapiSetFieldValue("custbody_item", "");
    }
  }
}

function BuildDaysCalcModal(data) {
  if (nlapiGetContext().getExecutionContext() != "userinterface") {
    return;
  }
  var calcTable = document.getElementById("selector_table");
  if (calcTable != null) {
    _MODAL_ITEM = data.item;
    var dates = new Array();
    if (_tools.isEmpty(_SHOW_DATA)) {
      getShowData();
    }
    if (!_tools.isEmpty(_ShowTableID)) {
      if (data.litem) {
        dates = _scLib.getShowDates(
          _ShowTableID,
          null,
          false,
          _scLib.LaborDateTypes
        );
      } else {
        dates = _scLib.getShowDates(
          _ShowTableID,
          null,
          false,
          _scLib.DCalcDateTypes
        );
      }
    }

    try {
      var s = nlapiSearchRecord("item", null, [
        "internalid",
        "anyof",
        data.item,
      ]);
      var r = nlapiLoadRecord(s[0].getRecordType(), data.item);
      var optCols = _tools.getMultiSelect("itemoptions", r, false, null);
      var hasSup = false;
      if (!_tools.isEmpty(optCols) && optCols.length > 0) {
        for (var o = 0; o < optCols.length; o++) {
          if (optCols[o] == "CUSTCOL_LABOR_DATE") {
            _CALC_WORKER_COUNT = true;
          } else if (optCols[o] == "CUSTCOL_LABOR_SUP_REQUIRED") {
            hasSup = true;
          }
        }
      }
      data["supitem"] = hasSup;
    } catch (err) {
      _log.logError(
        err,
        "Error encountered checking if item calcs for workers"
      );
      console.log(err, "Error encountered checking if item calcs for workers");
    }

    /////////////////////
    // build table header
    /////////////////////
    var hr = document.createElement("tr");
    hr.id = "daysitemrow_0";

    var th1 = document.createElement("th");
    th1.style.cssText = "text-align:center; width: 2%;";
    var tp1 = document.createElement("p");
    tp1.style.cssText = "width: 100%; font-weight: bold;";
    tp1.innerHTML = "Day";
    th1.appendChild(tp1);
    hr.appendChild(th1);

    var th0 = document.createElement("th");
    th0.style.cssText = "text-align:center; width: 2%;";
    var tp0 = document.createElement("p");
    tp0.style.cssText = "width: 100%; font-weight: bold;";
    tp0.innerHTML = "Add";
    th0.appendChild(tp0);
    hr.appendChild(th0);

    var th2 = document.createElement("th");
    th2.style.cssText = "text-align:center; width: 2%;";
    var tp2 = document.createElement("p");
    tp2.style.cssText = "width: 100%; font-weight: bold;";
    tp2.innerHTML = "Service Date";
    th2.appendChild(tp2);
    hr.appendChild(th2);

    var th3 = document.createElement("th");
    th3.style.cssText = "text-align:center; width: 2%;";
    var tp3 = document.createElement("p");
    tp3.style.cssText = "width: 100%; font-weight: bold;";
    tp3.innerHTML = "Start Time";
    th3.appendChild(tp3);
    hr.appendChild(th3);

    var th4 = document.createElement("th");
    th4.style.cssText = "text-align:center; width: 2%;";
    var tp4 = document.createElement("p");
    tp4.style.cssText = "width: 100%; font-weight: bold;";
    tp4.innerHTML = "End Time";
    th4.appendChild(tp4);
    hr.appendChild(th4);

    if (_CALC_WORKER_COUNT && data.litem) {
      var th7 = document.createElement("th");
      th7.style.cssText = "text-align:center; width: 2%;";
      var tp7 = document.createElement("p");
      tp7.style.cssText = "width: 100%; font-weight: bold;";
      tp7.innerHTML = "Number of<br />Workers";
      th7.appendChild(tp7);
      hr.appendChild(th7);
    }

    var th5 = document.createElement("th");
    th5.style.cssText = "text-align:center; width: 2%;";
    var tp5 = document.createElement("p");
    tp5.style.cssText = "width: 100%; font-weight: bold;";
    tp5.innerHTML = "Duration";
    th5.appendChild(tp5);
    hr.appendChild(th5);

    var th6 = document.createElement("th");
    th6.style.cssText = "text-align:center; width: 2%;";
    var tp6 = document.createElement("p");
    tp6.style.cssText = "width: 100%; font-weight: bold;";
    tp6.innerHTML = "Supervision<br />Required?";
    th6.appendChild(tp6);
    hr.appendChild(th6);

    calcTable.appendChild(hr);
    /////////////////////
    // table header done
    /////////////////////

    for (var d = 0; d < dates.length; d++) {
      var l = Math.round(d + 1).toFixed(0);
      var tr = document.createElement("tr");
      tr.id = "daysitemrow_" + l;

      var td1 = document.createElement("td");
      td1.style.cssText = "text-align:center;";
      var p1 = document.createElement("p");
      p1.style.cssText = "width: 100%;";
      p1.innerHTML = l;
      td1.appendChild(p1);
      tr.appendChild(td1);

      var td0 = document.createElement("td");
      td0.style.cssText = "text-align:center; vertical-align: middle;";
      var selectLineInput = document.createElement("input");
      selectLineInput.type = "checkbox";
      selectLineInput.className = "daysitemselect";
      selectLineInput.name = "daysitemselect_" + l;
      selectLineInput.id = "daysitemselect_" + l;
      selectLineInput.checked = true;
      td0.appendChild(selectLineInput);
      tr.appendChild(td0);

      var td2 = document.createElement("td");
      var dateInput = document.createElement("input");
      dateInput.type = "text";
      dateInput.className = "daysitemdate";
      dateInput.name = "daysitemdate_" + l;
      dateInput.id = "daysitemdate_" + l;
      if (dates != null) {
        dateInput.value = dates[d].date;
      }
      td2.appendChild(dateInput);
      tr.appendChild(td2);

      var td3 = document.createElement("td");
      td3.style.cssText = "text-align:center;";
      if (data.litem) {
        var startTimeInput = document.createElement("input");
        startTimeInput.type = "text";
        startTimeInput.className = "daysitemtimestart";
        startTimeInput.name = "daysitemtimestart_" + l;
        startTimeInput.id = "daysitemtimestart_" + l;
        startTimeInput.addEventListener("change", onTimeChange);
        td3.appendChild(startTimeInput);
      } else {
        var p3 = document.createElement("p");
        p3.style.cssText = "width: 100%;";
        p3.innerHTML = "&nbsp;";
        td3.appendChild(p3);
      }
      tr.appendChild(td3);

      var td4 = document.createElement("td");
      td4.style.cssText = "text-align:center;";
      if (data.litem) {
        var endTimeInput = document.createElement("input");
        endTimeInput.type = "text";
        endTimeInput.className = "daysitemtimeend";
        endTimeInput.name = "daysitemtimeend_" + l;
        endTimeInput.id = "daysitemtimeend_" + l;
        endTimeInput.addEventListener("change", onTimeChange);
        td4.appendChild(endTimeInput);
      } else {
        var p4 = document.createElement("p");
        p4.style.cssText = "width: 100%;";
        p4.innerHTML = "&nbsp;";
        td4.appendChild(p4);
      }
      tr.appendChild(td4);

      if (_CALC_WORKER_COUNT && data.litem) {
        var td7 = document.createElement("td");
        td7.style.cssText = "text-align:center;";
        var workNumInput = document.createElement("input");
        workNumInput.type = "text";
        workNumInput.className = "workercount";
        workNumInput.name = "workercount_" + l;
        workNumInput.id = "workercount_" + l;
        workNumInput.value = 1;
        td7.appendChild(workNumInput);
        tr.appendChild(td7);
      }

      var td5 = document.createElement("td");
      td5.style.cssText = "text-align:center;";
      var p5 = document.createElement("p");
      p5.style.cssText = "width: 100%;";
      p5.className = "daysitemduration";
      p5.id = "daysitemduration_" + l;
      p5.innerHTML = "&nbsp;";
      td5.appendChild(p5);
      tr.appendChild(td5);

      var td6 = document.createElement("td");
      td6.style.cssText = "text-align:center; vertical-align: middle;";
      if (data.supitem) {
        var supervisorInput = document.createElement("input");
        supervisorInput.type = "checkbox";
        supervisorInput.className = "daysitemsupervisor";
        supervisorInput.name = "daysitemsupervisor_" + l;
        supervisorInput.id = "daysitemsupervisor_" + l;
        td6.appendChild(supervisorInput);
      } else {
        var p6 = document.createElement("p");
        p6.style.cssText = "width: 100%;";
        p6.innerHTML = "&nbsp;";
        td6.appendChild(p6);
      }
      tr.appendChild(td6);

      calcTable.appendChild(tr);
    }

    var overlay = document.getElementById("ng_cs_calc_popup");
    if (overlay != null) {
      overlay.className = "ng_cs_overlay ng_cs_overlay_a";
    }
  }
}

function cancelDaysCalc() {
  _MODAL_ITEM = null;
  closeDaysCalc();
}

function processDaysCalc() {
  var parentItem = _MODAL_ITEM;
  _MODAL_ITEM = null;
  var length = new Number(nlapiGetFieldValue("custbody_carpet_length"));
  var width = new Number(nlapiGetFieldValue("custbody_carpet_width"));
  try {
    var pl = nlapiGetFieldValue("custbody_price_level") || "1";
    var itemRate = new Number(0);
    try {
      if (pl == "1") {
        itemRate = new Number(
          nlapiLookupField("item", parentItem, "baseprice")
        );
      } else {
        var ratesSearch = nlapiSearchRecord(
          "item",
          null,
          ["internalid", "is", parentItem],
          [new nlobjSearchColumn("otherprices")]
        );
        if (ratesSearch != null) {
          itemRate = new Number(
            ratesSearch[0].getValue("price{0}".NG_Format(pl))
          );
          if (isNaN(itemRate) || itemRate == 0) {
            itemRate = new Number(
              nlapiLookupField("item", parentItem, "baseprice")
            );
          }
        }
      }
    } catch (err) {}
    var calcTable = document.getElementById("selector_table");
    if (calcTable != null) {
      var lineNodes = calcTable.childNodes;
      if (lineNodes.length > 1) {
        var lineData = new Array();
        for (var d = 1; d < lineNodes.length; d++) {
          var l = Math.round(d).toFixed(0);
          if (
            document.getElementById("daysitemselect_" + l) != null &&
            document.getElementById("daysitemselect_" + l).checked
          ) {
            var dateID = "daysitemdate_{0}".NG_Format(l);
            var startID = "daysitemtimestart_{0}".NG_Format(l);
            var endID = "daysitemtimeend_{0}".NG_Format(l);
            var supervisorID = "daysitemsupervisor_{0}".NG_Format(l);
            var workCntID = "workercount_{0}".NG_Format(l);
            var dateInit = null;
            var startInit = null;
            var endInit = null;
            var workCnt = new Number(1);
            var supervisorInit = null;
            var hasSupervisor = false;
            if (document.getElementById(dateID) != null) {
              dateInit = document.getElementById(dateID).value;
              try {
                dateInit = nlapiDateToString(
                  nlapiStringToDate(dateInit),
                  "date"
                );
              } catch (err) {
                window.alert(
                  "An invalid date value has been entered on line {0}. Please enter dates in the format m/d/yyyy.".NG_Format(
                    l
                  )
                );
                setFieldFocus(dateID);
                return;
              }
            }
            if (document.getElementById(startID) != null) {
              startInit = document.getElementById(startID).value;
              if (
                _tools.isInArray(parentItem, _scLib.laborItems) &&
                _tools.isEmpty(startInit)
              ) {
                window.alert(
                  "Start time entry on line {0} is blank. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                    l
                  )
                );
                setFieldFocus(startID);
                return;
              }
              if (!_TIME_REG.test(startInit)) {
                window.alert(
                  "Start time entry on line {0} is invalid. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                    l
                  )
                );
                setFieldFocus(startID);
                return;
              }
              if (!_tools.isEmpty(startInit)) {
                startInit = startInit.toLowerCase();
                if (startInit.match(/am/) && !startInit.match(/ am/)) {
                  startInit = startInit.replace("am", " am");
                }
                if (startInit.match(/pm/) && !startInit.match(/ pm/)) {
                  startInit = startInit.replace("pm", " pm");
                }
              }
            }
            if (document.getElementById(endID) != null) {
              endInit = document.getElementById(endID).value;
              if (
                _tools.isInArray(parentItem, _scLib.laborItems) &&
                _tools.isEmpty(endInit)
              ) {
                window.alert(
                  "End time entry on line {0} is blank. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                    l
                  )
                );
                setFieldFocus(endID);
                return;
              }
              if (!_TIME_REG.test(endInit)) {
                window.alert(
                  "End time entry on line {0} is invalid. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                    l
                  )
                );
                setFieldFocus(endID);
                return;
              }
              if (!_tools.isEmpty(endInit)) {
                endInit = endInit.toLowerCase();
                if (endInit.match(/am/) && !endInit.match(/ am/)) {
                  endInit = endInit.replace("am", " am");
                }
                if (endInit.match(/pm/) && !endInit.match(/ pm/)) {
                  endInit = endInit.replace("pm", " pm");
                }
              }
            }
            if (document.getElementById(supervisorID) != null) {
              hasSupervisor = true;
              supervisorInit = document.getElementById(supervisorID).checked;
            }
            if (
              _CALC_WORKER_COUNT &&
              _tools.isInArray(parentItem, _scLib.laborItems)
            ) {
              if (document.getElementById(workCntID) != null) {
                var workCntInit = document.getElementById(workCntID).value;

                if (_tools.isEmpty(workCntInit)) {
                  window.alert(
                    "You must enter the number of workers required on line {0}.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(workCntID);
                  return;
                }

                workCnt = new Number(workCntInit);
                if (isNaN(workCnt) || workCnt < 1) {
                  window.alert(
                    "The number of workers requested on line {0} is invalid. Please verify your entry.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(workCntID);
                  return;
                }
              }
            }

            if (!_tools.isEmpty(dateInit)) {
              var data = {
                itemid: parentItem,
                cols: new Array(),
              };
              var date = nlapiStringToDate(dateInit);
              var lText = "";
              var sRate = new Number(0);
              if (_tools.isInArray(parentItem, _scLib.laborItems)) {
                var lr = processLaborRates(
                  date,
                  startInit,
                  endInit,
                  itemRate,
                  workCnt
                );
                if (lr == null) {
                  return;
                }
                data["qty"] = lr["q"];
                data["pl"] = "-1";
                data["rate"] = lr["r"];
                data["amount"] = _M.roundToHundredths(lr["q"] * lr["r"]);
                lText = lr["t"];
                if (!_tools.isEmpty(lText)) {
                  lText += "\nSupervision Required: {0}".NG_Format(
                    supervisorInit ? "Yes" : "No"
                  );
                }
                sRate = lr["s"];
              } else {
                var q = new Number(1);
                var sqft = new Number(1);
                if (
                  _tools.isInArray(parentItem, _scLib.sqftItems) ||
                  _tools.isInArray(parentItem, _scLib.sqdItems)
                ) {
                  if (!_tools.isEmpty(width) && !_tools.isEmpty(length)) {
                    try {
                      sqft = _M.roundToHundredths(width * length);
                    } catch (e) {}
                  }
                  if (isNaN(sqft)) {
                    sqft = new Number(1);
                  }
                } else {
                  q = new Number(nlapiGetFieldValue("custbody_quantity"));
                  if (isNaN(q) || q == 0) {
                    q = new Number(1);
                  }
                }
                var qty = _M.roundToHundredths(sqft * q);
                data["qty"] = qty;
                data["pl"] = pl;
                if (hasSupervisor) {
                  sRate = _M.roundToHundredths(itemRate * qty);
                }
              }

              var itemDescr = "";
              data.cols.push({
                field: "custcol_labor_date",
                value: nlapiDateToString(date, "date"),
              });
              data.cols.push({
                field: "custcol_labor_sup_required",
                value: supervisorInit ? "T" : "F",
              });
              if (
                !_tools.isEmpty(width) &&
                !_tools.isEmpty(length) &&
                width != 0 &&
                length != 0
              ) {
                data.cols.push({
                  field: "custcol_custom_carpet_size",
                  value: "Size: {0}' X {1}'".NG_Format(length, width),
                });
              }
              if (!_tools.isEmpty(startInit)) {
                if (_tools.isEmpty(lText))
                  itemDescr = "{0}Start Time: {1}".NG_Format(
                    !_tools.isEmpty(itemDescr)
                      ? "{0}\n".NG_Format(itemDescr)
                      : "",
                    startInit
                  );
                data.cols.push({
                  field: "custcol_labor_time",
                  value: startInit,
                });
              }
              if (!_tools.isEmpty(endInit)) {
                if (_tools.isEmpty(lText))
                  itemDescr = "{0}End Time: {1}".NG_Format(
                    !_tools.isEmpty(itemDescr)
                      ? "{0}\n".NG_Format(itemDescr)
                      : "",
                    endInit
                  );
                data.cols.push({
                  field: "custcol_labor_end_time",
                  value: endInit,
                });
              }
              itemDescr = "{0}{1}".NG_Format(
                !_tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
                lText
              );
              if (!_tools.isEmpty(date) && itemDescr.search("Service") < 0) {
                itemDescr = "{0}Service Date: {1}".NG_Format(
                  !_tools.isEmpty(itemDescr)
                    ? "{0}\n".NG_Format(itemDescr)
                    : "",
                  nlapiDateToString(date, "date")
                );
              }
              if (
                !_tools.isInArray(parentItem, _scLib.laborItems) &&
                hasSupervisor
              ) {
                itemDescr = "{0}Supervision Required: {1}".NG_Format(
                  !_tools.isEmpty(itemDescr)
                    ? "{0}\n".NG_Format(itemDescr)
                    : "",
                  supervisorInit ? "Yes" : "No"
                );
              }
              data.cols.push({ field: "description", value: itemDescr });
              lineData.push(data);
              if (_CALC_WORKER_COUNT) {
                data.cols.push({
                  field: "custcol_labor_workers",
                  value: Math.round(workCnt).toFixed(0),
                });
              }
              if (
                supervisorInit &&
                sRate > 0 &&
                !_tools.isEmpty(_scLib.SupervisorItem)
              ) {
                var sup = {
                  itemid: _scLib.SupervisorItem,
                  qty: 1,
                  pl: "-1",
                  rate: sRate,
                  amount: sRate,
                  cols: new Array(),
                };
                if (!_tools.isEmpty(startInit) && !_tools.isEmpty(endInit)) {
                  sup.cols.push({
                    field: "description",
                    value: "For '{0}' on date {1} from {2} to {3}".NG_Format(
                      nlapiLookupField("item", parentItem, "itemid"),
                      nlapiDateToString(date, "date"),
                      startInit,
                      endInit
                    ),
                  });
                  sup.cols.push(
                    {
                      field: "custcol_labor_date",
                      value: nlapiDateToString(date, "date"),
                    },
                    { field: "custcol_labor_time", value: startInit },
                    { field: "custcol_labor_end_time", value: endInit },
                    { field: "custcol_ng_cs_labor_item", value: parentItem }
                  );
                } else {
                  sup.cols.push({
                    field: "description",
                    value: "For '{0}' on date {1}".NG_Format(
                      nlapiLookupField("item", parentItem, "itemid"),
                      nlapiDateToString(date, "date")
                    ),
                  });
                }
                lineData.push(sup);
              }
            }
          }
        }

        if (lineData.length > 0) {
          pushDaysCalcToLine(lineData, 0);
        } else {
          if (
            window.confirm(
              "There are no dates selected for adding to the order. Is this correct?"
            )
          ) {
            console.log("no dates selected");
            closeDaysCalc();
            nlapiSetFieldValue("custbody_item", "");
          } else {
            return;
          }
        }
      } else {
        console.log("selector table has no lines");
        closeDaysCalc();
        nlapiSetFieldValue("custbody_item", "");
        return;
      }
    } else {
      console.log("selector table not found");
      closeDaysCalc();
      nlapiSetFieldValue("custbody_item", "");
      return;
    }
  } catch (err) {
    console.log(err);
    window.alert("There was a problem processing your request");
  }
}

function closeDaysCalc() {
  var overlay = document.getElementById("ng_cs_calc_popup");
  if (overlay != null) {
    overlay.className = "ng_cs_overlay ng_cs_overlay_b";
  }
  setTimeout(function () {
    var calcTable = document.getElementById("selector_table");
    if (calcTable != null) {
      while (calcTable.firstChild) calcTable.removeChild(calcTable.firstChild);
    }
  }, 1000);
  _CALC_WORKER_COUNT = false;
}

function processLaborRates(dateObj, start, end, itemRate, workerCount) {
  workerCount = workerCount || 1;
  var workerText = _CALC_WORKER_COUNT
    ? "\nWorkers Requested: {0}".NG_Format(workerCount.toFixed(0))
    : "";
  var date = nlapiDateToString(dateObj, "date");
  var st = nlapiStringToDate("{0} {1}".NG_Format(date, start)).getTime();
  var et = nlapiStringToDate("{0} {1}".NG_Format(date, end)).getTime();
  var filt = new Array(
    ["custrecord_ng_cs_labor_show", "anyof", [_ShowTableID]],
    "and",
    ["custrecord_ng_cs_labor_date", "on", date],
    "and",
    ["isinactive", "is", "F"]
  );
  var cols = new Array(
    new nlobjSearchColumn("custrecord_ng_cs_labor_start", null, null),
    new nlobjSearchColumn("custrecord_ng_cs_labor_end", null, null),
    new nlobjSearchColumn("custrecord_ng_cs_labor_type", null, null),
    new nlobjSearchColumn("custrecord_ng_cs_labor_multiplier", null, null),
    new nlobjSearchColumn("custrecord_ng_cs_supervisor_markup", null, null)
  );
  cols[0].setSort();

  var search = nlapiSearchRecord(
    "customrecord_ng_cs_show_labor_schedule",
    null,
    filt,
    cols
  );
  if (search != null) {
    var matched = false;
    var labor = {};
    for (var i = 0; i < search.length; i++) {
      var test_st = nlapiStringToDate(
        "{0} {1}".NG_Format(
          date,
          search[i].getValue("custrecord_ng_cs_labor_start")
        )
      ).getTime();
      var test_et = nlapiStringToDate(
        "{0} {1}".NG_Format(
          date,
          search[i].getValue("custrecord_ng_cs_labor_end")
        )
      ).getTime();

      if (st >= test_st && st <= test_et && et >= test_st && et <= test_et) {
        var lType = search[i].getValue("custrecord_ng_cs_labor_type");
        var lTypeText = search[i].getText("custrecord_ng_cs_labor_type");
        var mult = new Number(
          search[i].getValue("custrecord_ng_cs_labor_multiplier")
        );
        var supMrkUp = _M.roundToHundredths(
          new Number(
            search[i]
              .getValue("custrecord_ng_cs_supervisor_markup")
              .replace("%", "")
          ) / 100
        );
        var data = {
          lType: lTypeText,
          date: date,
          start: start,
          end: end,
          mult: mult,
          superV: supMrkUp,
        };

        labor[lType] = data;
        matched = true;
        break;
      }
    }
    if (!matched) {
      for (var i = 0; i < search.length; i++) {
        var test_st = nlapiStringToDate(
          "{0} {1}".NG_Format(
            date,
            search[i].getValue("custrecord_ng_cs_labor_start")
          )
        ).getTime();
        var test_et = nlapiStringToDate(
          "{0} {1}".NG_Format(
            date,
            search[i].getValue("custrecord_ng_cs_labor_end")
          )
        ).getTime();
        var lType = search[i].getId();

        var data = null;
        if (_tools.isEmpty(labor[lType])) {
          var lTypeText = search[i].getText("custrecord_ng_cs_labor_type");
          var mult = new Number(
            search[i].getValue("custrecord_ng_cs_labor_multiplier")
          );
          var supMrkUp = _M.roundToHundredths(
            new Number(
              search[i]
                .getValue("custrecord_ng_cs_supervisor_markup")
                .replace("%", "")
            ) / 100
          );
          data = {
            lType: lTypeText,
            date: date,
            mult: mult,
            superV: supMrkUp,
          };
        } else {
          data = labor[lType];
        }

        if (st < test_st && et >= test_st && et <= test_et) {
          data.start = search[i].getValue("custrecord_ng_cs_labor_start");
          data.end = end;
        } else if (st >= test_st && st <= test_et && et > test_et) {
          data.start = start;
          data.end = search[i].getValue("custrecord_ng_cs_labor_end");
        } else if (st < test_st && et > test_et) {
          data.start = search[i].getValue("custrecord_ng_cs_labor_start");
          data.end = search[i].getValue("custrecord_ng_cs_labor_end");
        } else {
          continue;
        }

        labor[lType] = data;
      }

      var tCount = new Number(0);
      var mCount = new Number(0);

      for (var key in labor) {
        tCount++;
        if (
          !_tools.isEmpty(labor[key].start) &&
          !_tools.isEmpty(labor[key].end)
        ) {
          mCount++;
        }
      }

      if (
        (tCount > 0 || mCount > 0) &&
        Math.round(tCount) == Math.round(mCount)
      ) {
        matched = true;
      }
    }

    if (!matched) {
      window.alert(
        "There do not appear to be any labor rates scheduled for this show on {0} that completely match the time frame of {1} to {2}".NG_Format(
          date,
          start,
          end
        )
      );
      return null;
    } else {
      var dur = getDuration(st, et);
      var response = {};
      if (_tools.getKeyCount(labor) == 1) {
        var k = null;
        for (var key in labor) {
          k = labor[key];
          break;
        }
        var rate = _M.roundToHundredths(k["mult"] * itemRate * workerCount);
        var text =
          "Service Date: {0}\nStart: {1} || End: {2}\nTotal Duration: {3}:{4}{9}\nLabor: {5} for {6}:{7} @ ${8}".NG_Format(
            date,
            start,
            end,
            dur["h"],
            dur["m"].toFixed(0).paddingLeft("00"),
            k["lType"],
            dur["h"],
            dur["m"].toFixed(0).paddingLeft("00"),
            rate.toFixed(2),
            workerText
          );
        response = {
          q: dur["d"],
          r: rate,
          t: text,
          s:
            k["superV"] > 0
              ? _M.roundToHundredths(k["superV"] * (rate * dur["d"]))
              : 0,
        };
      } else {
        var lText = "";
        var total = new Number(0);
        var superV = new Number(0);
        for (var key in labor) {
          var k = labor[key];
          var rate = _M.roundToHundredths(k["mult"] * itemRate * workerCount);
          var d = getDuration(
            nlapiStringToDate("{0} {1}".NG_Format(date, k["start"])).getTime(),
            nlapiStringToDate("{0} {1}".NG_Format(date, k["end"])).getTime()
          );
          var amount = _M.roundToHundredths(rate * d["d"]);
          total += amount;
          if (amount != 0) {
            lText += "\nLabor: {0} for {1}:{2} @ ${3}".NG_Format(
              k["lType"],
              d["h"],
              d["m"].toFixed(0).paddingLeft("00"),
              rate.toFixed(2)
            );
          }
          if (k["superV"] > superV) {
            superV = k["superV"];
          }
        }
        total = _M.roundToHundredths(total);
        var r = _M.roundToHundredths(total / dur["d"]);
        var text =
          "Service Date: {0}\nStart: {1} || End: {2}\nTotal Duration: {3}:{4}{6}{5}".NG_Format(
            date,
            start,
            end,
            dur["h"],
            dur["m"].toFixed(0).paddingLeft("00"),
            lText,
            workerText
          );
        response = {
          q: dur["d"],
          r: r,
          t: text,
          s: superV > 0 ? _M.roundToHundredths(superV * total) : 0,
        };
      }

      return response;
    }
  } else {
    window.alert(
      "There do not appear to be any labor rates scheduled for this show on {0}. Please review the show's labor scheduling.".NG_Format(
        date
      )
    );
    return null;
  }
}

function getDuration(s, e) {
  var duration = _M.roundToHundredths((e - s) / (1000 * 60 * 60));
  var hours = Math.floor(duration);
  var minutes = (duration * 60) % 60;

  return { d: duration, h: hours, m: minutes };
}

function pushDaysCalcToLine(data, i) {
  var line = data[i];
  var itemID = line.itemid;
  var qty = line.qty;
  var cols = line.cols;
  var pl = line.pl;
  var rate = line.rate;
  var amount = line.amount;

  nlapiSelectNewLineItem("item");
  nlapiSetCurrentLineItemValue("item", "item", itemID, true, true);

  setTimeout(function () {
    nlapiSetCurrentLineItemValue("item", "quantity", qty);
    if (!_tools.isEmpty(pl)) {
      nlapiSetCurrentLineItemValue("item", "price", pl);
    }
    if (pl == "-1") {
      nlapiSetCurrentLineItemValue("item", "rate", rate);
      nlapiSetCurrentLineItemValue("item", "amount", amount);
    }

    if (cols != null) {
      for (var c = 0; c < cols.length; c++) {
        nlapiSetCurrentLineItemValue(
          "item",
          cols[c].field,
          cols[c].value,
          true,
          true
        );
      }
    }

    setTimeout(function () {
      nlapiCommitLineItem("item");

      if (i + 1 < data.length) {
        pushDaysCalcToLine(data, i + 1);
      } else {
        addingToLine02 = false;
        closeDaysCalc();
        nlapiSetFieldValue("custbody_item", "");
      }

      return;
    }, 250);
  }, 250);
}

function matrixItem(parentItem) {
  var itemFilt = new Array(["matrix", "is", "T"], "and", [
    "isinactive",
    "is",
    "F",
  ]);
  var itemCols = new Array(
    new nlobjSearchColumn(_scLib.fields.item.color, null, null),
    new nlobjSearchColumn(_scLib.fields.item.size, null, null)
  );
  if (_scLib.EnableFreightOptsOption) {
    itemCols.push(
      new nlobjSearchColumn(_scLib.fields.item.freightOpts, null, null)
    );
  }
  if (_scLib.EnableOrientationOption) {
    itemCols.push(
      new nlobjSearchColumn(_scLib.fields.item.orientation, null, null)
    );
  }
  if (_scLib.EnableGraphicOption) {
    itemCols.push(
      new nlobjSearchColumn(_scLib.fields.item.graphics, null, null)
    );
  }
  var itemSearch = null;
  try {
    itemSearch = nlapiSearchRecord("item", null, itemFilt, itemCols);
  } catch (err) {
    _log.logError(
      err,
      "Error encountered verifying selected item is a matrix item"
    );
  }

  return itemSearch;
}

function parseTime(initTime) {
  if (_tools.isEmpty(initTime)) return null;
  var d = new Date(2000, 0, 1, 0, 0, 0, 0);
  var time = initTime.match(/(\d+)(:(\d\d))?\s*(p?)/i);
  d.setHours(parseInt(time[1]) + (parseInt(time[1]) < 12 && time[4] ? 12 : 0));
  d.setMinutes(parseInt(time[3]) || 0);
  return d;
}

function setDuration(line) {
  try {
    var startID = "daysitemtimestart_{0}".NG_Format(line);
    var endID = "daysitemtimeend_{0}".NG_Format(line);
    var durID = "daysitemduration_{0}".NG_Format(line);
    var start = document.getElementById(startID).value;
    var end = document.getElementById(endID).value;
    if (_tools.isInArray(end, ["12:00am", "12:00 am", "12:00AM", "12:00 AM"])) {
      window.alert("End time cannot be midnight. Please change to 11:59 pm");
      setFieldFocus(endID);
      return;
    }
    if (
      _tools.isEmpty(start) ||
      _tools.isEmpty(end) ||
      !_TIME_REG.test(start) ||
      !_TIME_REG.test(end)
    ) {
      return;
    }

    var tStart = parseTime(start);
    var tEnd = parseTime(end);
    if (tEnd.getTime() < tStart.getTime()) {
      window.alert("End time cannot be before start time");
      setFieldFocus(startID);
      return;
    } else if (tStart.getTime() == tEnd.getTime()) {
      window.alert("Start time and end time cannot be the same");
      setFieldFocus(endID);
      return;
    }
    var duration = _M.roundToHundredths((tEnd - tStart) / (1000 * 60 * 60));
    document.getElementById(durID).innerText = duration.toFixed(2);
  } catch (err) {
    window.alert(
      "There was a problem reading the time entry. Please verify that your time entries are in the format hh:mm am/pm"
    );
    console.log(err);
  }
}

function onTimeChange(e) {
  var e_id = e.target.id;
  var time = document.getElementById(e_id).value;
  if (_TIME_REG.test(time)) {
    setDuration(e_id.split("_")[1]);
  } else if (!_tools.isEmpty(time)) {
    window.alert(
      "Time entries should be in the format HH:MM am/pm. Please verify your entry."
    );
    e.target.focus();
  }
}

function setFieldFocus(id) {
  var field = document.getElementById(id);
  if (field != null) {
    field.focus();
  }
}

//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//
//	BEGIN SHOW MANAGEMENT CLIENT SCRIPTING
//
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////
//////////////////////////////////////////////////////

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function ng_clientPageInit_03(type) {
  if (!_TriggerBO) {
    var exhbID = nlapiGetFieldValue("entity");

    if (!_tools.isEmpty(_ShowTableID)) {
      setPriceLevel();
    }

    if (_tools.isEmpty(_ShowTableID) && _scLib.RetainLastShow) {
      var values = {};
      values["action"] = "get";
      values["user"] = nlapiGetUser();
      var lastShow = _scLib.getLastShow(values);
      _ShowTableID = lastShow;
      if (!_tools.isEmpty(lastShow)) {
        nlapiSetFieldValue("custbody_show_table", _ShowTableID, false);
      }
    }

    getShowData();
    if (!_tools.isEmpty(_SHOW_DATA)) {
      setTimeout(function () {
        setPriceLevel(exhbID);
      }, 1000);

      if (_UseCnclPct) {
        var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
        var pct =
          rawPct.search("%") >= 0
            ? new Number(rawPct.replace("%", ""))
            : new Number(rawPct);
        _CancellationPct = pct / 100;
      }
      if (_UseTaxRate && type != "edit" && !_scLib.SalesTaxOnItemLine) {
        var taxRate = _SHOW_DATA["custrecord_tax_rate"];
        if (!_tools.isEmpty(taxRate)) {
          nlapiSetFieldValue("istaxable", "T", false);
          nlapiSetFieldValue("taxitem", taxRate, false);
        }
      }
    }

    if (evType == "edit") {
      startingLineCount = nlapiGetLineItemCount("item");

      if (_UseCnclPct) {
        _CurrentRecord = nlapiLoadRecord(
          nlapiGetRecordType(),
          nlapiGetRecordId()
        );
        _ShowTableID =
          _ShowTableID || nlapiGetFieldValue("custbody_show_table");
        if (!_tools.isEmpty(_ShowTableID)) {
          _ApplyCnclCharge = isOnOrAfterMoveInDate(_ShowTableID);
        }
      }

      if (
        _UseScriptedPaymentForm &&
        _tools.isEmpty(_NoPaymentPrompt ? nlapiGetFieldValue("terms") : null) &&
        !_tools.isInArray(_CurrentRole, _NoPromptRoles)
      ) {
        triggerBackgroundExhibData(true);
      }
    }

    if (
      !_tools.isEmpty(_ShowTableID) &&
      !_tools.isEmpty(_SHOW_DATA) &&
      _tools.isEmpty(nlapiGetFieldValue("custbody_cseg_ng_cs_job")) &&
      _UseCSJobs
    ) {
      nlapiSetFieldValue(
        "custbody_cseg_ng_cs_job",
        _SHOW_DATA["custrecord_show_job"],
        false
      );
      nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"], false);
    } else if (!_tools.isEmpty(_ShowTableID) && !_tools.isEmpty(_SHOW_DATA)) {
      nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"], false);
    }

    var showField = document.forms["main_form"].elements["inpt_custbody_show"];
    if (showField != null) {
      if (isNaN(showField.length)) {
        showField.focus();
      } else {
        if (showField.length > 0) {
          showField[0].focus();
        }
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @returns {Boolean} True to continue save, false to abort save
 */
function ng_clientSaveRecord_03() {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function ng_clientValidateField_03(type, name, linenum) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function ng_clientFieldChanged_03(type, name, linenum) {
  if (!_TriggerBO) {
    if (name == "custbody_show_table") {
      _ShowTableID = nlapiGetFieldValue(name);
      if (!_tools.isEmpty(_ShowTableID)) {
        getShowData();

        if (!_tools.isEmpty(_SHOW_DATA)) {
          if (_UseCSJobs) {
            nlapiSetFieldValue(
              "custbody_cseg_ng_cs_job",
              _SHOW_DATA["custrecord_show_job"]
            );
            nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"]);
          } else {
            nlapiSetFieldValue("class", _SHOW_DATA["custrecord_fin_show"]);
          }
          if (_UseSubsidiaries) {
            if (!_tools.isEmpty(_SHOW_DATA["custrecord_show_subsidiary"])) {
              nlapiSetFieldValue(
                "subsidiary",
                _SHOW_DATA["custrecord_show_subsidiary"],
                false
              );
            } else {
              if (_tools.isEmpty(nlapiGetFieldValue("subsidiary"))) {
                nlapiSetFieldValue("subsidiary", "1", false);
              }
            }
          }

          if (_UseLocations) {
            nlapiSetFieldValue(
              "location",
              _SHOW_DATA["custrecord_show_venue"],
              false
            );
          }
          nlapiDisableField("entity", false);
          var showTableFields = new Array(
            "custrecord_adv_ord_date",
            "custrecord_adv_price_level",
            "custrecord_std_price_level"
          );
          if (_UseTaxRate) {
            showTableFields.push("custrecord_tax_rate");
          }
          if (_UseCnclPct) {
            showTableFields.push("custrecord_cancellation_pct");
          }
          _AdvDate = nlapiStringToDate(_SHOW_DATA["custrecord_adv_ord_date"]);
          nlapiSetFieldValue(
            "custbody_advanced_order_date",
            _SHOW_DATA["custrecord_adv_ord_date"],
            false
          );
          if (_UseTaxRate && !_scLib.SalesTaxOnItemLine) {
            if (!_tools.isEmpty(_SHOW_DATA["custrecord_tax_rate"])) {
              nlapiSetFieldValue("istaxable", "T", true);
              nlapiSetFieldValue(
                "taxitem",
                _SHOW_DATA["custrecord_tax_rate"],
                false
              );
            }
          }
          if (_UseCnclPct) {
            var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
            var pct =
              rawPct.search("%") >= 0
                ? new Number(rawPct.replace("%", ""))
                : new Number(rawPct);
            _CancellationPct = pct / 100;
          }

          if (_UseCnclPct) {
            _ApplyCnclCharge = isOnOrAfterMoveInDate(_ShowTableID);
          }

          /*nlapiSetFieldValue("department", _scLib.DefaultShowMgmtDepartment, false);
					setTimeout(function() {
						nlapiSetFieldValue("custbody_ng_cs_order_type", _scLib.DefaultShowMgmtOrderType, false);
					}, 2500);*/
        }

        if (_scLib.RetainLastShow) {
          var values = {};
          values["action"] = "set";
          values["user"] = nlapiGetUser();
          values["show"] = _ShowID;
          _scLib.setLastShow(values);
        }
      } else {
        nlapiSetFieldValue("class", "");
        if (_UseCSJobs) {
          nlapiSetFieldValue("custbody_cseg_ng_cs_job", "");
        }
        nlapiDisableField("entity", true);
        _ShowTableID = null;
        _SHOW_DATA = null;
      }
    }

    if (
      _tools.isInArray(name, [
        "custbody_show_table",
        "custbody_ng_cs_order_type",
      ])
    ) {
      setPriceLevel();
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @returns {Void}
 */
function ng_clientPostSourcing_03(type, name) {
  if (!_TriggerBO) {
    if (name == "entity") {
      setTimeout(function () {
        if (_tools.isEmpty(gpl)) {
          gpl = nlapiGetFieldValue("custbody_price_level");
        }
        setOrderTax();
      }, 250);
    }

    if (type == "item") {
      if (name == "item") {
        if (!addingToLine01 && !addingToLine02) {
          if (!_tools.isEmpty(nlapiGetFieldValue("custbody_price_level"))) {
            gpl = nlapiGetFieldValue("custbody_price_level");
          }
          if (!_tools.isEmpty(gpl)) {
            nlapiSetCurrentLineItemValue(type, "price", gpl);
          }
        }
      }
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientLineInit_03(type) {}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to save line item, false to abort save
 */
function ng_clientValidateLine_03(type) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Void}
 */
function ng_clientRecalc_03(type) {}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item insert, false to abort insert
 */
function ng_clientValidateInsert_03(type) {
  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @returns {Boolean} True to continue line item delete, false to abort delete
 */
function ng_clientValidateDelete_03(type) {
  return true;
}
