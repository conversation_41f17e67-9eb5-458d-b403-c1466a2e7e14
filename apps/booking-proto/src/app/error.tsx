"use client"

import { But<PERSON> } from "src/components/ui/button"
import { CalendarIcon, RefreshCw } from "lucide-react"
import { useEffect } from "react"

export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error)
  }, [error])

  return (
    <div className="flex h-screen flex-col items-center justify-center bg-background p-4">
      <div className="mb-4 text-destructive">
        <CalendarIcon size={64} />
      </div>
      <h1 className="mb-2 text-2xl font-bold text-foreground">Something went wrong</h1>
      <p className="mb-6 max-w-md text-center text-muted-foreground">
        We're sorry, but there was an error loading the calendar application.
      </p>
      <Button onClick={reset} className="flex items-center gap-2">
        <RefreshCw size={16} />
        Try again
      </Button>
    </div>
  )
}
