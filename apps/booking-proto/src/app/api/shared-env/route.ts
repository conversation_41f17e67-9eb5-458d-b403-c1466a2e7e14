import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
	const sharedEnv = req.headers.get('x-shared-env')

	if (typeof sharedEnv === 'string') {
		try {
			const parsedEnv = JSON.parse(sharedEnv)
			return NextResponse.json(parsedEnv)
		} catch (error) {
			console.error('Error parsing shared environment variables:', error)
			return NextResponse.json(
				{ error: 'Failed to parse shared environment variables' },
				{ status: 500 }
			)
		}
	} else {
		return NextResponse.json(
			{ error: 'Shared environment variables not found' },
			{ status: 404 }
		)
	}
}
