/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 */
define(["N/record", "N/render", "N/query", "N/search"], /**
 * @param{record} record
 * @param{render} render
 * @param{query} query
 * @param{search} search
 */
(record, render, query, search) => {
  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServletRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  const beforeLoad = (scriptContext) => {};

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = (scriptContext) => {
    let sc = scriptContext;
    let mode = sc.type;

    if (["create", "edit", "copy"].includes(mode)) {
      setEventDateData(sc);
    }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (scriptContext) => {
    let sc = scriptContext;
    let mode = sc.type;

    if (["create", "edit", "copy"].includes(mode)) {
      setAutomatedWebBlurb(sc);
    }
  };

  function setAutomatedWebBlurb(sc) {
    let currRec = record.load({
      type: 'customrecord_ng_cs_show_table_web_blurbs',
      id: sc.newRecord.id
    });

    let csEvent = currRec.getValue({
      fieldId: "custrecord_ng_cs_stwb_show_table",
    });
    let checkboxVal = currRec.getValue({
      fieldId: "custrecord_automated_content_enabled",
    });
    let templateId = currRec.getValue({
      fieldId: "custrecord_automated_content_template",
    });

    log.audit("csEvent", csEvent);
    log.audit("checkboxVal", checkboxVal);
    log.audit("templateId", templateId);

    if (checkboxVal && csEvent && templateId) {
      log.audit("Setting automated content");

      try {
        let renderer = render.create();

        const { setTemplateById, addRecord } = renderer;

        setTemplateById(Number(templateId));

        let eventRecord = record.load({
          type: "customrecord_show",
          id: csEvent,
        });
        addRecord("record", eventRecord);

        addRecord(
          "eventBlurb",
          record.load({
            type: "customrecord_ng_cs_show_table_web_blurbs",
            id: currRec.id,
          }),
        );

        var htmlContent = renderer.renderAsString();

        currRec.setValue({
          fieldId: "custrecord_ng_cs_stwb_web_blurb",
          value: htmlContent,
        });

        currRec.save();
      } catch (e) {
        log.audit("Error Creating Template", e);
      }
    }
  }

  function setEventDateData(sc) {
    let currRec = sc.newRecord;

    let csEvent = currRec.getValue({
      fieldId: "custrecord_ng_cs_stwb_show_table",
    });
    let checkboxVal = currRec.getValue({
      fieldId: "custrecord_automated_content_enabled",
    });
    let templateId = currRec.getValue({
      fieldId: "custrecord_automated_content_template",
    });

    if (checkboxVal && csEvent && templateId) {
      let eventDateRecordSearch = search.create({
        type: "customrecord_show_date",
        filters: [["custrecord_show_number_date", "anyof", csEvent]],
        columns: [
          search.createColumn({
            name: "custrecord_show_number_date",
            label: "CS Event",
          }),
          search.createColumn({
            name: "custrecord_date_type",
            label: "Date Type",
          }),
          search.createColumn({ name: "custrecord_date", label: "Date" }),
          search.createColumn({
            name: "custrecord_start_time",
            label: "Start Time",
          }),
          search.createColumn({
            name: "custrecord_end_time",
            label: "End Time",
          }),
        ],
      });

      let eventDateRecordSearchResults = [];
      let eventDateRecordData = [];

      getAllResultsFor(eventDateRecordSearch, (result) => {
        eventDateRecordSearchResults.push(result);
      });

      if (eventDateRecordSearchResults.length !== 0) {
        eventDateRecordData = eventDateRecordSearchResults.map((eventRec) => {
          return {
            custrecord_date_type: eventRec.getText({
              name: "custrecord_date_type",
            }),
            custrecord_date: eventRec.getValue({ name: "custrecord_date" }),
            custrecord_start_time: eventRec.getValue({
              name: "custrecord_start_time",
            }),
            custrecord_end_time: eventRec.getValue({
              name: "custrecord_end_time",
            }),
            sortableDate: new Date(eventRec.getValue({ name: "custrecord_date" }))
          };
        });

        log.audit("eventDateRecordData", eventDateRecordData);

        currRec.setValue({
          fieldId: "custrecord_ng_cs_blrb_event_date_data",
          value: JSON.stringify({ eventDate: eventDateRecordData }),
        });
      } else {
        currRec.setValue({
          fieldId: "custrecord_ng_cs_blrb_event_date_data",
          value: JSON.stringify({ eventDate: [] }),
        });
      }
    }
  }

  const getAllResultsFor = (searchObj, callback) => {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  };

  return { beforeLoad, beforeSubmit, afterSubmit };
});
