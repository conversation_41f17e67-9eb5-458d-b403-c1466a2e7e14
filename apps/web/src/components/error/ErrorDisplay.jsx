import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Collapse,
  Paper,
  Tabs,
  Tab,
  Button
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { 
  ClipboardIcon, 
  XMarkIcon, 
  ChevronDownIcon, 
  ChevronUpIcon,
  ExclamationTriangleIcon,
  ShieldExclamationIcon,
  CreditCardIcon,
  DocumentIcon,
  ClipboardDocumentCheckIcon,
  CodeBracketIcon,
  ShoppingCartIcon
} from '@heroicons/react/24/outline';
import Lot<PERSON> from 'react-lottie';
import errorAnimation from '../../../public/assets/lottie/error-order.json';
import SyntaxHighlighter from 'react-syntax-highlighter/dist/cjs/prism';
import { atomOneDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { useRouter } from 'next/router';

// Normalize the error object to handle different structures
const normalizeError = (errorInput) => {
  if (!errorInput) return null;
  
  console.log('Normalizing error input:', errorInput);
  
  let name = '';
  
  // Check if the error is inside an 'error' property
  if (errorInput.error && typeof errorInput.error === 'object') {
    console.log('Found nested error object');
    name = errorInput.error.name || errorInput.type || 'UNKNOWN_ERROR';
    
    // Add category prefix if not present and we can determine it
    if (name && !name.includes('.') && errorCategories[name]) {
      name = `${errorCategories[name]}.${name}`;
    }
    
    return {
      name: name,
      message: errorInput.error.message || errorInput.message || 'An unknown error occurred',
      details: errorInput.error.details || {},
      timestamp: errorInput.error.timestamp || new Date().toISOString()
    };
  }
  
  // Handle case where error properties are directly on the input object
  if (errorInput.name || errorInput.type) {
    console.log('Found direct error properties');
    name = errorInput.name || errorInput.type || 'UNKNOWN_ERROR';
    
    // Add category prefix if not present and we can determine it
    if (name && !name.includes('.') && errorCategories[name]) {
      name = `${errorCategories[name]}.${name}`;
    }
    
    return {
      name: name,
      message: errorInput.message || 'An unknown error occurred',
      details: errorInput.details || {},
      timestamp: errorInput.timestamp || new Date().toISOString()
    };
  }
  
  // Fallback for completely unknown structure
  console.log('Using fallback error handling');
  return {
    name: 'UNKNOWN_ERROR',
    message: typeof errorInput === 'string' ? errorInput : 'An unknown error occurred',
    details: typeof errorInput === 'object' ? errorInput : {},
    timestamp: new Date().toISOString()
  };
};

// Map error types to their categories if not already formatted
const errorCategories = {
  // Validation errors
  'INVALID_CART_ITEMS': 'VALIDATION',
  'MISSING_REQUIRED_FIELD': 'VALIDATION',
  'INVALID_FORMAT': 'VALIDATION',
  'INVALID_VALUE': 'VALIDATION',
  'DUPLICATE_ENTRY': 'VALIDATION',
  
  // Payment errors
  'CREDIT_CARD_ERROR': 'PAYMENT',
  'PROCESSING_FAILED': 'PAYMENT',
  'INSUFFICIENT_FUNDS': 'PAYMENT',
  'DECLINED': 'PAYMENT',
  
  // Record errors
  'NOT_FOUND': 'RECORD',
  'ALREADY_EXISTS': 'RECORD',
  'CONFLICT': 'RECORD',
  
  // System errors
  'INTERNAL_ERROR': 'SYSTEM',
  'SERVER_ERROR': 'SYSTEM',
  'TIMEOUT': 'SYSTEM',
  'NEW_ORDER_ERROR': 'SYSTEM'
};

const getErrorConfig = (errorName) => {
  if (!errorName) return {
    icon: ShieldExclamationIcon,
    color: 'error.main',
    title: 'System Error'
  };

  // Handle case where error name doesn't include category prefix
  // Add category prefix if we can determine it
  if (!errorName.includes('.') && errorCategories[errorName]) {
    errorName = `${errorCategories[errorName]}.${errorName}`;
  }

  // Handle nested error name "CATEGORY.TYPE" format
  if (errorName.includes('.')) {
    const category = errorName.split('.')[0];
    const errorType = errorName.split('.')[1];
    
    // Special case for cart validation errors
    if (category === 'VALIDATION' && errorType === 'INVALID_CART_ITEMS') {
      return {
        icon: ShoppingCartIcon,
        color: 'warning.dark',
        title: 'Cart Items Error',
        type: 'CART_ITEMS'
      };
    }
    
    switch (category) {
      case 'VALIDATION':
        return {
          icon: DocumentIcon,
          color: 'warning.dark',
          title: 'Validation Error'
        };
      case 'PAYMENT':
        return {
          icon: CreditCardIcon,
          color: 'error.main',
          title: 'Payment Error'
        };
      case 'RECORD':
        return {
          icon: ExclamationTriangleIcon,
          color: 'warning.dark',
          title: 'Record Error'
        };
      default:
        return {
          icon: ShieldExclamationIcon,
          color: 'error.main',
          title: 'System Error'
        };
    }
  }
  
  // Handle direct error names
  switch (errorName) {
    case 'VALIDATION_ERROR':
    case 'VALIDATION':
      return {
        icon: DocumentIcon,
        color: 'warning.dark',
        title: 'Validation Error'
      };
    case 'PAYMENT_ERROR':
    case 'PAYMENT':
    case 'CREDIT_CARD_ERROR':
      return {
        icon: CreditCardIcon,
        color: 'error.main',
        title: 'Payment Error'
      };
    case 'RECORD_ERROR':
    case 'RECORD':
      return {
        icon: ExclamationTriangleIcon,
        color: 'warning.dark',
        title: 'Record Error'
      };
    case 'INTERNAL_ERROR':
      return {
        icon: CodeBracketIcon,
        color: 'error.main',
        title: 'Internal Error'
      };
    case 'SYSTEM_ERROR':
    case 'SYSTEM':
    case 'NEW_ORDER_ERROR':
      return {
        icon: ShieldExclamationIcon,
        color: 'error.main',
        title: 'System Error'
      };
    default:
      return {
        icon: ShieldExclamationIcon,
        color: 'error.main',
        title: 'System Error'
      };
  }
};

const getPaletteColor = (theme, colorPath) => {
  if (!colorPath || !colorPath.includes('.')) return theme.palette.error.main;
  const [palette, variant] = colorPath.split('.');
  return theme.palette[palette]?.[variant] || theme.palette.error.main;
};

// Process newlines in text for proper display
const processNewlines = (text) => {
  if (typeof text !== 'string') return text;
  return text.replace(/\\n/g, '\n');
};

const formatErrorDetails = (details) => {
  if (!details) return {};
  
  // If details has a cause property, normalize it
  if (details.cause) {
    const formattedCause = { ...details.cause };
    
    // Format stack trace if it exists
    if (Array.isArray(formattedCause.stackTrace)) {
      formattedCause.stackTrace = formattedCause.stackTrace.join('\n');
    }
    
    // Process details field for proper newline display
    if (formattedCause.details) {
      formattedCause.details = processNewlines(formattedCause.details);
    }
    
    return {
      ...details,
      cause: formattedCause
    };
  }
  
  return details;
};

const formatStackTrace = (stackTrace) => {
  if (!stackTrace) return null;
  
  if (Array.isArray(stackTrace)) {
    return stackTrace.join('\n');
  }
  
  return processNewlines(stackTrace);
};

const formatBugReport = (originalError) => {
  const normalizedError = normalizeError(originalError);
  const errorConfig = getErrorConfig(normalizedError.name);
  const now = new Date().toISOString();
  const browser = window.navigator.userAgent;
  const url = window.location.href;
  const formattedDetails = formatErrorDetails(normalizedError.details);
  const stackTrace = formattedDetails.stack || 
                    (formattedDetails.cause?.stackTrace) || 
                    'No stack trace available';

  return `Bug Report - ${normalizedError.name}
===========================================
Time: ${now}
Environment: ${process.env.NODE_ENV}
URL: ${url}
Browser: ${browser}

Error Information
-------------------------------------------
Type: ${normalizedError.name}
Title: ${errorConfig.title}
Message: ${normalizedError.message}
Code: ${formattedDetails.cause?.code || 'N/A'}
Timestamp: ${normalizedError.timestamp}

Error Details
-------------------------------------------
${JSON.stringify(formattedDetails, null, 2)}

Stack Trace
-------------------------------------------
${typeof stackTrace === 'string' ? stackTrace : JSON.stringify(stackTrace, null, 2)}

Steps to Reproduce
-------------------------------------------
1. Navigate to: ${url}
2. Attempted action: Order submission
3. Result: Error occurred

Additional Context
-------------------------------------------
- Environment: ${process.env.NODE_ENV || 'Production'}
- Browser: ${browser}
- Time of Error: ${normalizedError.timestamp}
- Error Location: Checkout Process

Technical Details
-------------------------------------------
\`\`\`json
${JSON.stringify({
  error: normalizedError.name,
  message: normalizedError.message,
  code: formattedDetails.cause?.code,
  details: formattedDetails,
  timestamp: normalizedError.timestamp,
  browser: browser,
  url: url,
  env: process.env.NODE_ENV
}, null, 2)}
\`\`\`

Original Error Response
-------------------------------------------
\`\`\`json
${JSON.stringify(originalError, null, 2)}
\`\`\`

===========================================
Generated by CS Event Services Error Reporter
`;
};

const ErrorDisplay = ({ error, onClose }) => {
  const theme = useTheme();
  const [showDetails, setShowDetails] = useState(false);
  const [copied, setCopied] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const [normalizedError, setNormalizedError] = useState(null);
  const containerRef = useRef(null);
  const [containerWidth, setContainerWidth] = useState(0);
  
  // Add router for navigation to cart
  const router = useRouter();
  
  useEffect(() => {
    // Normalize the error on component mount or when error changes
    const normalized = normalizeError(error);
    console.log('Normalized error:', normalized);
    setNormalizedError(normalized);
  }, [error]);
  
  useEffect(() => {
    // Measure the container width
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          setContainerWidth(entry.contentRect.width);
        }
      });
      
      resizeObserver.observe(containerRef.current);
      
      // Initial measurement
      setContainerWidth(containerRef.current.offsetWidth);
      
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [error]);
  
  if (!normalizedError) return null;
  
  const errorConfig = getErrorConfig(normalizedError.name);
  const errorColor = getPaletteColor(theme, errorConfig.color);
  const formattedDetails = formatErrorDetails(normalizedError.details);
  
  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  const copyToClipboard = async (e) => {
    e.stopPropagation();
    
    try {
      const bugReport = formatBugReport(error);
      await navigator.clipboard.writeText(bugReport);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: errorAnimation,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  // Get error code from the most likely location
  const errorCode = formattedDetails.cause?.code || 
                   formattedDetails.code || 
                   (typeof error === 'object' && error.error?.details?.cause?.code);

  // Format the error message to display newlines properly
  const formattedMessage = processNewlines(normalizedError.message);

  // Check if this is a cart items validation error
  const isCartItemsError = errorConfig.type === 'CART_ITEMS';
  const invalidItems = isCartItemsError && formattedDetails?.invalidItems ? formattedDetails.invalidItems : [];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      ref={containerRef}
    >
      <Paper
        elevation={0}
        sx={{
          position: 'relative',
          overflow: 'hidden',
          mb: 3,
          borderRadius: 3,
          border: `1px solid ${alpha(errorColor, 0.12)}`,
          backgroundColor: 'white',
          boxShadow: `0 4px 6px -1px ${alpha('#000', 0.1)}, 0 2px 4px -1px ${alpha('#000', 0.06)}`
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            backgroundColor: errorColor,
          }}
        />

        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
            <Box 
              sx={{ 
                width: 40, 
                height: 40, 
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: alpha(errorColor, 0.1),
                borderRadius: 2,
                color: errorColor
              }}
            >
              {React.createElement(errorConfig.icon, { className: "h-6 w-6" })}
            </Box>

            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <Typography
                    variant="h6"
                    sx={{
                      color: errorColor,
                      fontWeight: 600,
                      fontSize: '0.875rem',
                    }}
                  >
                    {errorConfig.title}
                  </Typography>
                  {errorCode && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: alpha(errorColor, 0.7),
                        fontFamily: 'monospace',
                        mt: -0.5
                      }}
                    >
                      {errorCode}
                    </Typography>
                  )}
                </Box>
                <IconButton
                  aria-label="close"
                  size="small"
                  onClick={() => {
                    // For cart errors, navigate back to cart page when closing
                    if (isCartItemsError) {
                      onClose();
                      router.push('/cart');
                    } else {
                      onClose();
                    }
                  }}
                  sx={{
                    color: alpha(errorColor, 0.7),
                    '&:hover': {
                      backgroundColor: alpha(errorColor, 0.08)
                    }
                  }}
                >
                  <XMarkIcon className="h-4 w-4" />
                </IconButton>
              </Box>

              <Typography
                variant="body2"
                sx={{
                  color: alpha(errorColor, 0.9),
                  mb: 2,
                  lineHeight: 1.5,
                  whiteSpace: 'pre-line' // Handle newlines in the message
                }}
              >
                {formattedMessage}
              </Typography>

              {(normalizedError.details && Object.keys(normalizedError.details).length > 0) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      backgroundColor: alpha(theme.palette.grey[900], 0.03),
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.grey[900], 0.1)}`,
                      width: containerWidth > 0 ? `${containerWidth - 96}px` : 'calc(812px - 96px)', // Subtract padding and icon width
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      onClick={() => setShowDetails(!showDetails)}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 1.5,
                        cursor: 'pointer',
                        backgroundColor: alpha(theme.palette.grey[900], 0.02),
                        borderBottom: showDetails ? `1px solid ${alpha(theme.palette.grey[900], 0.1)}` : 'none',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.grey[900], 0.04)
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.palette.grey[800],
                            fontWeight: 500,
                            fontSize: '0.8125rem'
                          }}
                        >
                          Error Details
                        </Typography>
                        {showDetails ? (
                          <ChevronUpIcon className="h-4 w-4" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4" />
                        )}
                      </Box>
                      <Tooltip title={copied ? "Bug Report Copied!" : "Copy Bug Report"}>
                        <IconButton
                          size="small"
                          onClick={copyToClipboard}
                          sx={{
                            color: copied ? theme.palette.success.main : theme.palette.grey[600],
                            transition: 'color 0.2s ease',
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.grey[900], 0.08)
                            }
                          }}
                        >
                          {copied ? (
                            <ClipboardDocumentCheckIcon className="h-4 w-4" />
                          ) : (
                            <ClipboardIcon className="h-4 w-4" />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Box>

                    <Collapse in={showDetails}>
                      <Box>
                        <Tabs
                          value={tabIndex}
                          onChange={handleTabChange}
                          variant="fullWidth"
                          sx={{
                            borderBottom: `1px solid ${alpha(theme.palette.grey[900], 0.1)}`,
                            '& .MuiTab-root': {
                              fontSize: '0.75rem',
                              minHeight: '40px'
                            }
                          }}
                        >
                          <Tab label="Details" />
                          {(formattedDetails.stack || (formattedDetails.cause?.stackTrace)) && (
                            <Tab label="Stack Trace" />
                          )}
                          <Tab label="Raw Response" />
                        </Tabs>
                        <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
                          {tabIndex === 0 && (
                            <SyntaxHighlighter
                              language="json"
                              style={atomOneDark}
                              customStyle={{
                                margin: 0,
                                borderRadius: '0 0 8px 8px',
                                fontSize: '12px',
                                backgroundColor: 'transparent',
                                wordBreak: 'break-all',
                                whiteSpace: 'pre-wrap',
                                overflowWrap: 'break-word'
                              }}
                              wrapLines={true}
                              wrapLongLines={true}
                            >
                              {JSON.stringify(formattedDetails, null, 2)}
                            </SyntaxHighlighter>
                          )}
                          {tabIndex === 1 && (
                            <SyntaxHighlighter
                              language="javascript"
                              style={atomOneDark}
                              customStyle={{
                                margin: 0,
                                borderRadius: '0 0 8px 8px',
                                fontSize: '12px',
                                backgroundColor: 'transparent',
                                wordBreak: 'break-all',
                                whiteSpace: 'pre-wrap',
                                overflowWrap: 'break-word'
                              }}
                              wrapLines={true}
                              wrapLongLines={true}
                            >
                              {formatStackTrace(formattedDetails.stack) || formatStackTrace(formattedDetails.cause?.stackTrace) || 'No stack trace available'}
                            </SyntaxHighlighter>
                          )}
                          {tabIndex === 2 && (
                            <SyntaxHighlighter
                              language="json"
                              style={atomOneDark}
                              customStyle={{
                                margin: 0,
                                borderRadius: '0 0 8px 8px',
                                fontSize: '12px',
                                backgroundColor: 'transparent',
                                wordBreak: 'break-all',
                                whiteSpace: 'pre-wrap',
                                overflowWrap: 'break-word'
                              }}
                              wrapLines={true}
                              wrapLongLines={true}
                            >
                              {JSON.stringify(error, null, 2)}
                            </SyntaxHighlighter>
                          )}
                        </Box>
                      </Box>
                    </Collapse>
                  </Paper>
                </motion.div>
              )}
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Cart Items Error Display */}
      {isCartItemsError && invalidItems.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
            The following items need to be removed from your cart before you can proceed:
          </Typography>
          
          {/* Group items by collection if available */}
          {(() => {
            // Check if items have collection info
            const hasCollections = invalidItems.some(item => item.collection);
            
            if (hasCollections) {
              // Group items by collection
              const itemsByCollection = {};
              invalidItems.forEach(item => {
                const collectionName = item.collection?.name || 'Uncategorized';
                if (!itemsByCollection[collectionName]) {
                  itemsByCollection[collectionName] = [];
                }
                itemsByCollection[collectionName].push(item);
              });
              
              return (
                <>
                  {Object.entries(itemsByCollection).map(([collectionName, items], collectionIndex) => (
                    <Box key={collectionName} sx={{ mb: 3 }}>
                      <Typography 
                        variant="subtitle2" 
                        sx={{ 
                          mb: 1, 
                          px: 1, 
                          py: 0.5, 
                          backgroundColor: alpha(theme.palette.primary.main, 0.08),
                          borderRadius: '4px',
                          display: 'inline-block'
                        }}
                      >
                        Collection: {collectionName}
                      </Typography>
                      
                      <Paper variant="outlined" sx={{ 
                        borderRadius: 1,
                        overflow: 'hidden',
                        mb: collectionIndex < Object.keys(itemsByCollection).length - 1 ? 2 : 0
                      }}>
                        {items.map((item, index) => (
                          <Box 
                            key={index} 
                            sx={{ 
                              p: 2, 
                              display: 'flex',
                              alignItems: 'flex-start',
                              borderBottom: index !== items.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.light, 0.05)
                              }
                            }}
                          >
                            {/* Item Image */}
                            <Box 
                              sx={{ 
                                width: 70, 
                                height: 70, 
                                borderRadius: 1,
                                mr: 2,
                                overflow: 'hidden',
                                flexShrink: 0,
                                border: `1px solid ${theme.palette.divider}`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: alpha(theme.palette.grey[100], 0.5)
                              }}
                            >
                              {item.image ? (
                                <img 
                                  src={item.image} 
                                  alt={item.name} 
                                  style={{ 
                                    width: '100%', 
                                    height: '100%', 
                                    objectFit: 'contain' 
                                  }} 
                                />
                              ) : (
                                <ShoppingCartIcon style={{ 
                                  width: 30, 
                                  height: 30, 
                                  color: theme.palette.grey[400]
                                }} />
                              )}
                            </Box>
                            
                            {/* Item Details */}
                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                  {item.name}
                                </Typography>
                                
                                {item.quantity && (
                                  <Box sx={{ 
                                    backgroundColor: alpha(theme.palette.primary.main, 0.1), 
                                    borderRadius: '4px',
                                    px: 1.5,
                                    py: 0.5,
                                    display: 'flex',
                                    alignItems: 'center',
                                    height: 'fit-content'
                                  }}>
                                    <Typography variant="body2" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
                                      Qty: {item.quantity}
                                    </Typography>
                                  </Box>
                                )}
                              </Box>
                              
                              <Typography variant="body2" color="error.main" sx={{ mt: 0.5, fontWeight: 500 }}>
                                {item.reason}
                              </Typography>
                              
                              {/* Item Specifications */}
                              {(item.memo || item.squareFtLength || item.squareFtWidth || item.recordedFreightWeight) && (
                                <Box sx={{ mt: 1.5 }}>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                    {item.memo && (
                                      <Box sx={{ 
                                        backgroundColor: alpha(theme.palette.grey[500], 0.1), 
                                        borderRadius: '4px',
                                        px: 1,
                                        py: 0.5,
                                        display: 'flex',
                                        alignItems: 'center'
                                      }}>
                                        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                                          Memo: {item.memo}
                                        </Typography>
                                      </Box>
                                    )}
                                    
                                    {item.isSquareFtItem && (item.squareFtLength || item.squareFtWidth) && (
                                      <Box sx={{ 
                                        backgroundColor: alpha(theme.palette.warning.light, 0.2), 
                                        borderRadius: '4px',
                                        px: 1,
                                        py: 0.5,
                                        display: 'flex',
                                        alignItems: 'center'
                                      }}>
                                        <Typography variant="caption" sx={{ color: theme.palette.warning.dark }}>
                                          Size: {item.squareFtLength || 0} × {item.squareFtWidth || 0}
                                        </Typography>
                                      </Box>
                                    )}
                                    
                                    {item.recordedFreightWeight && (
                                      <Box sx={{ 
                                        backgroundColor: alpha(theme.palette.info.light, 0.2), 
                                        borderRadius: '4px',
                                        px: 1,
                                        py: 0.5,
                                        display: 'flex',
                                        alignItems: 'center'
                                      }}>
                                        <Typography variant="caption" sx={{ color: theme.palette.info.dark }}>
                                          Weight: {item.recordedFreightWeight}
                                        </Typography>
                                      </Box>
                                    )}
                                  </Box>
                                </Box>
                              )}
                              
                              {/* Item Type Indicators */}
                              {(item.isFreightItem || item.isLaborItem || item.isSquareFtItem) && (
                                <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                                  {item.isFreightItem && (
                                    <Box sx={{ 
                                      backgroundColor: alpha(theme.palette.success.light, 0.2), 
                                      borderRadius: '4px',
                                      px: 1,
                                      py: 0.5
                                    }}>
                                      <Typography variant="caption" sx={{ color: theme.palette.success.dark }}>
                                        Freight Item
                                      </Typography>
                                    </Box>
                                  )}
                                  {item.isLaborItem && (
                                    <Box sx={{ 
                                      backgroundColor: alpha(theme.palette.secondary.light, 0.2), 
                                      borderRadius: '4px',
                                      px: 1,
                                      py: 0.5
                                    }}>
                                      <Typography variant="caption" sx={{ color: theme.palette.secondary.dark }}>
                                        Labor Item
                                      </Typography>
                                    </Box>
                                  )}
                                  {item.isSquareFtItem && (
                                    <Box sx={{ 
                                      backgroundColor: alpha(theme.palette.info.light, 0.2), 
                                      borderRadius: '4px',
                                      px: 1,
                                      py: 0.5
                                    }}>
                                      <Typography variant="caption" sx={{ color: theme.palette.info.dark }}>
                                        Square Foot Item
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              )}
                              
                              {/* Item ID for reference */}
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                                Item ID: {item.id}
                              </Typography>
                            </Box>
                          </Box>
                        ))}
                      </Paper>
                    </Box>
                  ))}
                </>
              );
            } else {
              // No collection information, display items directly
              return (
                <Paper variant="outlined" sx={{ 
                  borderRadius: 1,
                  overflow: 'hidden',
                  mb: 2
                }}>
                  {invalidItems.map((item, index) => (
                    <Box 
                      key={index} 
                      sx={{ 
                        p: 2, 
                        display: 'flex',
                        alignItems: 'flex-start',
                        borderBottom: index !== invalidItems.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.light, 0.05)
                        }
                      }}
                    >
                      {/* Item Image */}
                      <Box 
                        sx={{ 
                          width: 70, 
                          height: 70, 
                          borderRadius: 1,
                          mr: 2,
                          overflow: 'hidden',
                          flexShrink: 0,
                          border: `1px solid ${theme.palette.divider}`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: alpha(theme.palette.grey[100], 0.5)
                        }}
                      >
                        {item.image ? (
                          <img 
                            src={item.image} 
                            alt={item.name} 
                            style={{ 
                              width: '100%', 
                              height: '100%', 
                              objectFit: 'contain' 
                            }} 
                          />
                        ) : (
                          <ShoppingCartIcon style={{ 
                            width: 30, 
                            height: 30, 
                            color: theme.palette.grey[400]
                          }} />
                        )}
                      </Box>
                      
                      {/* Item Details */}
                      <Box sx={{ flex: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            {item.name}
                          </Typography>
                          
                          {item.quantity && (
                            <Box sx={{ 
                              backgroundColor: alpha(theme.palette.primary.main, 0.1), 
                              borderRadius: '4px',
                              px: 1.5,
                              py: 0.5,
                              display: 'flex',
                              alignItems: 'center',
                              height: 'fit-content'
                            }}>
                              <Typography variant="body2" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
                                Qty: {item.quantity}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                        
                        <Typography variant="body2" color="error.main" sx={{ mt: 0.5, fontWeight: 500 }}>
                          {item.reason}
                        </Typography>
                        
                        {/* Item Specifications */}
                        {(item.memo || item.squareFtLength || item.squareFtWidth || item.recordedFreightWeight) && (
                          <Box sx={{ mt: 1.5 }}>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              {item.memo && (
                                <Box sx={{ 
                                  backgroundColor: alpha(theme.palette.grey[500], 0.1), 
                                  borderRadius: '4px',
                                  px: 1,
                                  py: 0.5,
                                  display: 'flex',
                                  alignItems: 'center'
                                }}>
                                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                                    Memo: {item.memo}
                                  </Typography>
                                </Box>
                              )}
                              
                              {(item.squareFtLength || item.squareFtWidth) && (
                                <Box sx={{ 
                                  backgroundColor: alpha(theme.palette.warning.light, 0.2), 
                                  borderRadius: '4px',
                                  px: 1,
                                  py: 0.5,
                                  display: 'flex',
                                  alignItems: 'center'
                                }}>
                                  <Typography variant="caption" sx={{ color: theme.palette.warning.dark }}>
                                    Size: {item.squareFtLength || 0} × {item.squareFtWidth || 0}
                                  </Typography>
                                </Box>
                              )}
                              
                              {item.recordedFreightWeight && (
                                <Box sx={{ 
                                  backgroundColor: alpha(theme.palette.info.light, 0.2), 
                                  borderRadius: '4px',
                                  px: 1,
                                  py: 0.5,
                                  display: 'flex',
                                  alignItems: 'center'
                                }}>
                                  <Typography variant="caption" sx={{ color: theme.palette.info.dark }}>
                                    Weight: {item.recordedFreightWeight}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        )}
                        
                        {/* Item Type Indicators */}
                        {(item.isFreightItem || item.isLaborItem || item.isSquareFtItem) && (
                          <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                            {item.isFreightItem && (
                              <Box sx={{ 
                                backgroundColor: alpha(theme.palette.success.light, 0.2), 
                                borderRadius: '4px',
                                px: 1,
                                py: 0.5
                              }}>
                                <Typography variant="caption" sx={{ color: theme.palette.success.dark }}>
                                  Freight Item
                                </Typography>
                              </Box>
                            )}
                            {item.isLaborItem && (
                              <Box sx={{ 
                                backgroundColor: alpha(theme.palette.secondary.light, 0.2), 
                                borderRadius: '4px',
                                px: 1,
                                py: 0.5
                              }}>
                                <Typography variant="caption" sx={{ color: theme.palette.secondary.dark }}>
                                  Labor Item
                                </Typography>
                              </Box>
                            )}
                            {item.isSquareFtItem && (
                              <Box sx={{ 
                                backgroundColor: alpha(theme.palette.info.light, 0.2), 
                                borderRadius: '4px',
                                px: 1,
                                py: 0.5
                              }}>
                                <Typography variant="caption" sx={{ color: theme.palette.info.dark }}>
                                  Square Foot Item
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        )}
                        
                        {/* Item ID for reference */}
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Item ID: {item.id}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Paper>
              );
            }
          })()}
          
          <Box sx={{ mt: 2, pb: 2, display: 'flex', justifyContent: 'center' }}>
            <Button 
              variant="contained" 
              color="primary"
              onClick={() => {
                onClose();
                router.push('/cart');
              }}
              startIcon={<ShoppingCartIcon style={{ width: 16, height: 16 }} />}
              sx={{
                px: 3,
                py: 1,
                fontWeight: 600,
                boxShadow: theme.shadows[2],
                '&:hover': {
                  boxShadow: theme.shadows[4]
                }
              }}
            >
              Return to Cart to Fix Items
            </Button>
          </Box>
        </Box>
      )}
    </motion.div>
  );
};

export default ErrorDisplay; 