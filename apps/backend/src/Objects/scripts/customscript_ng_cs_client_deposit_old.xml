<clientscript scriptid="customscript_ng_cs_client_deposit_old">
  <description></description>
  <fieldchangedfunction>clientFieldChanged</fieldchangedfunction>
  <isinactive>T</isinactive>
  <lineinitfunction></lineinitfunction>
  <name>NG CS Client Deposit (OLD)</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <pageinitfunction>clientPageInit</pageinitfunction>
  <postsourcingfunction></postsourcingfunction>
  <recalcfunction></recalcfunction>
  <saverecordfunction></saverecordfunction>
  <scriptfile>[/SuiteScripts/CS UI Script Files/client/ng_cs_clientDeposit.js]</scriptfile>
  <validatedeletefunction></validatedeletefunction>
  <validatefieldfunction></validatefieldfunction>
  <validateinsertfunction></validateinsertfunction>
  <validatelinefunction></validatelinefunction>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_client_deposit_dep">
      <allemployees>F</allemployees>
      <alllocalizationcontexts>T</alllocalizationcontexts>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <eventtype></eventtype>
      <executioncontext>ACTION|ADVANCEDREVREC|BANKCONNECTIVITY|BANKSTATEMENTPARSER|BUNDLEINSTALLATION|CLIENT|CONSOLRATEADJUSTOR|CSVIMPORT|CUSTOMGLLINES|CUSTOMMASSUPDATE|DATASETBUILDER|DEBUGGER|EMAILCAPTURE|FICONNECTIVITY|FIPARSER|MAPREDUCE|OCRPLUGIN|OTHER|PAYMENTGATEWAY|PAYMENTPOSTBACK|PLATFORMEXTENSION|PORTLET|PROMOTIONS|RECORDACTION|RESTLET|SCHEDULED|SDFINSTALLATION|SHIPPINGPARTNERS|SUITELET|TAXCALCULATION|USEREVENT|USERINTERFACE|WEBAPPLICATION|WEBSERVICES|WORKBOOKBUILDER|WORKFLOW</executioncontext>
      <isdeployed>F</isdeployed>
      <loglevel>AUDIT</loglevel>
      <recordtype>CUSTOMERDEPOSIT</recordtype>
      <status>RELEASED</status>
    </scriptdeployment>
  </scriptdeployments>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
  </libraries>
</clientscript>