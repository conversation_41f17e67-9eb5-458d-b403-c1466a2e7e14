import Link from "next/link";
import { CreditCard, Delete, Edit } from "@mui/icons-material";
import {
  Backdrop,
  Box,
  Button,
  Card,
  Chip,
  CircularProgress,
  Container,
  IconButton,
  Typography,
} from "@mui/material";
import TableRow from "components/TableRow";
import { H5 } from "components/Typography";
import { FlexBox } from "components/flex-box";
import UserDashboardHeader from "components/header/UserDashboardHeader";
import CustomerDashboardLayout from "components/layouts/customer-dashboard";
import moment from "moment";
import Head from "next/head";
import React, { useState } from "react";
import { useSnackbar } from "notistack";
import { useSession } from "next-auth/react";
import { useTheme } from "@mui/material/styles";
import { useSettings } from "../../src/store/zSettingsStore";
import { loadUserPayments } from "../../lib/user/payment-list";
import { AuthGuard } from "../../src/guards/AuthGuard";
import LoadingCircleSpinner from "components/spinners/circle-spinner";

const PaymentMethods = ({ paymentMethods }) => {
  const [allPaymentMethods, setAllPaymentMethods] = useState(paymentMethods);
  const [isDeleting, setIsDeleting] = useState(new Set());
  const [backdrop, setBackdropVisible] = useState({
    visible: false,
    message: "",
    spinner: true,
  });
  const { enqueueSnackbar } = useSnackbar();
  const { settings } = useSettings();
  const { data: session } = useSession();
  const user = session?.user;
  const theme = useTheme();
  const { palette } = theme;

  console.log("palette", palette);

  const resetBackdrop = () => {
    setBackdropVisible({ visible: false, message: "", spinner: true });
  };

  // SECTION TITLE HEADER LINK
  const HEADER_LINK = (
    <Button
      color="primary"
      LinkComponent={Link}
      href="/payment-methods/add"
      sx={{
        bgcolor: "primary.light",
        px: "2rem",
      }}
    >
      Add New Payment Method
    </Button>
  );

  const handleCardDelete = async (id) => {
    console.log("Delete Card: ", id);
    let updatedPayments = [...allPaymentMethods];
    let updatedDelete = new Set(isDeleting).add(id);
    setIsDeleting(updatedDelete);
    setBackdropVisible({
      visible: true,
      message: "Deleting Payment Method...",
      spinner: true,
    });
    const headers = new Headers();
    headers.append("Content-Type", "application/json");

    const requestConfig = {
      method: "DELETE",
      headers,
    };

    const response = await fetch(
      `/api/customer/delete/card/${id}?uid=${user?.id}`,
      requestConfig,
    );

    if (!response.ok) {
      console.error("Error deleting Payment: ", response);
      enqueueSnackbar("Error deleting Payment", { variant: "error" });
      setAllPaymentMethods(updatedPayments);

      updatedDelete = new Set(isDeleting);
      updatedDelete.delete(id);
      setIsDeleting(updatedDelete);
      setBackdropVisible({
        visible: true,
        message: "Payment Failed To Delete",
        spinner: false,
      });
      setTimeout(() => {
        resetBackdrop();
      }, 500);
      return;
    }

    enqueueSnackbar("Payment deleted successfully", { variant: "success" });
    setBackdropVisible({
      visible: true,
      message: "Payment deleted successfully",
      spinner: false,
    });
    setTimeout(() => {
      setAllPaymentMethods(updatedPayments.filter((item) => item.id !== id));
      updatedDelete = new Set(isDeleting);
      updatedDelete.delete(id);
      setIsDeleting(updatedDelete);
      resetBackdrop();
    }, 1000);
  };

  function detectPaymentProcessor(paymentText) {
    const processors = [
      { name: "Visa", keywords: ["visa"] },
      { name: "Mastercard", keywords: ["mastercard", "master card"] },
      { name: "Amex", keywords: ["american express", "amex"] },
      // Add more payment processors and their associated keywords here
    ];

    const matchingProcessor = processors.find((processor) =>
      processor.keywords.some((keyword) =>
        paymentText.toLowerCase().includes(keyword),
      ),
    );

    return matchingProcessor ? matchingProcessor.name : "payment-card"; // If no payment processor is detected
  }

  return (
    <CustomerDashboardLayout
      topbarBgColor={settings?.custrecord_ng_cs_navbar_bckgrnd_color}
    >
      <Head>
        <title>
          {settings?.company?.name || "ConventionSuite Event Services"}
          {" | "}
          My Payment Methods
        </title>
      </Head>

      {/* TITLE HEADER AREA */}
      <UserDashboardHeader
        title="Payment Methods"
        icon={CreditCard}
        button={HEADER_LINK}
      />
      {/* BACKDROP FOR LOADING INDICATION */}
      <Backdrop
        style={{ zIndex: 1000 }}
        open={backdrop.visible /* true backdrop.visible */}
      >
        <Container display="flex">
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
          >
            <Container display="flex">
              <Box
                display="flex"
                flexDirection="row"
                justifyContent="center"
                alignItems="center"
              >
                <span>
                  {backdrop.spinner ? (
                    <LoadingCircleSpinner />
                  ) : null}
                </span>
              </Box>
              <Typography
                sx={{
                  textAlign: "center",
                  marginTop: "4vh",
                  color: "white",
                }}
                variant="body2"
              >
                {backdrop.message}
              </Typography>
            </Container>
          </Box>
        </Container>
      </Backdrop>

      {/* ALL PAYMENT LIST AREA */}
      {allPaymentMethods.map((item, ind) => (
        <Box
          key={item.id}
          sx={{
            position: "relative",
          }}
        >
          <TableRow
            sx={{
              my: "1rem",
              padding: "6px 18px",
              borderColor: item.default
                ? `${palette.secondary.main} !important`
                : "transparent",
              border: item.default && "1px solid",
            }}
            key={ind}
          >
            <FlexBox alignItems="center" m={0.75}>
              <Card
                sx={{
                  width: 42,
                  height: 28,
                  mr: "10px",
                  borderRadius: "2px",
                }}
              >
                <img
                  src={`/assets/images/payment-methods/${detectPaymentProcessor(
                    item.paymentMethod.label,
                  )}.svg`}
                  alt={item.paymentMethod.label}
                  width="100%"
                />
              </Card>

              <H5 whiteSpace="pre" m={0.75}>
                {item.name}
              </H5>
            </FlexBox>

            <Typography whiteSpace="pre" m={0.75}>
              {item.number}
            </Typography>

            <Typography whiteSpace="pre" m={0.75}>
              {moment(item.expiration).format("MM / YYYY")}
            </Typography>

            <Typography whiteSpace="pre" textAlign="center" color="grey.600">
              <IconButton
                LinkComponent={Link}
                href={`/payment-methods/${item.id}`}
              >
                <Edit fontSize="small" color="inherit" />
              </IconButton>

              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleCardDelete(item.id);
                }}
              >
                {isDeleting && isDeleting.has(item.id) ? (
                  <CircularProgress size={10} disableShrink />
                ) : (
                  <Delete fontSize="small" color="inherit" />
                )}
              </IconButton>
            </Typography>
          </TableRow>
          {item.default && (
            <Chip
              size="small"
              variant="filled"
              label="Default"
              color="secondary"
              sx={{
                position: "absolute",
                top: -9,
                right: 8,
              }}
            />
          )}
        </Box>
      ))}

      {/* /!* PAGINATION AREA *!/ */}
      {/* <FlexBox justifyContent="center" mt={5}> */}
      {/*  <Pagination count={5} onChange={(data) => console.log(data)} /> */}
      {/* </FlexBox> */}
    </CustomerDashboardLayout>
  );
};

PaymentMethods.getLayout = (page) => <AuthGuard>{page}</AuthGuard>;

export const getServerSideProps = async ({ req, res }) => {
  console.debug("cookies", req.cookies);
  const contact = req.cookies?.contact;
  const customer = req.cookies?.customer;

  if (!contact || !customer) {
    return {
      redirect: {
        destination: "/eventSelect",
        permanent: false,
      },
    };
  }

  const paymentList = await loadUserPayments(customer, contact);
  res.setHeader(
    "Cache-Control",
    "public, s-maxage=10, stale-while-revalidate=59",
  );

  return {
    props: {
      paymentMethods: paymentList,
    },
  };
};

const paymentMethods = [
  {
    id: "1050017AS",
    exp: "08 / 2022",
    payment_method: "Amex",
    card_no: "1234 **** **** ****",
  },
  {
    id: "1050017AS",
    exp: "10 / 2025",
    payment_method: "Mastercard",
    card_no: "1234 **** **** ****",
  },
  {
    id: "1050017AS",
    exp: "N/A",
    payment_method: "PayPal",
    card_no: "<EMAIL>",
  },
  {
    id: "1050017AS",
    exp: "08 / 2022",
    payment_method: "Visa",
    card_no: "1234 **** **** ****",
  },
];
export default PaymentMethods;
