/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var evType;
var _GenRandomEmail = _scLib.GenRandomEmail;
var _RandEmailDomain = _scLib.RandEmailDomain;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType 
 * 
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function clientPageInit(type){
	evType = type;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function clientFieldChanged(type, name, linenum){
	if (type == "creditcards") {
		if (name == "ccnumber") {
			var cardNumber = nlapiGetCurrentLineItemValue(type, name);
			if (!_tools.isEmpty(cardNumber) && cardNumber.search(/\*/) < 0) {
				var ccType = null;
				switch(_scLib.checkCC(cardNumber)) {
					case 1 : // Mastercard
						ccType = _scLib.MastercardPayMethodId;
						break;
					case 2 : // VISA
						ccType = _scLib.VisaPayMethodId;
						break;
					case 3 : // AmEx
						ccType = _scLib.AmexPayMethodId;
						break;
					case 4 : // Discover
						ccType = _scLib.DiscoverPayMethodId;
						break;
					default:
						window.alert("Invalid card number entered. Please try again.");
						nlapiSetCurrentLineItemValue(type, name, "", false);
						document.getElementById(name).focus();
						return;
						break;
				}
				if (ccType != null) {
					nlapiSetCurrentLineItemValue(type, "paymentmethod", ccType);
				}
			}
		}
	}
}


/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @returns {Boolean} True to continue save, false to abort save
 */
function clientSaveRecord(){
	var email = nlapiGetFieldValue("email");
	if (_tools.isEmpty(email) && _GenRandomEmail && !_tools.isEmpty(_RandEmailDomain)) {
		var go = window.confirm("This exhibitor has no e-mail address. Generate a random address now?");
		if (go) {
			while (true) {
				var genCount = new Number(0);
				while (genCount < 10) {
					email = "exhib-{0}-{1}-{2}-{3}{4}".NG_Format(_tools.randomString(4, 0),_tools.randomString(6, 0),_tools.randomString(0, 6),_tools.randomString(0, 4),_RandEmailDomain);
					var eFilt = new Array(
							["email","is",email]
					);
					var eSearch = null;
					var ex = false;
					try { eSearch = nlapiSearchRecord("customer", null, eFilt, null); }
					catch (err) { ex = true; }
					
					if (eSearch != null || ex) {
						genCount ++;
					} else {
						break;
					}
				}
				
				if (genCount >= 10) {
					var again = window.confirm("Unable to create random e-mail address. Try again?");
					if (!again) {
						break;
					}
				} else {
					nlapiSetFieldValue("email", email);
					break;
				}
			}
		}
	}
	
	if (!_tools.isEmpty(email) && nlapiGetFieldValue("giveaccess") == "T") {
		var eFilt = new Array(
				["email","is",email]
			,	"and"
			,	["giveaccess","is","T"]
		);
		if (!_tools.isInArray(evType, ["create","copy"])) {
			eFilt.push("and", ["internalid","noneof",[nlapiGetRecordId()]]);
		}
		var eCols = new Array(
				new nlobjSearchColumn("entityid", null, null)
			,	new nlobjSearchColumn("companyname", null, null)
		);
		var eSearch = null;
		try {
			eSearch = nlapiSearchRecord("customer", null, eFilt, eCols);
		} catch (err) { }
		if (eSearch != null) {
			var entityList = "";
			for (var i = 0; i < eSearch.length; i++) {
				entityList += eSearch[i].getValue("entityid") + " " + eSearch[i].getValue("companyname") + "\n";
			}
			
			var msg = "The following exhibitor(s) have been given web store access with the same e-mail address as this exhibitor:\n\n";
			msg += entityList;
			msg += "\nThis exhibitor cannot be saved until either it is given a different e-mail address or the above mentioned exhibitors have their e-mail addresses altered.";
			window.alert(msg);
			return false;
		}
	}
	
	return true;
}
