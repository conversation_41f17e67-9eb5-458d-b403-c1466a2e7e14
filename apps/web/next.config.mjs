import with<PERSON>undleAnalyzer from "@next/bundle-analyzer";
import withPlugins from "next-compose-plugins";

const {
  NEXT_PUBLIC_SCAN_DOMAIN,
  NEXT_PUBLIC_BOOKING_DOMAIN,
  ANALYZE,
  NEXT_PUBLIC_ACCOUNT_URL_ID
} = process.env;

/**
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ["@event-services/auth"],
  publicRuntimeConfig: {
    // Available on both server and client
    theme: "DEFAULT",
    currency: "USD",
  },
  images: {
    domains: [
      "unsplash.com",
      "i.imgur.com",
      `${NEXT_PUBLIC_ACCOUNT_URL_ID?.toLowerCase()}.app.netsuite.com`,
    ],
  },
  experimental: {
    nextScriptWorkers: true,
  },
  async rewrites() {
    return [
      {
        source: "/scan",
        destination: `${NEXT_PUBLIC_SCAN_DOMAIN}/scan`,
      },
      {
        source: "/scan/:path+",
        destination: `${NEXT_PUBLIC_SCAN_DOMAIN}/scan/:path+`,
      },
      {
        source: "/booking",
        destination: `${NEXT_PUBLIC_BOOKING_DOMAIN}/booking`,
      },
      {
        source: "/booking/:path+",
        destination: `${NEXT_PUBLIC_BOOKING_DOMAIN}/booking/:path+`,
      },
    ];
  },
};

const config = withPlugins(
  [[withBundleAnalyzer, { enabled: ANALYZE === "true" }]],
  nextConfig,
);

export default config;
