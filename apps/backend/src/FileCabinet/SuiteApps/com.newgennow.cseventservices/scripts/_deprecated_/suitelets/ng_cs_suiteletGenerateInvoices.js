/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var _PreInvoicing = _scLib.UsePreInvoicing;

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {
	if (request.getMethod() == "GET") {
		displayForm(request, response);
	} else {
		processRequest(request, response);
	}
}

function displayForm(request, response, status) {
	var context = nlapiGetContext();
	var sessionID = request.getParameter("sid");
	var form = nlapiCreateForm("Order Invoicing");
	var selShowsRAW = request.getParameter("shsl") || null;
	var selShows = !_tools.isEmpty(selShowsRAW) ? selShowsRAW.split(",") : [];
	var ordTypesRAW = request.getParameter("ot") || null;
	var ordTypes = !_tools.isEmpty(ordTypesRAW) ? ordTypesRAW.split(",") : [];
	var showDetails = request.getParameter("shdt") == "T";
	
	if (!_tools.isEmpty(sessionID)) {
		status = JSON.parse(context.getSessionObject(sessionID) || { });
		_log.logInfo("Retrieved Session Data", "{0}".NG_Format(JSON.stringify(status)));
		for (var showId in status) {
			if (showId != "sched") {
				var groupId = "requeststatus_{0}".NG_Format(showId);
				var fieldId = "custpage_status_{0}".NG_Format(showId);
				var divId = "div__alert__{0}".NG_Format(showId);
				form.addFieldGroup(groupId, "'{0}' Invoicing Status".NG_Format(status[showId].showName));
				if (status[showId].queued) {
					NewGen.lib.UI.addSuccessDiv(form, status[showId].status, fieldId, false, groupId, divId);
				} else if (status[showId].empty) {
					NewGen.lib.UI.addWarningDiv(form, status[showId].status, fieldId, false, groupId, divId);
				} else {
					NewGen.lib.UI.addFailureDiv(form, status[showId].status, fieldId, false, groupId, divId);
				}
			}
		}
	}
	
	form.addFieldGroup("show_sel", "Event Selection");
	var showsField = form.addField("custpage_show", "multiselect", "Select Event to Invoice", null, "show_sel").setMandatory(true).setBreakType("startcol");
	var ordTypeField = form.addField("custpage_order_types", "multiselect", "Order Types", null, "show_sel");
	var otFilt = new Array(
			["isinactive","is","F"]
	);
	var otCols = new Array(
			new nlobjSearchColumn("name", null, null)
	);
	otCols[0].setSort();
	var otResults = _tools.getSearchResults(nlapiCreateSearch("customlist_ng_cs_order_type", otFilt, otCols).runSearch(), false);
	if (!_tools.isEmpty(otResults)) {
		for (var r = 0; r < otResults.length; r++) {
			var isDefault = otResults[r].getId() == _scLib.DefaultExhibitorOrderType;
			ordTypeField.addSelectOption(otResults[r].getId(), otResults[r].getValue("name"), isDefault);
		}
	} else {
		ordTypeField.addSelectOption("", "No Options Available", false);
	}
	form.addField("custpage_invoice_date", "date", "Invoice Date", null, "show_sel");
	form.addField("custpage_display_details", "checkbox", "Display Order Details", null, "show_sel").setBreakType("startcol");
	form.addField("custpage_blank_001", "text", " ", null, "show_sel").setDisplayType("inline").setBreakType("startcol").setDefaultValue('<span style="color: #FFFFFF;">&nbsp;.&nbsp;</span>');
	form.addField("custpage_instructions", "textarea", "Instructions", null, "show_sel").setDisplayType("inline").setBreakType("startcol").setDefaultValue(getInstructions());
	form.addField("custpage_blank_002", "text", " ", null, "showselection").setDisplayType("inline").setBreakType("startcol").setDefaultValue('<span style="color: #FFFFFF;">&nbsp;.&nbsp;</span>');
	
	form.addField("custpage_page_url", "text", "url").setDisplayType("hidden").setDefaultValue(nlapiResolveURL("SUITELET", context.getScriptId(), context.getDeploymentId()));
	form.addField("custpage_url", "text", "url").setDisplayType("hidden").setDefaultValue(nlapiResolveURL("SUITELET", "customscript_ng_cs_sl_chk_acct_period", "customdeploy_ng_cs_sl_chk_acct_per_dep", true));
	
	_scLib.getOpenShows(showsField, true);
	
	var vals = { };
	var setFields = false;
	if (selShows.length > 0) {
		vals['custpage_show'] = selShows;
		setFields = true;
	}
	if (showDetails) {
		vals['custpage_display_details'] = "T";
		vals['custpage_order_types'] = ordTypes;
		setFields = true;
		
		if (selShows.length > 0) {
			var sFilt = new Array(
					["internalid","anyof",selShows]	
			);
			var sCols = new Array(
					new nlobjSearchColumn("name", null, null)
			);
			sCols[0].setSort();
			var sSearch = null;
			try {
				sSearch = nlapiSearchRecord("customrecord_show", null, sFilt, sCols);
			} catch (err) {
				_log.logError(err, "Error encountered getting event names");
			}
			
			if (!_tools.isEmpty(sSearch)) {
				for (var s = 0; s < sSearch.length; s++) {
					var ordFilt = new Array(
							["mainline", "is", "T"]
						,	"and"
						,	["custbody_show_table", "anyof", [sSearch[s].getId()]]
						,	"and"
						,	["custbody_ng_cs_order_type", "anyof", ordTypes]
						,	"and"
						,	["custbody_to_be_deleted","is","F"]
						,	"and"
						,	["status","noneof",["C","G","H","SalesOrd:C","SalesOrd:G","SalesOrd:H"]]
					);
					if (_PreInvoicing) {
						filt.push(
								"and"
							,	["custbody_invoice","is","T"]
							,	"and"
							,	["custbody_finvoice","is","F"]
						);
					}
					var ordCols = new Array(
							new nlobjSearchColumn("tranid", null, null)
						,	new nlobjSearchColumn("entity", null, null)
						,	new nlobjSearchColumn("custbody_booth_actual_exhibitor", null, null)
						,	new nlobjSearchColumn("custrecord_booth_number", "custbody_booth", null)
						,	new nlobjSearchColumn("custbody_balance", null, null)
						,	new nlobjSearchColumn("total", null, null)
					);
					ordCols[0].setSort();
					var ordSearch = null;
					try {
						var srch = nlapiCreateSearch("salesorder", ordFilt, ordCols);
						ordSearch = _tools.getSearchResults(srch.runSearch(), false);
					} catch (err) {
						_log.logError(err, "Error encountered getting event orders", "Show: {0}".NG_Format(sSearch[s].getValue("name")));
					}
					
					var tabId = "show_tab_{0}".NG_Format(sSearch[s].getId());
					var listId = "order_list_{0}".NG_Format(sSearch[s].getId());
					form.addTab(tabId, sSearch[s].getValue("name"));
					var sl = form.addSubList(listId, "list", sSearch[s].getValue("name"));
					sl.addField("selected", "checkbox", "Select");
					sl.addField("custcol_order_num", "text", "Order #");
					sl.addField("custcol_booth_num", "text", "Booth #");
					sl.addField("custcol_bill_prty", "text", "Billing Party");
					sl.addField("custcol_exhib", "text", "Exhibitor");
					sl.addField("custcol_total", "text", "Total");
					sl.addField("custcol_balance", "text", "Balance");
					sl.addField("custcol_order_id", "text", "Order ID").setDisplayType("hidden");
					
					if (!_tools.isEmpty(ordSearch)) {
						var ordValues = new Array();
						for (var r = 0; r < ordSearch.length; r++) {
							ordValues.push({
									custcol_order_num : ordSearch[r].getValue("tranid")
								,	custcol_booth_num : ordSearch[r].getValue(ordCols[3])
								,	custcol_bill_prty : ordSearch[r].getText("entity")
								,	custcol_exhib : ordSearch[r].getText("custbody_booth_actual_exhibitor")
								,	custcol_total : (new Number(ordSearch[r].getValue("total"))).toFixed(2)
								,	custcol_balance : (new Number(ordSearch[r].getValue("custbody_balance"))).toFixed(2)
								,	custcol_order_id : ordSearch[r].getId()
							});
						}
						sl.setLineItemValues(ordValues);
					}
				}
			}
		}
		
		form.addButton("custpage_show_refresh_btn", "Refresh Event Details", "ng_refreshDetails()");
	}
	if (setFields) {
		form.setFieldValues(vals);
	}
	
	form.setScript("customscript_ng_cs_cl_invoice_gen_form");
	
	form.addSubmitButton("Submit");
	response.writePage(form);
}

function processRequest(request, response) {
	var context = nlapiGetContext();
	var shows = request.getParameterValues("custpage_show");
	var invDate = request.getParameter("custpage_invoice_date");
	var ordTypesRAW = request.getParameter("custpage_order_types");
	var ordTypes = _tools.getMultiSelect(null, null, false, ordTypesRAW)
	if (ordTypes.length < 1) {
		ordTypes.push(_scLib.DefaultExhibitorOrderType);
	}
	var status = {  };
	
	var filt = new Array(
			["custbody_show_table","anyof",shows]
		,	"and"
		,	["custbody_to_be_deleted","is","F"]
		,	"and"
		,	["mainline","is","T"]
		,	"and"
		,	["status","noneof",["C","G","H","SalesOrd:C","SalesOrd:G","SalesOrd:H"]]
		,	"and"
		,	["custbody_ng_cs_order_type","anyof",ordTypes]
	);
	if (_PreInvoicing) {
		filt.push(
				"and"
			,	["custbody_invoice","is","T"]
			,	"and"
			,	["custbody_finvoice","is","F"]
		);
	}
	var cols = new Array(
			new nlobjSearchColumn("custbody_show_table", null, "count")
		,	new nlobjSearchColumn("custbody_show_table", null, "group")
	);
	var search = null;
	try {
		search = nlapiSearchRecord("salesorder", null, filt, cols);
	} catch (err) {
		_log.logError(err, "Error encountered searching for orders to invoice");
	}
	
	var schedStart = NewGen.lib.time.roundDownDate(new Date(), (1000 * 60 * 1), true);
	
	if (search != null) {
		for (var s = 0; s < search.length; s++) {
			var showId = search[s].getValue(cols[1]);
			var showName = search[s].getText(cols[1]);
			status[showId] = { showName : showName , queued : false , empty : false };
			try {
				var params = {
						custscript_ng_cs_gen_inv_show : JSON.stringify([showId])
					,	custscript_ng_cs_gen_inv_date : invDate
					,	custscript_ng_cs_gen_inv_ord_types : JSON.stringify(ordTypes)
				};
				var listId = "order_list_{0}".NG_Format(showId);
				var lineCount = request.getLineItemCount(listId);
				var selCount = 0;
				var orderIdList = new Array();
				for (var l = 1; l <= lineCount; l++) {
					if (request.getLineItemValue(listId, "selected", l) == "T") {
						orderIdList.push(request.getLineItemValue(listId, "custcol_order_id", l));
					}
				}
				if (orderIdList.length > 0) {
					params.custscript_ng_cs_gen_inv_slct = JSON.stringify(orderIdList);
				}
				var qstatus = nlapiScheduleScript("customscript_ng_cs_schd_gen_invoices", null, params);
				if (qstatus == "QUEUED") {
					status[showId].status = "Invoice generation successfully queued for event.";
					status[showId].queued = true;
				} else {
					status[showId].status = "Invoice generation failed to queue for event with queue status of '{0}'.".NG_Format(qstatus);
				}
			} catch (err) {
				_log.logError(err, "Invoice generation script failed to queue", "Event: {0} ({1})".NG_Format(showName, showId));
				status[showId].status = ("Invoice generation failed to queue for event with the following error: [{0}] {1}.".NG_Format(err.name, err.message)).substr(0, 300);
			}
		}
	}
	
	var schedEnd = NewGen.lib.time.roundUpDate(new Date(), (1000 * 60 * 1), true);
	
	for (var s = 0; s < shows.length; s++) {
		if (_tools.isEmpty(status[shows[s]])) {
			var showName = nlapiLookupField("customrecord_show", shows[s], "name");
			status[shows[s]] = { showName : showName , queued : false , empty : true , status : "Event has no open orders for invoicing." };
		}
	}
	
	var schedFilt = new Array(
			["script.scriptid","is",context.getScriptId()]
		,	"and"
		,	["datecreated", "within", "today"]
	);
	var schedCols = new Array(
			new nlobjSearchColumn("datecreated", null, null)
		,	new nlobjSearchColumn("startdate", null, null)
		,	new nlobjSearchColumn("enddate", null, null)
		,	new nlobjSearchColumn("status", null, null)
		,	new nlobjSearchColumn("percentcomplete", null, null)
		,	new nlobjSearchColumn("taskid", null, null)
	);
	
	var schedSearch = nlapiSearchRecord("scheduledscriptinstance", null, schedFilt, schedCols);
	status['sched'] = new Array();
	if (!_tools.isEmpty(schedSearch)) {
		var rs = schedStart.getTime();
		var re = schedEnd.getTime();
		
		for (var i = 0; i < schedSearch.length; i++) {
			var rt = nlapiStringToDate(schedSearch[i].getValue("datecreated") || "1/1/2000").getTime();
			if (rt >= rs && rt <= re) {
				status.sched.push({
						datecreated : schedSearch[i].getValue("datecreated")
					,	percentcomplete : schedSearch[i].getValue("percentcomplete")
					,	status : schedSearch[i].getValue("status")
					,	taskid : schedSearch[i].getValue("taskid")
				});
			}
		}
	}
	
	var sessionID = _tools.generateUUID();
	context.setSessionObject(sessionID, JSON.stringify(status));
	nlapiSetRedirectURL("SUITELET", context.getScriptId(), context.getDeploymentId(), null, { sid : sessionID });
}
/*
function fieldChange(type, name, linenum) {
	if (name == "custpage_show") {
		var showSel = _tools.getMultiSelect("custpage_show", null, false, null);
		if (showSel.length > 5) {
			window.alert("The maximum selection count is 5. Please remove some of your selections.");
			return;
		}
		
		if (nlapiGetFieldValue("custpage_display_details") == "T") {
			nlapiSetFieldValue("custpage_display_details", "F", false);
		}
	}
	
	if (name == "custpage_invoice_date") {
		var invDate = nlapiGetFieldValue(name);
		if (!_tools.isEmpty(invDate)) {
			var isClosed = nlapiRequestURL(nlapiGetFieldValue("custpage_url"), { recDate : invDate }).getBody();
			if (isClosed == "T") {
				window.alert("The invoice date you have chosen lies within a closed accounting period. Please enter a different date or leave blank to use today's date.");
				nlapiSetFieldValue(name, "", false);
			}
		}
	}
	
	if (name == "custpage_display_details") {
		var showDetails = nlapiGetFieldValue("custpage_display_details") == "T";
		if (showDetails) {
			ng_refreshDetails();
		}
	}
	
	if (!_tools.isEmpty(type) && type.search("order_list_") >= 0) {
		if (name == "selected") {
			if (nlapiGetLineItemValue(type, "selected", linenum) == "T") {
				var lineCount = nlapiGetLineItemCount(type);
				var selectCount = 0;
				for (var l = 1; l <= lineCount; l++) {
					if (nlapiGetLineItemValue(type, "selected", l) == "T") {
						selectCount++;
					}
				}
				
				if (selectCount >= 50) {
					window.alert("A maximum of 50 individual orders can be selected per event for invoicing. This line will be deselected.");
					nlapiSetLineItemValue(type, "selected", linenum, "F", false);
				}
			}
		}
	}
}

function onSave() {
	var showSel = _tools.getMultiSelect("custpage_show", null, false, null);
	if (showSel.length > 5) {
		window.alert("The maximum selection count is 5. Please remove some of your selections.");
		return false;
	}
	
	for (var s = 0; s < showSel.length; s++) {
		var listId = "order_list_{0}".NG_Format(showSel[s]);
		var lineCount = nlapiGetLineItemCount(listId);
		var selectCount = 0;
		for (var l = 1; l <= lineCount; l++) {
			if (nlapiGetLineItemValue(listId, "selected", l) == "T") {
				selectCount++;
			}
		}
		
		if (selectCount >= 50) {
			var showName = nlapiLookupField("customrecord_show", showSel[s], "name");
			var diff = Math.round(selectCount - 50).toFixed(0);
			window.alert("A maximum of 50 individual orders can be selected per event for invoicing. Please deselect at least {0} order(s) for event \"{1}\".".NG_Format(diff, showName));
		}
	}
	
	return true;
}

function ng_refreshDetails() {
	var shows = _tools.getMultiSelect("custpage_show", null, false, null);
	if (!_tools.isEmpty(shows) && shows.length > 0) {
		var sp = NewGen.lib.url.escapeURL(shows.join(","));
		var targetURL = "{0}&shdt={1}&shsl={2}".NG_Format(nlapiGetFieldValue("custpage_page_url"), "T", sp);
		NS.form.setChanged(false);
		window.location.replace(targetURL);
	} else {
		window.alert("Please select at least one event first before attempting to retrieve details.");
		nlapiSetFieldValue("custpage_display_details", "F", false);
	}
}
*/
function checkAccountingPeriod(request, response) {
	var recDate = request.getParameter("recDate");
	
	var filt = new Array(
			["startdate","onorbefore",recDate]
		,	"and"
		,	["enddate","onorafter",recDate]
		,	"and"
		,	["isquarter","is","F"]
		,	"and"
		,	["isyear","is","F"]
	);
	var search = null;
	try {
		search = nlapiSearchRecord("accountingperiod", null, filt, null);
	} catch (err) {
		_log.logError(err, "Error encountered searching for accounting periods");
	}
	if (search != null) {
		response.write(nlapiLoadRecord("accountingperiod", search[0].getId()).getFieldValue("closed"));
	} else {
		response.write("F");
	}
}

function getInstructions() {
	var html = '';
	html += '<p><span style="font-weight: bold;">How to use the Order Invoicing / Final Exhibitor Statement Tool</span><br />';
	html += 'Once an event is complete you will be able to general Final Invoices for your Event. Final invoices will be calculated based on the amount on orders that is left to be billed (order total less any deposits or payments made before final invoicing). This process will fully bill all Booth Orders on the event.<br />';
	html += '<br />';
	html += 'The tool can be used across all orders on an event in bulk (Option 1) or individual exhibitors/orders can be selected (Option 2).<br />';
	html += '<br />';
	html += '<span style="font-weight: bold;">Option 1)</span> To invoice all orders on an event select the event(s) from the "Event" multi-select (up to 5 events can be selected at one time) and press the Submit button.<br />';
	html += '<br />';
	html += '<span style="font-weight: bold;">Option 2)</span> If you\'d like to invoice only certain orders on certain event, select the event(s) from the "Event" multi-select (up to 5 events can be selected at one time) and check the "Display Individual Order Details" button. This will display all orders that have the ability to be invoiced. Select up to 50 line items across the selected events and press the Submit button to invoice those orders. The amount in the "Balance" column will be the amount that is sent out in an open Invoice to the exhibitor/customer.</p>';
	
	return html;
}
