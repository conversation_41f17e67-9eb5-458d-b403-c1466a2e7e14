import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  addPerformanceHeaders
} from "@event-services/netsuite-http";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  try {
    const { method } = req;

    switch (method) {
      case "GET": {
        return res.status(200).json({
          ready: "Ready for address mutation.",
        });
      }

      case "POST": {
        // Validate required body
        if (!req.body) {
          return res.status(400).json({ error: "Request body is required" });
        }

        console.debug('REQ body: " ', req.body);

        // Build NetSuite URL
        const url = buildNetSuiteUrl(
          "customscript_ng_cses_rl_user_registrar",
          "customdeploy_ng_cses_rl_user_registrar"
        );

        // Make request to NetSuite
        const { data, duration } = await makeNetSuiteRequest({
          url,
          method: "POST",
          body: req.body,
        });

        console.debug('REQ form Body: " ', req.body);

        // Add performance headers
        addPerformanceHeaders(res, requestStart, duration);

        return res.status(200).json(data);
      }

      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    return handleApiError(error, { res: res, requestStart: requestStart });
  }
}
