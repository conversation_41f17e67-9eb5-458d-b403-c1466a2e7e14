<customrecordtype scriptid="customrecord_show">
  <accesstype>NONENEEDED</accesstype>
  <allowattachments>T</allowattachments>
  <allowinlinedeleting>T</allowinlinedeleting>
  <allowinlinedetaching>T</allowinlinedetaching>
  <allowinlineediting>T</allowinlineediting>
  <allowmobileaccess>F</allowmobileaccess>
  <allownumberingoverride>F</allownumberingoverride>
  <allowquickadd>T</allowquickadd>
  <allowquicksearch>T</allowquicksearch>
  <allowuiaccess>T</allowuiaccess>
  <customsegment></customsegment>
  <description></description>
  <enabledle>T</enabledle>
  <enablekeywords>T</enablekeywords>
  <enablemailmerge>T</enablemailmerge>
  <enablenumbering>F</enablenumbering>
  <enableoptimisticlocking>F</enableoptimisticlocking>
  <enablesystemnotes>T</enablesystemnotes>
  <hierarchical>F</hierarchical>
  <icon></icon>
  <iconbuiltin>T</iconbuiltin>
  <iconindex></iconindex>
  <includeinsearchmenu>T</includeinsearchmenu>
  <includename>T</includename>
  <isinactive>F</isinactive>
  <isordered>F</isordered>
  <numberinginit></numberinginit>
  <numberingmindigits></numberingmindigits>
  <numberingprefix></numberingprefix>
  <numberingsuffix></numberingsuffix>
  <recordname>CS Event</recordname>
  <showcreationdate>F</showcreationdate>
  <showcreationdateonlist>F</showcreationdateonlist>
  <showid>F</showid>
  <showlastmodified>F</showlastmodified>
  <showlastmodifiedonlist>F</showlastmodifiedonlist>
  <shownotes>T</shownotes>
  <showowner>F</showowner>
  <showownerallowchange>F</showownerallowchange>
  <showowneronlist>F</showowneronlist>
  <customrecordcustomfields>
    <customrecordcustomfield scriptid="custrecord_fin_show">
      <accesslevel>2</accesslevel>
      <allowquickadd>T</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The job to which the show is related</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter or select the job to which the show is related.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Class</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-101</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_acct_exec">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The employee in the NetSuite ERP system assigned as the account manager.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the Account Manager from the drop down list. If you have appropriate privileges, you will be able to add the Account Manager.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Account Manager</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_sales_rep">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Record for the sales representative</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the sales representative from the drop down list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Sales Rep</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_facility">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Facilities where shows can be held.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter or select the facility where the show will be held.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_facility]</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_hall">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The ball room or hall where the show or event will be held.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the room(s) or hall(s) where the show or event will be held. This is a multi-select field.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue Spaces</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_exhibition_hall]</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield>[scriptid=customrecord_show.custrecord_facility]</fldcomparefield>
          <fldfilter>[scriptid=customrecord_exhibition_hall.custrecord_hall_facility]</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel></fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_type">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The type of show</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the type of show or event (usually Exhibitor Services or Show Management).</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_order_type]</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_w_start_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The first day orders will be available for entry.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the first day orders will be available for entry.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Website Start Date</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_w_end_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The last day orders will be available for entry.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the last day orders will be available for entry.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Website End Date</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_adv_wh_ship_rate">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Default shipping rate for freight services occurring on or before the advance order date.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CURRENCY</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is the default shipping rate for freight services occurring on or before the advance order date.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Advance Warehouse Drayage Rate</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_inbetween_ship_rate">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Default shipping rate for freight services occurring after the advance order date but before show start</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CURRENCY</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is the the default shipping rate for freight services occurring after the advance order date but before show start date.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>In-Between Shipping Rate</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_direct_shipping_rate">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The default shipping rate for freight services occurring during the show</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CURRENCY</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is the default shipping rate for freight services occurring during the show</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Show Site Drayage Rate</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_booth_size">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>A list of booth sizes</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select from the list of booths.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Booth Size</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|176212|243192, scriptid=customlist_booth_size]</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_image">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Show Management company logo or logo for the show.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>IMAGE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select an existing image or add image for the show management company or the show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Portal Image</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_adv_price_level">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Price level for advanced orders.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level for advanced orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Advance Price Level</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_std_price_level">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Price level for standard orders.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level for standard orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Standard Price Level</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_floor_plan">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The floor plan of the show.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DOCUMENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>The floor plan of the show in PDF format.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Floor Plan</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_venue">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The location of the show pulled from the Location record</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the location of the show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Advanced Warehouse Location</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-103</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_subsidiary">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Allow user to pick subsidiary from NetSuite</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the subsidiary from the list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Subsidiary</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-117</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_tax_rate">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Select the tax code for the show.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the tax code for the show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Tax Group</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-128</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_tax_percent">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>0.0%</defaultvalue>
      <description>Sales tax rate, based upon selected sales tax item for the event; meant for display purposes</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Tax Percent</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_evt_gst_pct">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>0.0%</defaultvalue>
      <description>Canadian GST rate, based upon selected sales tax code for the event; meant for display purposes</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Canadian GST rate, based upon selected sales tax code, for the event.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>GST/HST Percent</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_evt_pst_pct">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Canadian PST rate, based upon selected sales tax item for the event; meant for display purposes</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>PST Percent</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_cancellation_pct">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>50.0%</defaultvalue>
      <description>Cancellation percentage by show.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the cancellation percentage. This is applied by line item for cancellations.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Cancellation Percent</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue>100</maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_customer">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Select a customer</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select a customer from the list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Customer</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-2</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_customer_address">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Populated from customer record</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXTAREA</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from customer record, enter the address here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Customer Address</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYBILLADDRESS</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_show_customer]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_customer_phone">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Customer phone.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from customer record, enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Customer Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYPHONE</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_show_customer]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_customer_cell">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Customer mobile phone</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from customer record, enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Customer Cell</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYALTPHONE</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_show_customer]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_customer_email">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Customer email address.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>EMAIL</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from customer record, enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Customer Email</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYEMAIL</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_show_customer]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_acct_exec_cell">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Account rep mobile phone</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from employee record, enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Account Rep Cell Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYMOBILEPHONE</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_acct_exec]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_sales_rep_cell">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Sales rep mobile phone.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from employee record, enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Sales Rep Cell Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYMOBILEPHONE</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_sales_rep]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_exh_svs_rep">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Selected from employee record.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the exhibitor services representative from the list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Services Rep</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_exh_svs_rep_cell">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Exhibitor Services Rep mobile phone</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from employee record, enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Services Rep Cell Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYMOBILEPHONE</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_exh_svs_rep]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_project_mgr">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Project Manager mobile phone.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not populated from the employee record, enter the number here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Project Manager</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_project_mgr_cell">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Project Manager Cell Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>STDENTITYMOBILEPHONE</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_project_mgr]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_facility_address">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Address where the show is taking place.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXTAREA</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Address where the show is taking place. This address should be copied to sales orders and deposits.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue Address</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_fac_map">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>http://maps.google.com/maps?q={custrecord_facility_address}</defaultvalue>
      <description>Map of facility.</description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>URL</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter a URL to a map of the facility. For example http://www.location.com/map.pdf</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue Map</label>
      <linktext>Map</linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>F</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_facility_contact">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Contact name at venue.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the name of the contact person for the venue.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue Contact Name</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_venue_contacts]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield>[scriptid=customrecord_show.custrecord_facility]</fldcomparefield>
          <fldfilter>[scriptid=customrecord_ng_cs_venue_contacts.custrecord_ng_cs_venue_of_contact]</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel></fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_facility_contact_phone">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The facility contact phone. Not linked to an entity.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter in the contact phone number for the facility.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue Contact Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>[scriptid=customrecord_ng_cs_venue_contacts.custrecord_ng_cs_venuecont_phone]</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_facility_contact]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_facility_contact_email">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Facility email address.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>EMAIL</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter in the facility email address.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Venue Contact Email</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom>[scriptid=customrecord_ng_cs_venue_contacts.custrecord_ng_cs_venuecont_email]</sourcefrom>
      <sourcelist>[scriptid=customrecord_show.custrecord_facility_contact]</sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_terms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Payment terms.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the payment terms from the list for the show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Payment Terms</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-199</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_site_price_level">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Price level for on-site orders.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level for on-site orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>On Site Price Level</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_status">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection>[scriptid=customrecord_cs_event_status.val_26474_t1474328_802]</defaultselection>
      <defaultvalue></defaultvalue>
      <description>Show status</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the status of your show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Status</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_cs_event_status]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ship_to_warehouse_address">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CLOBTEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Ship To Warehouse Address</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ship_to_facility_address">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CLOBTEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Ship To Facility Address</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_mgmnt_price_lvl">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Show Management Price Level</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default show management price level.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Show Management Price Level</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_show_job">
      <accesslevel>2</accesslevel>
      <allowquickadd>T</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The job list linked to CS Job</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the job to which the show is linked.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Job</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_cseg_ng_cs_job]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_cs_st_show_complete">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection>[bundleid=176152|176212|243192, scriptid=customlist68.value_2_t1509349_311]</defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Complete</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|176212|243192, scriptid=customlist68]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_cs_st_contact_search">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Contact Search</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-119</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_cs_st_contact_group">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Contact Group</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-8</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_st_show_campaign">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Campaign</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-24</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_web_store_alt_pay">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Web Store Alt Pay</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_event_comments">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXTAREA</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Comments</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_event_rental_location">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Rentals Default Location</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-103</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_adv_ord_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Advanced Order Date</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the date for orders considered to be advanced.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Last Date for Advance Order Pricing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_19_4132987_329]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_wh_ship_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Warehouse Ship Date</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_19_4132987_329]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_last_adv_ship">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Last Day for Advance Shipments</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the last day for for advance shipments.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Last Day for Advance Shipments</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_19_4132987_329]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_last_late_ship">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Last Day for Late Shipments</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_19_4132987_329]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_cs_st_send_inventory_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Inventory Outgoing Date</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_19_4132987_329]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_cs_st_return_inventory_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Inventory Return Date</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_19_4132987_329]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_show_sprvsr_markup">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Labor Supervisor Markup</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_37_t1675345_475]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_show_image_url">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>URL</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Image URL</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_show.tab_72_t1516212_597]</subtab>
    </customrecordcustomfield>
  </customrecordcustomfields>
  <permissions>
    <permission>
      <permittedlevel>FULL</permittedlevel>
      <permittedrole>[bundleid=243192, scriptid=customrole_token_based_auth]</permittedrole>
      <restriction></restriction>
    </permission>
  </permissions>
  <subtabs>
    <subtab scriptid="tab_19_4132987_329">
      <tabparent></tabparent>
      <tabtitle>Event Dates</tabtitle>
    </subtab>
    <subtab scriptid="tab_15_4132987_329">
      <tabparent></tabparent>
      <tabtitle>Booths</tabtitle>
    </subtab>
    <subtab scriptid="tab_18_4132987_329">
      <tabparent></tabparent>
      <tabtitle>Freight</tabtitle>
    </subtab>
    <subtab scriptid="tab_17_4132987_329">
      <tabparent></tabparent>
      <tabtitle>Web Categories</tabtitle>
    </subtab>
    <subtab scriptid="tab_14_4132987_329">
      <tabparent></tabparent>
      <tabtitle>Additional Documents</tabtitle>
    </subtab>
    <subtab scriptid="tab_16_4132987_329">
      <tabparent></tabparent>
      <tabtitle>Transactions</tabtitle>
    </subtab>
    <subtab scriptid="tab_83_t1516212_757">
      <tabparent></tabparent>
      <tabtitle>Shipments</tabtitle>
    </subtab>
    <subtab scriptid="tab_37_t1675345_475">
      <tabparent></tabparent>
      <tabtitle>Labor</tabtitle>
    </subtab>
    <subtab scriptid="tab_66_t1516212_605">
      <tabparent></tabparent>
      <tabtitle>Tasks</tabtitle>
    </subtab>
    <subtab scriptid="tab_67_t1516212_384">
      <tabparent></tabparent>
      <tabtitle>Communications</tabtitle>
    </subtab>
    <subtab scriptid="tab_68_t1516212_219">
      <tabparent></tabparent>
      <tabtitle>System Information</tabtitle>
    </subtab>
    <subtab scriptid="tab_70_t1516212_746">
      <tabparent></tabparent>
      <tabtitle>Areas</tabtitle>
    </subtab>
    <subtab scriptid="tab_71_t1516212_694">
      <tabparent></tabparent>
      <tabtitle>Vendors</tabtitle>
    </subtab>
    <subtab scriptid="tab_72_t1516212_597">
      <tabparent></tabparent>
      <tabtitle>Web</tabtitle>
    </subtab>
    <subtab scriptid="tab_74_t1516212_392">
      <tabparent></tabparent>
      <tabtitle>Bookings</tabtitle>
    </subtab>
    <subtab scriptid="tab_78_t1516212_313">
      <tabparent></tabparent>
      <tabtitle>Sessions</tabtitle>
    </subtab>
  </subtabs>
  <recordsublists>
    <recordsublist scriptid="sublist_10_4132987_298">
      <recorddescr>Orders</recorddescr>
      <recordfield>[scriptid=customrecord_show.custrecord_show_job]</recordfield>
      <recordsearch>[bundleid=176152|176212|243192, scriptid=customsearch_ng_cs_show_job_orders]</recordsearch>
      <recordtab>[scriptid=customrecord_show.tab_16_4132987_329]</recordtab>
    </recordsublist>
  </recordsublists>
</customrecordtype>