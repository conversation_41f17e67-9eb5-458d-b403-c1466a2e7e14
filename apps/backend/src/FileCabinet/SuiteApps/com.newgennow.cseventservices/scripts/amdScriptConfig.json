{"baseUrl": "../../packages", "paths": {"logger": "./@logger/ng_cross_env_logger.js", "settings": "./@settings/ng_cses_cm_settings_hook.js", "lodash": "./@lodash/lodash.min.js", "prices": "./@prices/ng_server_cm_price_hooks.js", "moment": "./@moment/moment.min.js", "crypto-js": "./@crypto/crypto-js/crypto-js.js", "NG/tools/two": "../scripts/lib/newgen.library.v21.js", "NG/client": "../scripts/lib/newgen.library.cs.21.js", "NG/tools/es5": "../scripts/lib/newgen.library.v2.js", "NG/client/es5": "../scripts/lib/newgen.library.cs.js", "NG/pt/es5": "../scripts/lib/newgen.paytrace.lib.js", "NG/rental/es5": "../scripts/lib/newgen.ri.library.v2.js"}}