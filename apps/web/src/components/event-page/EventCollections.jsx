import React, { memo } from "react";
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  ImageList,
  Skeleton,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import Scrollbar from "components/Scrollbar";
import CollectionCard from "components/containers/CollectionCard";
import noCategoryImage from "../../../public/assets/images/categories/no-category-image.jpg";

const montserrat = Montserrat({
  weight: ["100", "200", "300", "400", "500", "600", "800", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

const EventCollections = memo(({ data, mobile }) => (
  <Grid container display="flex" flexDirection="column" spacing={2}>
    <Grid item xs={12}>
      <Box
        sx={{
          pt: 3,
          pl: 2,
          height: 800,
        }}
      >
        <Scrollbar>
          <ImageList gap={3} cols={2}>
            {!data ? (
              <Skeleton variant="rect" />
            ) : data?.collections && data?.collections.length !== 0 ? (
              data.collections.map((col, i) => {
                return (
                  <CollectionCard
                    key={col.id}
                    to={`/collection/${col.id}`}
                    sx={{ width: 300, height: 300 }}
                    image={col.image ? col.image : noCategoryImage}
                    title={col?.name}
                  />
                );
              })
            ) : (
              <div>
                <Box
                  display="flex"
                  flexDirection="row"
                  className="pt-5"
                  style={{
                    display: mobile && "table-cell",
                  }}
                >
                  {mobile ? (
                    <Typography
                      style={{
                        fontFamily: montserrat.style.fontFamily,
                      }}
                      variant="h5"
                    >
                      No collections found on event
                    </Typography>
                  ) : (
                    <Card
                      className="text-center float-left"
                      style={{ marginTop: "4rem" }}
                    >
                      <CardContent>
                        <Typography
                          style={{
                            fontFamily: montserrat.style.fontFamily,
                          }}
                          variant="h5"
                        >
                          No collections found on event
                        </Typography>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </div>
            )}
          </ImageList>
        </Scrollbar>
      </Box>
    </Grid>
  </Grid>
));

EventCollections.displayName = "EventCollections";

export default EventCollections; 