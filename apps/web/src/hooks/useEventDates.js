import { useMemo } from 'react';
import moment from 'moment';
import { useSettings } from '../store/zSettingsStore';

export const useEventDates = (eventData) => {
  const { settings } = useSettings();

  const { startDate, endDate } = useMemo(() => {
    if (!eventData?.eventDates || !settings?.custrecord_ng_cs_default_show_date) {
      return { startDate: '', endDate: '' };
    }

    const eventDates = eventData.eventDates
      .filter((date) => {
        if (settings) {
          return (
            date.dateType.value ===
            settings.custrecord_ng_cs_default_show_date[0].value
          );
        }
        return false;
      })
      .map((result) => {
        const eventDateObj = new Date(result.date);
        const eventYear = eventDateObj.getFullYear();
        const eventMonth = eventDateObj.getMonth();
        const eventDay = eventDateObj.getDate() + 1;
        return Date.UTC(eventYear, eventMonth, eventDay);
      });

    if (eventDates.length === 0) {
      return { startDate: '', endDate: '' };
    }

    const eventStartDate = Math.min(...eventDates);
    const formattedStartDate = moment(eventStartDate).format('MM/DD/YYYY');
    const eventEndDate = Math.max(...eventDates);
    const formattedEndDate = moment(eventEndDate).format('MM/DD/YYYY');

    return { startDate: formattedStartDate, endDate: formattedEndDate };
  }, [eventData, settings]);

  return { startDate, endDate };
}; 