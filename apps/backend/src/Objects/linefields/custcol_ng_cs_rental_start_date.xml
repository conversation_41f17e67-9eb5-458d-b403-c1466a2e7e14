<transactioncolumncustomfield scriptid="custcol_ng_cs_rental_start_date">
  <accesslevel>2</accesslevel>
  <applyformatting>F</applyformatting>
  <colexpense>F</colexpense>
  <colexpensereport>F</colexpensereport>
  <colgrouponinvoices>F</colgrouponinvoices>
  <colinventoryadjustment>F</colinventoryadjustment>
  <colitemfulfillment>F</colitemfulfillment>
  <colitemfulfillmentorder>F</colitemfulfillmentorder>
  <colitemreceipt>F</colitemreceipt>
  <colitemreceiptorder>F</colitemreceiptorder>
  <coljournal>F</coljournal>
  <colkititem>F</colkititem>
  <colopportunity>F</colopportunity>
  <colpackingslip>F</colpackingslip>
  <colpickingticket>F</colpickingticket>
  <colprintflag>F</colprintflag>
  <colpurchase>F</colpurchase>
  <colreturnform>F</colreturnform>
  <colsale>T</colsale>
  <colstore>F</colstore>
  <colstorehidden>F</colstorehidden>
  <colstorewithgroups>F</colstorewithgroups>
  <coltime>F</coltime>
  <coltransferorder>F</coltransferorder>
  <columncustomtransactions></columncustomtransactions>
  <defaultchecked>F</defaultchecked>
  <defaultselection></defaultselection>
  <defaultvalue></defaultvalue>
  <description></description>
  <displayheight></displayheight>
  <displaytype>NORMAL</displaytype>
  <displaywidth></displaywidth>
  <dynamicdefault></dynamicdefault>
  <encryptatrest>F</encryptatrest>
  <fieldtype>DATE</fieldtype>
  <help></help>
  <isformula>F</isformula>
  <ismandatory>F</ismandatory>
  <label>Rental Start Date</label>
  <linktext></linktext>
  <maxlength></maxlength>
  <maxvalue></maxvalue>
  <minvalue></minvalue>
  <onparentdelete></onparentdelete>
  <searchlevel>2</searchlevel>
  <selectrecordtype></selectrecordtype>
  <showhierarchy>F</showhierarchy>
  <sourcefilterby></sourcefilterby>
  <sourcefrom></sourcefrom>
  <sourcelist></sourcelist>
  <storevalue>T</storevalue>
</transactioncolumncustomfield>