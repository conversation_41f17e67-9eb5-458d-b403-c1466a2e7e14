import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { v4 as uuidv4 } from "uuid"
import type { Booking, Room } from "@/src/types/calendar"
// No longer need useCalendar here
// import { useCalendar } from "src/context/calendar-context"

export interface CartItem {
  id: string
  booking: Booking & {
    rateTypeId?: string
    rateId?: string
    rate?: number
  }
  room: Room
  addedAt: Date
  ghostId?: string
}

interface BookingCartState {
  items: CartItem[]
  isOpen: boolean
  addItem: (item: CartItem) => void
  updateItem: (itemId: string, updates: Partial<Booking>) => void
  removeItem: (itemId: string) => void
  clearCart: () => void
  setItems: (items: CartItem[]) => void
  setCartOpen: (open: boolean) => void
  toggleCart: () => void
}

// Helper function to ensure a date is valid
function ensureValidDate(date: any): Date {
  if (date instanceof Date && !isNaN(date.getTime())) {
    return date
  }

  // Try to parse string dates
  if (typeof date === "string") {
    const parsed = new Date(date)
    if (!isNaN(parsed.getTime())) {
      return parsed
    }
  }

  // Default to current time if invalid
  return new Date()
}

export const useBookingCart = create<BookingCartState>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      addItem: (item) => {
        // This function should only add an item to the state
        set((state) => ({
          items: [...state.items, item],
          isOpen: true, // Automatically open the cart when an item is added
        }))
      },
      updateItem: (itemId, updates) => {
        // This function should only update an item in state.
        // The component will handle updating the ghost booking.
        set((state) => ({
          items: state.items.map((item) =>
            item.id === itemId ? { ...item, booking: { ...item.booking, ...updates } } : item,
          ),
        }))
      },
      removeItem: (itemId) => {
        // This function should only remove an item from state.
        // The component will handle deleting the ghost booking.
        set((state) => ({
          items: state.items.filter((item) => item.id !== itemId),
        }))
      },
      clearCart: () => {
        // This function should only clear the items from state.
        // The component will handle deleting the ghost bookings.
        set({ items: [] })
      },
      toggleCart: () => set((state) => ({ isOpen: !state.isOpen })),
      setCartOpen: (open: boolean) => set({ isOpen: open }),
      setItems: (items) => set({ items }),
    }),
    {
      name: "booking-cart-storage",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
)

// Function to initialize the store from localStorage on client side
export function hydrateBookingCart() {
  if (typeof window !== "undefined") {
    try {
      // First rehydrate the store
      useBookingCart.persist.rehydrate();
      
      // Then ensure all date strings are converted to Date objects
      const currentState = useBookingCart.getState();
      
      if (currentState.items.length > 0) {
        // Convert date strings to Date objects
        const itemsWithDates = currentState.items.map(item => ({
          ...item,
          booking: {
            ...item.booking,
            start: typeof item.booking.start === 'string' ? new Date(item.booking.start) : 
                  item.booking.start instanceof Date ? item.booking.start : new Date(),
            end: typeof item.booking.end === 'string' ? new Date(item.booking.end) : 
                item.booking.end instanceof Date ? item.booking.end : 
                new Date(typeof item.booking.start === 'string' ? new Date(item.booking.start).getTime() + 3600000 : 
                        item.booking.start instanceof Date ? item.booking.start.getTime() + 3600000 : 
                        new Date().getTime() + 3600000),
          },
          addedAt: typeof item.addedAt === 'string' ? new Date(item.addedAt) : 
                  item.addedAt instanceof Date ? item.addedAt : new Date()
        }));
        
        // Update the state with properly formatted dates
        useBookingCart.setState({ items: itemsWithDates });
      }
      
      console.log("Cart hydrated with", useBookingCart.getState().items.length, "items");
    } catch (error) {
      console.error("Error hydrating cart:", error);
    }
  }
}

