/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       07 Nov 2018     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){
	var doPrint = request.getParameter("prt") == "T";
	if (request.getMethod() == "GET") {
		if (doPrint) {
			doWork(request, response);
		}
		else {
			displayForm(request, response);
		}
	}
	else {
		response.write("INVALID_HTTP_REQUEST");
	}
}

function displayForm(request, response) {
	var context = nlapiGetContext();
	
	var form = nlapiCreateForm("Delivery By Product");
	var showsField = form.add<PERSON>("custpage_show", "multiselect", "Event");
	_scLib.getOpenShows(showsField, true);
	var subCatField = form.addField("custpage_sub_category", "multiselect", "Item Subcategory");
	var subCatList = data_GetSubcategories();
	for (var sc = 0; sc < subCatList.length; sc++) {
		subCatField.addSelectOption(subCatList[sc].id, subCatList[sc].value);
	}
	subCatField.setBreakType("startcol");

	var groupByField = form.addField("custpage_group_by", "select", "Group By");
	groupByField.addSelectOption("", "");
	groupByField.addSelectOption("A", "Event");
	groupByField.addSelectOption("B", "Location");
	groupByField.addSelectOption("C", "Booth Type");
	groupByField.defaultValue = "A";
	groupByField.setMandatory(true);

	var sortField = form.addField("custpage_sort", "select", "Sort By");
	sortField.addSelectOption("", "");
	sortField.addSelectOption("A", "Booth", true);
	sortField.addSelectOption("B", "Exhibitor");

	form
		.addField(
			"custpage_filter_label",
			"label",
			"Filter By"
		)

	form.addField(
		"custpage_filter_location",
		"multiselect",
		"Booth Location"
	)

	form.addField(
		"custpage_filter_pavilion",
		"multiselect",
		"Booth Pavilion"
	)

	form.addField("custpage_url", "text", "").setDisplayType("hidden").setDefaultValue(nlapiResolveURL("SUITELET", context.getScriptId(), context.getDeploymentId()));
	form.addField("custpage_page_break_after_cat", "checkbox", "Page Break Between Categories");
	form.addField("custpage_show_item_categories", "checkbox", "Show Item Descriptions");
	
	addTimeZoneOffsetHTML(form);
	
	form.addButton("custpage_reset_button", "Reset", "resetPage()");
	form.addButton("custpage_print_button", "Print", "printPage()");
	form.setScript("customscript_ng_cs_cl_dlvry_by_prod_rprt");
	response.writePage(form);
}

function doWork(request, response) {
	var shows = request.getParameter("shs").split(";");
	var subCat = !_tools.isEmpty(request.getParameter("sct")) ? request.getParameter("sct").split(";") : "";
	var sType = request.getParameter("srt") || "A";
	var gType = request.getParameter("grp") || "A";
	var doPageBreak = request.getParameter("brk") == "T";
	var showItemCategories = request.getParameter("shwitemcat")
	var boothLocParam = request.getParameter("locfilt");
	var boothPavParam = request.getParameter("pavfilt");
	var boothLocationFilter = [];
	var boothPavilionFilter = [];
	var tables = [];

	if (boothLocParam) {
		boothLocationFilter = request.getParameter("locfilt").split('\u0005');
	}
	if (boothPavParam) {
		boothPavilionFilter = request.getParameter("pavfilt").split('\u0005');
	}

	_log.logInfo("Item Cats", showItemCategories);
	
	var printDate = setOffsetPrintDate(request);
	
	var filt1 = [
				["internalid","anyof",shows]
		],
		cols1 = [
				new nlobjSearchColumn("name", null, null)
			,	new nlobjSearchColumn("custrecord_facility", null, null)
		],
		showResults;
	
	try {
		showResults = _tools.getSearchResults(nlapiCreateSearch("customrecord_show", filt1, cols1).runSearch(), false);
	}
	catch (err) {
		_log.logError(err, "Error encountered getting init show data");
	}
	
	if (!_tools.isEmpty(showResults)) {
		for (var s = 0; s < showResults.length; s++) {
			if (tables.findIndex(function(x) { return x == showResults[s].getId(); }) < 0) {
				tables.push(showResults[s].getId());
			}
		}
		
		var orderResults
		try {
			orderResults = data_GetOrderData({
					eventIdList : tables
				,	boothOrdersOnly : true
				,	filterOutDescrLines : true
				,	subCategories : subCat
			});
		}
		catch (err) {
			_log.logError(err, "Error encountered retrieving event order data for reporting");
		}

		nlapiLogExecution("AUDIT", "orderResults", JSON.stringify(orderResults));
		
		if (!_tools.isEmpty(orderResults)) {
			var showObjList = [],
				orderObjList = [],
				categoryMap = { },
				subCategoryMap = { },
				itemList = [],
				boothLocationList = [],
				boothTypeList = [];
			
			for (var r = 0; r < orderResults.length; r++) {
				itemList.push(orderResults[r].getValue(nlobjSearchColumn("item", null, null)));
			}
			var stockItemData = data_GetStockItems(itemList);
			
			nlapiLogExecution("AUDIT", "orderResults count", orderResults.length.toFixed(0));
			
			for (var i = 0; i < showResults.length; i++) {
				var objS = {
						id : showResults[i].getId()
					,	name : nlapiEscapeXML(showResults[i].getValue(nlobjSearchColumn("name", null, null)))
					,	table_id : showResults[i].getId()
					,	facility : showResults[i].getValue(nlobjSearchColumn("custrecord_facility", null, null))
					,	facility_text : nlapiEscapeXML(showResults[i].getText(nlobjSearchColumn("custrecord_facility", null, null)))
				};
				showObjList.push(objS);
			}
			
			for (var o = 0; o < orderResults.length; o++) {
				var order = orderResults[o];
				if (order.getValue(nlobjSearchColumn("itemid", "item", null)) == "End of Group") {
					continue;
				}
				else if ((Number(order.getValue("quantity"))) <= 0) {
					continue;
				}
				var tableId = order.getValue("custbody_show_table");
				
				var catId = order.getValue(nlobjSearchColumn("custitem_item_category", "item", null)),
					catText = nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem_item_category", "item", null)) || "N/A"),
					subCatId = order.getValue(nlobjSearchColumn("custitem_subcategory", "item", null)) || "",
					subCatIdAlt = "cat_{0}".NG_Format(catId),
					subCatText = nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem_subcategory", "item", null)) || order.getText(nlobjSearchColumn("custitem_item_category", "item", null)) || "N/A");
				
				if (!_tools.isEmpty(catId)) {
					if (_tools.isEmpty(categoryMap[catId])) {
						categoryMap[catId] = {
								id : catId
							,	text : catText
						}
					}
					var sCatKey = "{0}:{1}".NG_Format(catId, (subCatId || subCatIdAlt));
					if (!_tools.isEmpty(subCatId) && _tools.isEmpty(subCategoryMap[subCatId])) {
						subCategoryMap[sCatKey] = {
								id : subCatId
							,	text : subCatText
							,	cat : catId
						}
					}
					else if (_tools.isEmpty(subCatId) && _tools.isEmpty(subCategoryMap[subCatIdAlt])) {
						subCategoryMap[sCatKey] = {
								id : subCatIdAlt
							,	text : catText
							,	cat : catId
						}
					}
				}
				else {
					if (_tools.isEmpty(categoryMap["UNKNOWN"])) {
						categoryMap["UNKNOWN"] = {
								id : "UNKNOWN"
							,	text : "N/A"
						}
						
						subCategoryMap["UNKNOWN"] = {
								id : "UNKNOWN"
							,	text : "N/A"
							,	cat : "UNKNOWN"
						}
					}
				}

				try {
					var currBoothLocation = order.getValue(nlobjSearchColumn("custrecord_booth_location", "custbody_booth", null))
					var currBoothType = order.getText(nlobjSearchColumn("custrecord_booth_type", "custbody_booth", null))

					if (boothLocationList.indexOf(currBoothLocation) === -1 && !_tools.isEmpty(currBoothLocation)) {
						boothLocationList.push(currBoothLocation)
					}

					if (boothTypeList.indexOf(currBoothType) === -1 && !_tools.isEmpty(currBoothType)) {
						boothTypeList.push(currBoothType)
					}

				} catch (e) {
					nlapiLogExecution("AUDIT", "Error Getting Booth Location & Booth type", e);
				}
				
				var itemId = order.getValue("item"),
					qty = Number(order.getValue("quantity")),
					boothId = order.getValue("custbody_booth");
				
				if (!_tools.isEmpty(stockItemData[itemId]) && (stockItemData[itemId] || []).length > 0) {
					for (var d = 0; d < stockItemData[itemId].length; d++) {
						var stockId = stockItemData[itemId][d]['stockId'],
							stockCat = stockItemData[itemId][d]['catKey'],
							stockName = stockItemData[itemId][d]['stockName'],
							stockDisplayName = stockItemData[itemId][d]['displayName'],
							stockColorId = stockItemData[itemId][d]['colorId'],
							stockColor = stockItemData[itemId][d]['colorName'],
							stockSizeId = stockItemData[itemId][d]['sizeId'],
							stockSize = stockItemData[itemId][d]['sizeName'],
							stockOrientId = stockItemData[itemId][d]['orientId'],
							stockSubstrId = stockItemData[itemId][d]['substrId'],
							stockIsMatrix = !_tools.isEmpty(stockColor) || !_tools.isEmpty(stockSize),
							stockParent = stockItemData[itemId][d]['parentName'],
							stockParentId = stockItemData[itemId][d]['parentId'],
							stockRef = !_tools.isEmpty(stockParentId) && stockIsMatrix ? stockParentId : stockId,
							stockRefName = !_tools.isEmpty(stockParentId) && stockIsMatrix ? stockParent : stockName,
							stockQty = stockItemData[itemId][d]['qty'],
							stockExtQty = _M.roundToHundredths(qty * stockQty);
						
						var colorKey = !_tools.isEmpty(stockColorId) ? "-{0}".NG_Format(stockColorId) : "",
							sizeKey = !_tools.isEmpty(stockSizeId) ? "-{0}".NG_Format(stockSizeId) : "",
							orientKey = !_tools.isEmpty(stockOrientId) ? "-{0}".NG_Format(stockOrientId) : "",
							substrKey = !_tools.isEmpty(stockSubstrId) ? "-{0}".NG_Format(stockSubstrId) : "",
							stockKey = "{0}-{1}-{2}{3}{4}{5}{6}".NG_Format(boothId, stockRef, stockCat, colorKey, sizeKey, orientKey, substrKey),
							stockFullName = "{0}{1}{2}{3}".NG_Format(
									stockRefName
								,	!_tools.isEmpty(stockDisplayName) ? " <i>{0}</i>".NG_Format(stockDisplayName) : ""
								,	!_tools.isEmpty(stockColor) ? " <b>Color:</b> {0}".NG_Format(stockColor) : ""
								,	!_tools.isEmpty(stockSize) ? " <b>Size:</b> {0}".NG_Format(stockSize) : ""
							);
						
						var stockIndex = orderObjList.findIndex(function(x) { return x.stockKey == stockKey; });
						if (stockIndex >= 0) {
							orderObjList[stockIndex].quantity = _M.roundToHundredths(orderObjList[stockIndex].quantity + stockExtQty);
						}
						else {
							var stockObj = {
									item_id : stockId
								,	item_name : stockFullName
								,	quantity : stockExtQty
								,	cat_id : stockItemData[itemId][d]['catId'] || "UNKNOWN"
								,	cat_text :nlapiEscapeXML(stockItemData[itemId][d]['catName'] || "N/A")
								,	sub_cat_id : (stockItemData[itemId][d]['subCatId'] != "@NONE@" ? stockItemData[itemId][d]['subCatId'] : "") || (!_tools.isEmpty(stockItemData[itemId][d]['catId']) ? "cat_{0}".NG_Format(stockItemData[itemId][d]['catId']) : "UNKNOWN")
								,	sub_cat_text : nlapiEscapeXML(stockItemData[itemId][d]['subCatName'] || stockItemData[itemId][d]['catKey'] || "N/A")
								,	table_id : tableId
								,	color : stockColor
								,	size : stockSize
								,	orientation : stockItemData[itemId][d]['orientName']
								,	substrate : stockItemData[itemId][d]['substrName']
								,	booth_id : boothId
								,	booth_number : nlapiEscapeXML(order.getValue(nlobjSearchColumn("custrecord_booth_number", "custbody_booth", null)))
								, booth_location : order.getValue(nlobjSearchColumn("custrecord_booth_location", "custbody_booth", null))
								, booth_pavilion : order.getValue(nlobjSearchColumn("custrecord_booth_pavilion", "custbody_booth", null))
								, booth_type : order.getText(nlobjSearchColumn("custrecord_booth_type", "custbody_booth", null))
								,	booth_sort : order.getValue(nlobjSearchColumn("custrecord_sortboothnum", "custbody_booth", null)) || (order.getValue(nlobjSearchColumn("custrecord_booth_number", "custbody_booth", null)) || "").NG_paddingRight("000000000000000")
								,	exhib_id : order.getValue("custbody_booth_actual_exhibitor")
								,	exhib_name : nlapiEscapeXML(order.getValue(nlobjSearchColumn("companyname", "custbody_booth_actual_exhibitor", null)))
								,	contact_id : order.getValue(nlobjSearchColumn("custrecord_booth_contact", "custbody_booth", null))
								,	stockKey : stockKey
								,   description : order.getValue(nlobjSearchColumn("custcol_description", null, null))
							};
							orderObjList.push(stockObj);
						}
					}
				}
				else {
					var obj = {
								item_id : itemId
							,	item_name : nlapiEscapeXML(itemNameDisplay(order.getValue(nlobjSearchColumn("itemid", "item", null)), order.getValue(nlobjSearchColumn("displayname", "item", null))))
							,	quantity : qty
							,	cat_id : catId || "UNKNOWN"
							,	cat_text : nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem_item_category", "item", null)) || "N/A")
							,	sub_cat_id : order.getValue(nlobjSearchColumn("custitem_subcategory", "item", null)) || (!_tools.isEmpty(catId) ? "cat_{0}".NG_Format(catId) : "UNKNOWN")
							,	sub_cat_text : nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem_subcategory", "item", null)) || order.getText(nlobjSearchColumn("custitem_item_category", "item", null)) || "N/A")
							,	table_id : tableId
							,	color : nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem27", "item", null)) || "")
							,	size : nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem28", "item", null)) || "")
							,	orientation : nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem_orientation", "item", null)) || "")
							,	substrate : nlapiEscapeXML(order.getText(nlobjSearchColumn("custitem42", "item", null)) || "")
							,	booth_id : boothId
							,	booth_number : nlapiEscapeXML(order.getValue(nlobjSearchColumn("custrecord_booth_number", "custbody_booth", null)))
					  	, booth_location : order.getValue(nlobjSearchColumn("custrecord_booth_location", "custbody_booth", null))
					  	, booth_pavilion : order.getValue(nlobjSearchColumn("custrecord_booth_pavilion", "custbody_booth", null))
					  	, booth_type : order.getText(nlobjSearchColumn("custrecord_booth_type", "custbody_booth", null) )
							,	booth_sort : order.getValue(nlobjSearchColumn("custrecord_sortboothnum", "custbody_booth", null))
							,	exhib_id : order.getValue("custbody_booth_actual_exhibitor")
							,	exhib_name : nlapiEscapeXML(order.getValue(nlobjSearchColumn("companyname", "custbody_booth_actual_exhibitor", null)))
						    ,   description : order.getValue(nlobjSearchColumn("custcol_description", null, null))
						};
					orderObjList.push(obj);
				}
			}

			nlapiLogExecution("AUDIT", "orderObjList", JSON.stringify(orderObjList));

			var categoryMapList = [];
			for (var map in categoryMap) {
				categoryMapList.push(categoryMap[map]);
			}
			categoryMapList = _tools.objectSort(categoryMapList, "text");
			var subCategoryMapList = [];
			for (var map in subCategoryMap) {
				subCategoryMapList.push(subCategoryMap[map]);
			}
			subCategoryMapList = _tools.objectSort(subCategoryMapList, "text");
			
			var xml = "";
			xml += '<?xml version="1.0"?>';
			xml += '<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">';
			xml += '<pdfset>';
			
			nlapiLogExecution("AUDIT", "showObjList count", showObjList.length.toFixed(0));
			nlapiLogExecution("AUDIT", "showObjList", JSON.stringify(showObjList));

			// var groupedOrders = groupOrders(orderObjList, gType);
			//
			// nlapiLogExecution("AUDIT", "groupedOrders", JSON.stringify(groupedOrders));

			// Object.keys(groupedOrders).forEach(function(boothKey) {
			// 	var ordersInGroup = groupedOrders[boothKey]; // Get orders for this booth location/type
			// 	var exhibIdList = [];
			// 	var itemGrouping = [];
			//
			// 	// Iterate over orders and group them by item_id and exhib_id
			// 	for (var id = 0; id < ordersInGroup.length; id++) {
			// 		var order = ordersInGroup[id];
			//
			// 		// Ensure exhib_id is added to the exhibIdList only once
			// 		if (!_tools.isInArray(order.exhib_id, exhibIdList)) {
			// 			exhibIdList.push(order.exhib_id);
			// 		}
			//
			// 		// Group items by item_id
			// 		var index = itemGrouping.NG_findIndexAlt("item_id", order.item_id);
			// 		if (index < 0) {
			// 			itemGrouping.push({
			// 				item_id: order.item_id,
			// 				item_name: order.item_name,
			// 				cat_id: order.cat_id,
			// 				sub_cat_id: order.sub_cat_id,
			// 				data: []
			// 			});
			// 			index = itemGrouping.NG_findIndexAlt("item_id", order.item_id);
			// 		}
			// 		itemGrouping[index].data.push(order);
			// 	}
			//
			// 	var exhibCount = Math.round(exhibIdList.length);
			//
			// 	// Initialize displayData to simulate structure similar to showObjList
			// 	var displayData = {};
			// 	var showObj;
			// 	var header;
			//
			// 	if (gType == 'A') {
			// 		showObj = showObjList.filter(function(show) {
			// 			return show.table_id === boothKey
			// 		})
			//
			// 		displayData[boothKey] = showObj[0];
			//
			// 		// Get data through functions (e.g., data_GetShowTables, data_GetShowDates)
			// 		displayData = data_GetShowDates(data_GetShowTables(displayData));
			// 		var tableData = displayData[boothKey];
			//
			// 		// Generate the page header
			// 		header = xml_PageHeader(tableData, "Delivery By Product")
			// 			.replace("{ADDITIONAL_A}", "")
			// 			.replace("{ADDITIONAL_B}", "");
			// 	} else {
			// 		header = xml_boothTypePageHeader(boothKey, gType)
			// 	}
			//
			// 	// Start the XML content
			// 	var xmlA = "";
			// 	xmlA += '<pdf>\n';
			// 	xmlA += '<head>\n';
			// 	xmlA += '<macrolist>{0}{1}</macrolist>\n'.NG_Format(header, xml_PageFooter(printDate));
			// 	xmlA += xml_StyleSheet();
			// 	xmlA += '</head>\n';
			// 	xmlA += _BODY_XML;
			//
			// 	// Add table with total exhibitor count
			// 	xmlA += '<table table-layout="fixed" border="0" style="width:720px; margin-top:10px; border-bottom: 1px black;" cellmargin="0" cellpadding="0" class="listing">\n';
			// 	xmlA += '<tr><td width="65%" align="left"><b>Total Report Exhibitors: {0} {1}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;">&nbsp;</p></td></tr>\n'.NG_Format(exhibCount.toFixed(0), boothKey);
			// 	xmlA += '</table>\n';
			//
			// 	var catCount = 0;
			//
			// 	// Process each category from categoryMapList
			// 	for (var cm = 0; cm < categoryMapList.length; cm++) {
			// 		var cMap = categoryMapList[cm];
			// 		var subCats = subCategoryMapList.filter(function(x) { return x.cat == cMap.id; });
			// 		var sCatList = subCats.map(function(sc) { return sc.id; });
			//
			// 		// Find items in the itemGrouping that match the category map
			// 		var group = itemGrouping.filter(function(x) { return _tools.isInArray(x.sub_cat_id, sCatList) && x.cat_id == cMap.id; });
			//
			// 		if (group.length > 0) {
			// 			if (doPageBreak && catCount > 0) {
			// 				xmlA += '<pbr />\n';
			// 			}
			// 			catCount++;
			//
			// 			var subCatCount = 0;
			// 			// Process each sub-category
			// 			for (var sCat = 0; sCat < subCats.length; sCat++) {
			// 				var sCatItems = group.filter(function(x) { return x.sub_cat_id == subCats[sCat].id; });
			//
			// 				if (sCatItems.length > 0) {
			// 					var dataList = [];
			// 					var scExhibIdList = [];
			//
			// 					// Process each item in the sub-category
			// 					for (var sc = 0; sc < sCatItems.length; sc++) {
			// 						var scItem = sCatItems[sc];
			//
			// 						// Sorting based on type
			// 						if (sType == "A") {
			// 							scItem.data = scItem.data.sort(SortByBooth);
			// 						} else if (sType === 'B') {
			// 							scItem.data = _tools.objectSort(scItem.data, "exhib_name");
			// 						} else if (sType === 'C') {
			// 							scItem.data = _tools.objectSort(scItem.data, "booth_location");
			// 						} else if (sType === 'D') {
			// 							scItem.data = _tools.objectSort(scItem.data, "booth_pavilion");
			// 						} else if (sType === 'E') {
			// 							scItem.data = _tools.objectSort(scItem.data, "booth_type");
			// 						}
			//
			// 						// Filter and accumulate booth data
			// 						for (var d = 0; d < scItem.data.length; d++) {
			// 							var boothLocation = scItem.data[d].booth_location;
			// 							var boothPavilion = scItem.data[d].booth_pavilion;
			//
			// 							// Filter based on location and pavilion if set
			// 							if (boothLocationFilter && boothPavilionFilter) {
			// 								if (boothLocation !== boothLocationFilter && boothPavilion !== boothPavilionFilter) {
			// 									continue;
			// 								}
			// 							} else if (boothPavilionFilter && boothPavilion !== boothPavilionFilter) {
			// 								continue;
			// 							} else if (boothLocationFilter && boothLocation !== boothLocationFilter) {
			// 								continue;
			// 							}
			//
			// 							if (!_tools.isInArray(scItem.data[d].exhib_id, scExhibIdList)) {
			// 								scExhibIdList.push(scItem.data[d].exhib_id);
			// 							}
			//
			// 							// Add item to dataList or update the existing one
			// 							var idxA = dataList.NG_findIndexAlt("item_id", scItem.data[d].item_id);
			// 							if (idxA < 0) {
			// 								dataList.push({
			// 									item_id: scItem.data[d].item_id,
			// 									item_name: scItem.data[d].item_name,
			// 									total: Number(scItem.data[d].quantity),
			// 									list: [scItem.data[d]]
			// 								});
			// 							} else {
			// 								var idxB = dataList[idxA].list.NG_findIndexAlt("booth_id", scItem.data[d].booth_id);
			// 								if (idxB < 0) {
			// 									dataList[idxA].list.push(scItem.data[d]);
			// 								} else {
			// 									dataList[idxA].list[idxB].quantity = _M.roundToHundredths(dataList[idxA].list[idxB].quantity + scItem.data[d].quantity);
			// 								}
			// 								dataList[idxA].total = _M.roundToHundredths(dataList[idxA].total + scItem.data[d].quantity);
			// 							}
			// 						}
			// 					}
			//
			// 					var scExhibCount = Math.round(scExhibIdList.length);
			// 					var scRatio = _M.roundToHundredths((scExhibCount / exhibCount) * 100);
			// 					var topBorder = subCatCount > 0 ? "border-top:2px black;" : "";
			// 					if (subCatCount > 0) {
			// 						xmlA += '<pbr />\n';
			// 					}
			// 					subCatCount++;
			//
			// 					// Generate the sub-category table
			// 					xmlA += '<table table-layout="fixed" border="0" style="width:720px; margin-top:10px; border-bottom: 1px black;{0}" cellmargin="0" cellpadding="0" class="listing">\n'.NG_Format(topBorder);
			// 					xmlA += '<tr><td width="30%" align="left"><b>{0}</b></td><td width="35%" align="center"><b>{1}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;"><b>Ordered From This Category:<br />{2} ({3}%)</b></p></td></tr>\n'.NG_Format(
			// 						subCats[sCat].text, "&nbsp;", scExhibCount.toFixed(0), scRatio.toFixed(2)
			// 					);
			// 					xmlA += '</table>\n';
			//
			// 					for (var dl = 0; dl < dataList.length; dl++) {
			// 						xmlA += '<table table-layout="fixed" border="0" style="width:720px; margin-top:5px;" cellmargin="0" cellpadding="0" class="listing">\n';
			// 						xmlA += '<THEAD>\n';
			//
			// 						if (dl == 0) {
			// 							xmlA += '<tr><th colspan="10" align="left"><b><i>{0}</i></b></th><th colspan="2" align="center"><b>QTY</b></th><th colspan="2" align="center"><b>Booth</b></th><th colspan="2">&nbsp;</th></tr>\n'.NG_Format(
			// 								dataList[dl].item_name
			// 							);
			// 						}
			// 						else {
			// 							xmlA += '<tr><th colspan="10" align="left"><b><i>{0}</i></b></th><th colspan="6">&nbsp;</th></tr>\n'.NG_Format(
			// 								dataList[dl].item_name
			// 							);
			// 						}
			//
			// 						xmlA += '</THEAD>\n';
			// 						xmlA += '<TBODY>\n';
			//
			// 						var shaded = false;
			// 						for (var l = 0; l < dataList[dl].list.length; l++) {
			// 							var item = dataList[dl].list[l];
			// 							var style = ' style="padding-top: 3px; padding-bottom: 3px;{0}"'.NG_Format(shaded ? " background-color: {0};".NG_Format(_scLib.RptLineShadeHex || _SHADED_HEX) : "");
			// 							if (_scLib.ShadeAltRptLines) {
			// 								if (shaded) {
			// 									shaded = false;
			// 								}
			// 								else {
			// 									shaded = true;
			// 								}
			// 							}
			//
			// 							if (item.description) {
			// 								if (showItemCategories === "T") {
			// 									item.description = escapeXml(item.description)
			// 									xmlA += '<tr{0}><td colspan="10">&nbsp;&nbsp;&nbsp;&nbsp;{1}</td><td colspan="2" align="right">{2}</td><td colspan="2" align="right" style="word-break: break-all;">{3}</td><td colspan="2" align="center"><table style="margin-top: 5px; height: 10px; width: 15px; border: 1px; border-color: #000000; background-color: #ffffff;"><tr><td><span style="font-size:6pt;">&nbsp;</span></td></tr></table></td></tr>'.NG_Format(
			// 										style , item.exhib_name + ' ' + item.description , item.quantity.toFixed(2) , item.booth_number
			// 									);
			// 								} else {
			// 									xmlA += '<tr{0}><td colspan="10">&nbsp;&nbsp;&nbsp;&nbsp;{1}</td><td colspan="2" align="right">{2}</td><td colspan="2" align="right" style="word-break: break-all;">{3}</td><td colspan="2" align="center"><table style="margin-top: 5px; height: 10px; width: 15px; border: 1px; border-color: #000000; background-color: #ffffff;"><tr><td><span style="font-size:6pt;">&nbsp;</span></td></tr></table></td></tr>'.NG_Format(
			// 										style , item.exhib_name, item.quantity.toFixed(2) , item.booth_number
			// 									);
			// 								}
			// 							} else {
			// 								xmlA += '<tr{0}><td colspan="10">&nbsp;&nbsp;&nbsp;&nbsp;{1}</td><td colspan="2" align="right">{2}</td><td colspan="2" align="right" style="word-break: break-all;">{3}</td><td colspan="2" align="center"><table style="margin-top: 5px; height: 10px; width: 15px; border: 1px; border-color: #000000; background-color: #ffffff;"><tr><td><span style="font-size:6pt;">&nbsp;</span></td></tr></table></td></tr>'.NG_Format(
			// 									style , item.exhib_name, item.quantity.toFixed(2) , item.booth_number
			// 								);
			// 							}
			//
			// 						}
			//
			// 						xmlA += '<tr><td colspan="10" align="right"><b>Total:</b></td><td colspan="2" align="right">{0}</td><td colspan="4">&nbsp;</td></tr>'.NG_Format(
			// 							dataList[dl].total.toFixed(2)
			// 						);
			//
			// 						xmlA += '</TBODY>\n';
			// 						xmlA += '</table>\n';
			// 					}
			// 				}
			// 			}
			// 		}
			// 	}
			//
			// 	// End the XML document
			// 	xmlA += '</body></pdf>\n';
			// 	xml += xmlA;
			// });

			for (var s = 0; s < showObjList.length; s++) {
				var sObj = showObjList[s];
				var itemData = orderObjList.filter(function(x) { return x.table_id == sObj.table_id; });
				//
				// var exhibIdList = [];
				// var itemGrouping = [];
				// for (var id = 0; id < itemData.length; id++) {
				// 	if (!_tools.isInArray(itemData[id].exhib_id, exhibIdList)) {
				// 		exhibIdList.push(itemData[id].exhib_id);
				// 	}
				// 	var index = itemGrouping.NG_findIndexAlt("item_id", itemData[id].item_id);
				// 	if (index < 0) {
				// 		itemGrouping.push({
				// 				item_id : itemData[id].item_id
				// 			,	item_name : itemData[id].item_name
				// 			,	cat_id : itemData[id].cat_id
				// 			,	sub_cat_id : itemData[id].sub_cat_id
				// 			, booth_location : itemData[id].booth_location
				// 			, booth_type : itemData[i].booth_type
				// 			,	data : []
				// 		});
				// 		index = itemGrouping.NG_findIndexAlt("item_id", itemData[id].item_id);
				// 	}
				// 	itemGrouping[index].data.push(itemData[id]);
				// }
				//
				// nlapiLogExecution("AUDIT", "itemGrouping", JSON.stringify(itemGrouping));
				//
				// var exhibCount = Math.round(exhibIdList.length);
				//
				// var displayData = {};
				// displayData[sObj.id] = sObj;
				//
				// nlapiLogExecution("AUDIT", "displayData", JSON.stringify(displayData));
				//
				// displayData = data_GetShowDates(data_GetShowTables(displayData));
				// var tableData = displayData[sObj.id];
				// var header = xml_PageHeader(tableData, "Delivery By Product").replace("{ADDITIONAL_A}", "").replace("{ADDITIONAL_B}", "");
				//
				// var xmlA = "";
				// xmlA += '<pdf>\n';
				// xmlA += '<head>\n';
				// xmlA += '<macrolist>{0}{1}</macrolist>\n'.NG_Format(header,xml_PageFooter(printDate));
				// xmlA += xml_StyleSheet();
				// xmlA += '</head>\n';
				// xmlA += _BODY_XML;
				//
				// xmlA += '<table table-layout="fixed" border="0" style="width:720px; margin-top:10px; border-bottom: 1px black;{0}" cellmargin="0" cellpadding="0" class="listing">\n'.NG_Format(topBorder);
				// xmlA += '<tr><td width="65%" align="left"><b>Total Report Exhibitors: {0}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;">&nbsp;</p></td></tr>\n'.NG_Format(exhibCount.toFixed(0));
				// xmlA += '</table>\n';
				//
				// var catCount = Number(0);

				nlapiLogExecution("AUDIT", " 🟢 categoryMapList", JSON.stringify(categoryMapList));
				nlapiLogExecution("AUDIT", " 🟢 subCategoryMapList", JSON.stringify(subCategoryMapList));

				var groupedOrders = groupOrders(itemData, gType);

				nlapiLogExecution("AUDIT", " 🟢 groupedOrders", JSON.stringify(groupedOrders));

				Object.keys(groupedOrders).forEach(function(boothKey) {
					var ordersInGroup = groupedOrders[boothKey]; // Get orders for this booth location/type
					var exhibIdList = [];
					var itemGrouping = [];

					// Iterate over orders and group them by item_id and exhib_id
					for (var id = 0; id < ordersInGroup.length; id++) {
						var order = ordersInGroup[id];

						// Ensure exhib_id is added to the exhibIdList only once
						if (!_tools.isInArray(order.exhib_id, exhibIdList)) {
							exhibIdList.push(order.exhib_id);
						}

						// Group items by item_id
						var index = itemGrouping.NG_findIndexAlt("item_id", order.item_id);
						if (index < 0) {
							itemGrouping.push({
								item_id: order.item_id,
								item_name: order.item_name,
								cat_id: order.cat_id,
								cat_text: order.cat_text,
								sub_cat_id: order.sub_cat_id,
								data: []
							});
							index = itemGrouping.NG_findIndexAlt("item_id", order.item_id);
						}
						itemGrouping[index].data.push(order);
					}

					var exhibCount = Math.round(exhibIdList.length);

					// Initialize displayData to simulate structure similar to showObjList
					var displayData = {};
					displayData[sObj.id] = sObj;

					displayData = data_GetShowDates(data_GetShowTables(displayData));
					var tableData = displayData[sObj.id];
					var header = xml_PageHeader(tableData, "Delivery By Product").replace("{ADDITIONAL_A}", "").replace("{ADDITIONAL_B}", "");


					// if (gType == 'A') {
					// 	showObj = showObjList.filter(function(show) {
					// 		return show.table_id === boothKey
					// 	})
					//
					// 	displayData[boothKey] = showObj[0];
					//
					// 	// Get data through functions (e.g., data_GetShowTables, data_GetShowDates)
					// 	displayData = data_GetShowDates(data_GetShowTables(displayData));
					// 	var tableData = displayData[boothKey];
					//
					// 	// Generate the page header
					// 	header = xml_PageHeader(tableData, "Delivery By Product")
					// 		.replace("{ADDITIONAL_A}", "")
					// 		.replace("{ADDITIONAL_B}", "");
					// } else {
					// 	header = xml_boothTypePageHeader(boothKey, gType)
					// }

					// Start the XML content
					var xmlA = "";
					xmlA += '<pdf>\n';
					xmlA += '<head>\n';
					xmlA += '<macrolist>{0}{1}</macrolist>\n'.NG_Format(header, xml_PageFooter(printDate));
					xmlA += xml_StyleSheet();
					xmlA += '</head>\n';
					xmlA += _BODY_XML;

					// Add table with total exhibitor count
					xmlA += '<table table-layout="fixed" border="0" style="width:720px; margin-top:10px; border-bottom: 1px black; padding-bottom: 20px" cellmargin="0" cellpadding="0" class="listing">\n';

					if (gType === 'A') { // Event
						xmlA += '<tr><td width="65%" align="left"><b>Total Report Exhibitors: {0}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;">&nbsp;</p></td></tr>\n'.NG_Format(exhibCount.toFixed(0));
					} else if (gType === 'B') { // Booth Location
						xmlA += '<tr><td width="65%" align="left"><b>Booth Location: {1}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;"><b>Total Report Exhibitors: {0}</b></p></td></tr>\n'.NG_Format(exhibCount.toFixed(0), boothKey);
					} else { // Booth Type
						xmlA += '<tr><td width="65%" align="left"><b>Booth Type: {1}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;"><b>Total Report Exhibitors: {0}</b></p></td></tr>\n'.NG_Format(exhibCount.toFixed(0), boothKey);
					}

					xmlA += '</table>\n';

					var catCount = 0;

					// Process each category from categoryMapList
					for (var cm = 0; cm < categoryMapList.length; cm++) {
						var cMap = categoryMapList[cm];
						var subCats = subCategoryMapList.filter(function(x) { return x.cat == cMap.id; });

						nlapiLogExecution("AUDIT", " 🟢 subCats", JSON.stringify(subCats));

						var sCatList = subCats.map(function(sc) { return sc.id; });

						// Find items in the itemGrouping that match the category map
						var group = itemGrouping.filter(function(x) { return _tools.isInArray(x.sub_cat_id, sCatList) && x.cat_id == cMap.id; });

						if (group.length > 0) {
							if (doPageBreak && catCount > 0) {
								xmlA += '<pbr />\n';
							}
							catCount++;

							var subCatCount = 0;
							// Process each sub-category
							for (var sCat = 0; sCat < subCats.length; sCat++) {
								var sCatItems = group.filter(function(x) { return x.sub_cat_id == subCats[sCat].id; });

								if (sCatItems.length > 0) {
									var dataList = [];
									var scExhibIdList = [];

									// Process each item in the sub-category
									for (var sc = 0; sc < sCatItems.length; sc++) {
										var scItem = sCatItems[sc];

										nlapiLogExecution("AUDIT", " 🟢 scItem", JSON.stringify(scItem));

										// Sorting based on type
										if (sType == "A") {
											scItem.data = scItem.data.sort(SortByBooth);
										} else if (sType === 'B') {
											scItem.data = _tools.objectSort(scItem.data, "exhib_name");
										} else if (sType === 'C') {
											scItem.data = _tools.objectSort(scItem.data, "booth_location");
										} else if (sType === 'D') {
											scItem.data = _tools.objectSort(scItem.data, "booth_pavilion");
										} else if (sType === 'E') {
											scItem.data = _tools.objectSort(scItem.data, "booth_type");
										}

										// Filter and accumulate booth data
										for (var d = 0; d < scItem.data.length; d++) {
											var boothLocation = scItem.data[d].booth_location;
											var boothPavilion = scItem.data[d].booth_pavilion;

											// Filter based on location and pavilion if set
											if (
												(boothLocationFilter.length !== 0 || boothPavilionFilter.length !== 0) &&
												!(boothLocationFilter.indexOf(boothLocation) !== -1 || boothPavilionFilter.indexOf(boothPavilion) !== -1)
											) {
												continue;
											}

											if (!_tools.isInArray(scItem.data[d].exhib_id, scExhibIdList)) {
												scExhibIdList.push(scItem.data[d].exhib_id);
											}

											// Add item to dataList or update the existing one
											var idxA = dataList.NG_findIndexAlt("item_id", scItem.data[d].item_id);
											if (idxA < 0) {
												dataList.push({
													item_id: scItem.data[d].item_id,
													item_name: scItem.data[d].item_name,
													total: Number(scItem.data[d].quantity),
													list: [scItem.data[d]]
												});
											} else {
												var idxB = dataList[idxA].list.NG_findIndexAlt("booth_id", scItem.data[d].booth_id);
												if (idxB < 0) {
													dataList[idxA].list.push(scItem.data[d]);
												} else {
													dataList[idxA].list[idxB].quantity = _M.roundToHundredths(dataList[idxA].list[idxB].quantity + scItem.data[d].quantity);
												}
												dataList[idxA].total = _M.roundToHundredths(dataList[idxA].total + scItem.data[d].quantity);
											}
										}
									}

									var scExhibCount = Math.round(scExhibIdList.length);
									var scRatio = _M.roundToHundredths((scExhibCount / exhibCount) * 100);
									var topBorder = subCatCount > 0 ? "border-top:2px black;" : "";
									if (subCatCount > 0) {
										xmlA += '<pbr />\n';
									}
									subCatCount++;

									if (scExhibCount.toFixed(0) > 0) {
										// Generate the sub-category table
										xmlA += '<table table-layout="fixed" border="0" style="background-color:#DDDDDD; width:720px; margin-top:10px; padding-top: 10px; border-bottom: 1px black;{0}" cellmargin="0" cellpadding="0" class="listing">\n'.NG_Format(topBorder);
										xmlA += '<tr><td width="60%" align="left"><b>{4}: {0}</b></td><td width="5%" align="center"><b>{1}</b></td><td width="35%" align="center"><p style="width: 100%; text-align: center;"><b>Ordered From This Category:<br />{2} ({3}%)</b></p></td></tr>\n'.NG_Format(
											subCats[sCat].text, "&nbsp;", scExhibCount.toFixed(0), scRatio.toFixed(2), scItem.cat_text
										);
										xmlA += '</table>\n';
									}


									for (var dl = 0; dl < dataList.length; dl++) {
										xmlA += '<table table-layout="fixed" border="0" style="width:720px; margin-top:5px;" cellmargin="0" cellpadding="0" class="listing">\n';
										xmlA += '<THEAD>\n';

										if (dl == 0) {
											xmlA += '<tr><th colspan="10" align="left"><b><i>{0}</i></b></th><th colspan="2" align="center"><b>QTY</b></th><th colspan="2" align="center"><b>Booth</b></th><th colspan="2">&nbsp;</th></tr>\n'.NG_Format(
												dataList[dl].item_name
											);
										}
										else {
											xmlA += '<tr><th colspan="10" align="left"><b><i>{0}</i></b></th><th colspan="6">&nbsp;</th></tr>\n'.NG_Format(
												dataList[dl].item_name
											);
										}

										xmlA += '</THEAD>\n';
										xmlA += '<TBODY>\n';

										var shaded = false;
										for (var l = 0; l < dataList[dl].list.length; l++) {
											var item = dataList[dl].list[l];
											var style = ' style="padding-top: 3px; padding-bottom: 3px;{0}"'.NG_Format(shaded ? " background-color: {0};".NG_Format(_scLib.RptLineShadeHex || _SHADED_HEX) : "");
											if (_scLib.ShadeAltRptLines) {
												if (shaded) {
													shaded = false;
												}
												else {
													shaded = true;
												}
											}

											if (item.description) {
												if (showItemCategories === "T") {
													item.description = escapeXml(item.description)
													xmlA += '<tr{0}><td colspan="10">&nbsp;&nbsp;&nbsp;&nbsp;{1}</td><td colspan="2" align="right">{2}</td><td colspan="2" align="right" style="word-break: break-all;">{3}</td><td colspan="2" align="center"><table style="margin-top: 5px; height: 10px; width: 15px; border: 1px; border-color: #000000; background-color: #ffffff;"><tr><td><span style="font-size:6pt;">&nbsp;</span></td></tr></table></td></tr>'.NG_Format(
														style , item.exhib_name + ' ' + item.description , item.quantity.toFixed(2) , item.booth_number
													);
												} else {
													xmlA += '<tr{0}><td colspan="10">&nbsp;&nbsp;&nbsp;&nbsp;{1}</td><td colspan="2" align="right">{2}</td><td colspan="2" align="right" style="word-break: break-all;">{3}</td><td colspan="2" align="center"><table style="margin-top: 5px; height: 10px; width: 15px; border: 1px; border-color: #000000; background-color: #ffffff;"><tr><td><span style="font-size:6pt;">&nbsp;</span></td></tr></table></td></tr>'.NG_Format(
														style , item.exhib_name, item.quantity.toFixed(2) , item.booth_number
													);
												}
											} else {
												xmlA += '<tr{0}><td colspan="10">&nbsp;&nbsp;&nbsp;&nbsp;{1}</td><td colspan="2" align="right">{2}</td><td colspan="2" align="right" style="word-break: break-all;">{3}</td><td colspan="2" align="center"><table style="margin-top: 5px; height: 10px; width: 15px; border: 1px; border-color: #000000; background-color: #ffffff;"><tr><td><span style="font-size:6pt;">&nbsp;</span></td></tr></table></td></tr>'.NG_Format(
													style , item.exhib_name, item.quantity.toFixed(2) , item.booth_number
												);
											}

										}

										xmlA += '<tr><td colspan="10" align="right"><b>Total:</b></td><td colspan="2" align="right">{0}</td><td colspan="4">&nbsp;</td></tr>'.NG_Format(
											dataList[dl].total.toFixed(2)
										);

										xmlA += '</TBODY>\n';
										xmlA += '</table>\n';
									}
								}
							}
						}
					}

					// End the XML document
					xmlA += '</body></pdf>\n';
					xml += xmlA;
				});



				// xmlA += '</body></pdf>\n';
				// xml += xmlA;
			}
			
			xml += '</pdfset>';
			
			_log.logInfo("Printing...");
			try {
				var file = nlapiXMLToPDF(xml);
				response.setContentType("PDF", "delivery_by_product_{0}.pdf".NG_Format(_tools.timeStamp(null,null,null)), "inline");
				response.write(file.getValue());
			}
			catch (err) {
				_log.logError(err, "Error encountered creating Booth Checklist PDF");
				if (!_tools.isEmpty(_scLib.ReportXMLFolderID)) {
					try {
						var text = nlapiCreateFile("pdf_xml_out_del_by_product{0}.txt".NG_Format(_tools.timeStamp()), "PLAINTEXT", xml);
						text.setFolder(_scLib.ReportXMLFolderID);
						nlapiSubmitFile(text);
					}
					catch (e) {
						_log.logError(e, "Error encountered saving PDF XML text file");
					}
				}
				response.write("PDF creation failed: [{0}] {1}".NG_Format(err.name,err.message));
			}
		}
		else {
			response.write("Could not get order item data");
		}
	}
	else {
		response.write("Could not get show data");
	}
}

function groupOrders(orderObjList, groupBy) {
	var groupedData = {};

	orderObjList.forEach(function(order) {
		var key;
		if (groupBy === 'B') {
			key = order.booth_location || 'Unknown Location';
		} else if (groupBy === 'C') {
			key = order.booth_type || 'Unknown Type';
		} else {
			key = order.table_id;
		}

		if (!groupedData[key]) {
			groupedData[key] = [];
		}
		groupedData[key].push(order);
	});

	return groupedData;
}

function escapeXml(description) {
	return description.replace(/[<>&'"]/g, function (c) {
		switch (c) {
			case '<': return '&lt;';
			case '>': return '&gt;';
			case '&': return '&amp;';
			case '\'': return '&apos;';
			case '"': return '&quot;';
		}
	});
}

function SortByBooth(a, b) {
	if (isNaN(a['booth_number'])) {
		if (isNaN(b['booth_number'])) {
			return (a['booth_number'] || "").localeCompare(b['booth_number'] || "");
		}
		else {
			return -1;
		}
	}
	else {
		if (isNaN(b['booth_number'])) {
			return 1;
		}
		else {
			return Number(a['booth_number']) - Number(b['booth_number']);
		}
	}
}

function groupItemsBy(items, key) {
	return items.reduce(function (acc, item) {
		var groupKey = item[key] || 'Undefined';
		if (!acc[groupKey]) {
			acc[groupKey] = [];
		}
		acc[groupKey].push(item);
		return acc;
	}, {});
}