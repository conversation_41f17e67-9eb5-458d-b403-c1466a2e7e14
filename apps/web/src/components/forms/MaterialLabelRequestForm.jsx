// eslint-disable jsx-a11y/label-has-for
import { motion } from "motion/react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  Step,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Typography,
  useMediaQ<PERSON>y,
  <PERSON>m,
} from "@mui/material";
import React, { Fragment, useState } from "react";
import { useTheme } from "@mui/material/styles";
import * as Yup from "yup";
import { t } from "i18next";
import { Formik } from "formik";
import axios from "axios";
import { useSnackbar } from "notistack";
import { Pickup } from "./BOL-Steps/Pickup";
import { Delivery } from "./BOL-Steps/Delivery";
import { useSettings } from "../../store/zSettingsStore";
import { useEventSelection } from "../../store/eventStore";
import { useUser } from "../../store/zUserStore";
import { Carrier } from "./BOL-Steps/Carrier";
import { DeadlineOps } from "./BOL-Steps/DeadlineOps";
import { Payment } from "./BOL-Steps/Payment";
import { SpecailInstructions } from "./BOL-Steps/Special-Instructions";
import { TermsAndConditions } from "./BOL-Steps/TermsAndConditions";

const steps = [
  {
    label: "Pickup material location",
    description: "Tell us the location of the materials for pickup",
  },
  {
    label: "Freight delivery details",
    description: "Tell us where the freight should be sent",
  },
  {
    label: "Carrier details",
    description: "Tell us your designated carrier",
  },
  {
    label: "Deadline Missed",
    description:
      "Tell us what to do if your designated carrier misses the deadline",
  },
  {
    label: "Payment form",
    description: "Tell us who will pay",
  },
  {
    label: "Special Instructions",
    description: "",
  },
  {
    label: "Accept Terms and Conditions",
    description: "",
  },
];

const LadingStepper = ({ formik, activeStep, setActiveStep }) => {
  const [skipped, setSkipped] = useState(new Set());
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { booth, event } = useEventSelection();

  const isStepOptional = (step) => {
    return step === 5;
  };

  const isStepSkipped = (step) => {
    return skipped.has(step);
  };

  /**
   * Validate individual fields using the formik.validateField method
   * @param {Array} fields - Array of field names to validate
   * */
  const validateFields = (fields) => {
    if (Array.isArray(fields)) {
      fields.forEach((field) => {
        formik.validateField(field);
      });
    }
  };

  const handleNext = () => {
    let newSkipped = skipped;
    if (isStepSkipped(activeStep)) {
      newSkipped = new Set(newSkipped.values());
      newSkipped.delete(activeStep);
    }
    let errorsPresent = [];
    let pickupFields = [
      "numberOfLabels",
      "exhibitCompany",
      "exhibitContact",
      "boothNumber",
      "exhibitContactPhone",
    ];
    let deliveryFields = [
      "numberOfSeparateBoothShipments",
      "company",
      "contact",
      "contactPhone",
      "address",
      "city",
      "state",
      "zipCode",
      "country",
    ];
    let carrierFields = ["carrierName", "typeOfService"];
    let deadlineFields = ["deadlineMissed"];
    let paymentFields = ["whoPaid"];
    let specialInstructionsFields = ["specialInstructions"];
    let termsAndConditionsFields = ["acceptTermsAndConditions"];

    console.log("Errors present: ", formik.errors);
    console.log("Values present: ", formik.values);
    let erroredFields = Object.keys(formik.errors);

    console.log("Errored fields: ", erroredFields);
    let pickupValidated = [];
    let deliveryValidated = [];
    let carrierValidated = [];
    let deadlineValidated = [];

    switch (activeStep) {
      case 0: // Pickup
        validateFields(pickupFields);

        break;
      case 1: // Delivery
        validateFields(deliveryFields);

        break;
      case 2: // Carrier
        validateFields(carrierFields);

        break;
      case 3: // Deadline
        validateFields(deadlineFields);

        break;
      case 4: // Payment
        validateFields(paymentFields);
        // No need to validate this step
        break;
      case 5: // Special Instructions
        // No need to validate this step
        break;
      case 6: // Terms and Conditions
        validateFields(termsAndConditionsFields);
        // No need to validate this step
        break;
      default:
        console.log("No validation for this step:", activeStep);
    }

    pickupValidated = pickupFields.filter((field) =>
      erroredFields.includes(field),
    );

    if (pickupValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 0,
      });
    }
    deliveryValidated = deliveryFields.filter((field) =>
      erroredFields.includes(field),
    );

    if (deliveryValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 1,
      });
    }
    carrierValidated = carrierFields.filter((field) =>
      erroredFields.includes(field),
    );

    if (carrierValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 2,
      });
    }
    deadlineValidated = deadlineFields.filter((field) =>
      erroredFields.includes(field),
    );
    if (deadlineValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 3,
      });
    }

    console.log("Errors built: ", errorsPresent);

    let firstErrorAt =
      errorsPresent.length !== 0
        ? Math.min(...errorsPresent.map((error) => error.step))
        : -1;

    console.log("First error at: ", firstErrorAt);

    let lastStep = activeStep === steps.length - 1;
    if (lastStep) {
      console.log("Submitting form...");
      if (firstErrorAt !== -1) {
        setActiveStep(firstErrorAt);
      }
    } else if (firstErrorAt !== -1 && activeStep >= firstErrorAt) {
      setActiveStep(firstErrorAt);
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setSkipped(newSkipped);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSkip = () => {
    if (!isStepOptional(activeStep)) {
      // You probably want to guard against something like this,
      // it should never occur unless someone's actively trying to break something.
      throw new Error("You can't skip a step that isn't optional.");
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
    setSkipped((prevSkipped) => {
      const newSkipped = new Set(prevSkipped.values());
      newSkipped.add(activeStep);
      return newSkipped;
    });
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  const handleBillOfLadingSubmit = async ({
    values,
    setSubmitting,
    isSubmitting,
    errors,
    setErrors,
  }) => {
    await axios
      .post("/api/customer/post/bill-of-lading", values)
      .then((response) => {
        console.log("Response from Outbound Shipment Request submit: ", response);
        setSubmitting(false);
        if (response.status === 200) {
          console.log("Outbound Shipment Request submitted successfully");
          setSubmitting(false);
        } else {
          console.log("Outbound Shipment Request submission failed");
          setSubmitting(false);
        }
      });
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Stepper color="primary" activeStep={activeStep} orientation="vertical">
        {steps.map((step, index) => {
          const stepProps = {};
          const labelProps = {};
          if (isStepOptional(index)) {
            labelProps.optional = (
              <Typography variant="caption">Optional</Typography>
            );
          }
          if (isStepSkipped(index)) {
            stepProps.completed = false;
          }
          return (
            <Step key={step.label} {...stepProps}>
              <StepLabel color="primary" {...labelProps}>
                {step.label}
              </StepLabel>
              <StepContent>
                <Typography sx={{ mt: 2, mb: 1 }}>
                  {step.description}
                </Typography>
                <Box sx={{ display: "flex", flexDirection: "row", pt: 2 }}>
                  <Box sx={{ flex: "1 1 auto" }}>
                    {activeStep === 0 && <Pickup formik={formik} />}
                    {activeStep === 1 && <Delivery formik={formik} />}
                    {activeStep === 2 && <Carrier formik={formik} />}
                    {activeStep === 3 && <DeadlineOps formik={formik} />}
                    {activeStep === 4 && <Payment formik={formik} />}
                    {activeStep === 5 && (
                      <SpecailInstructions formik={formik} />
                    )}
                    {activeStep === 6 && <TermsAndConditions formik={formik} />}
                    <Box display="flex" justifyContent="space-between">
                      <Box sx={{ pt: 1 }}>
                        <Button
                          color="inherit"
                          variant="outlined"
                          disabled={activeStep === 0}
                          onClick={handleBack}
                          sx={{ mr: 1 }}
                        >
                          Back
                        </Button>
                      </Box>

                      <Box sx={{ pt: 1 }}>
                        {isStepOptional(activeStep) && (
                          <Button
                            color="inherit"
                            onClick={handleSkip}
                            sx={{ mr: 1 }}
                          >
                            Skip
                          </Button>
                        )}

                        <Button
                          type={
                            activeStep === steps.length - 1
                              ? "submit"
                              : "button"
                          }
                          startIcon={
                            formik.isSubmitting ? (
                              <CircularProgress size="1rem" />
                            ) : null
                          }
                          disabled={
                            Boolean(formik.errors.submit) || formik.isSubmitting
                          }
                          onClick={handleNext}
                          variant={
                            activeStep === steps.length - 1
                              ? "contained"
                              : "outlined"
                          }
                        >
                          {activeStep === steps.length - 1 ? "Finish" : "Next"}
                        </Button>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </StepContent>
            </Step>
          );
        })}
      </Stepper>
      {activeStep === steps.length && (
        <>
          <Typography sx={{ mt: 2, mb: 1 }}>
            All steps completed - you&apos;re finished
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "row", pt: 2 }}>
            <Box sx={{ flex: "1 1 auto" }} />
            <Button onClick={handleReset}>Reset</Button>
          </Box>
        </>
      )}
    </Box>
  );
};
export const MaterialLabelRequestForm = ({ data }) => {
  const { settings } = useSettings();
  const { booth, event } = useEventSelection();
  const { user } = useUser();
  const { enqueueSnackbar } = useSnackbar();
  const [activeStep, setActiveStep] = useState(0);

  console.log("Data props received: ", data);

  return (
    <Container sx={{ paddingTop: "3vh" }}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box
                sx={{
                  backgroundColor: "background.paper",
                  minHeight: "100%",
                  p: 3,
                }}
              >
                <Formik
                  enableReinitialize={user?.profile}
                  initialValues={{
                      // Required Info
                      event: event.details.id,
                      booth: booth.id,
                      // Material Pickup
                      numberOfLabels: 1,
                      exhibitCompany: user.id,
                      exhibitContact: user.contact,
                      boothNumber: booth.id,
                      exhibitContactPhone: user.profile?.phone,
                      // Freight Delivery
                      numberOfSeparateBoothShipments: 1,
                      company: user.id,
                      contact: user.profile?.contactProfile.id,
                      contactPhone: user.profile?.contactProfile.phone,
                      address: event.venue.values.custrecord_facility_address1,
                      city: event.venue.values.custrecord_facility_city,
                      state:
                        event.venue.values.custrecord_facility_state[0].text,
                      zipCode: event.venue.values.custrecord_facility_zip,
                      country:
                        event.venue.values.custrecord_facility_country[0].text,
                      destinationEvent: "",
                      destinationExhibitorBooth: "",
                      destinationCo: "",
                      // Carrier Details
                      carrierName: "",
                      typeOfService: "",
                      typeOfServiceNotes: "",
                      carrierTrackingNumber: "",
                      // Deadline Missed
                      deadlineMissed: "2", // 1 = Re-route to exhibitor booth, 2 = Return to warehouse,
                      reRouteCarrierName: "",
                      reRouteCarrierPhone: "",
                      reRouteShippingOption: "",
                      // Payment Form
                      whoPaid: "1", // 1 = Exhibitor, 2 = 3rd party
                      payorCompany: "",
                      payorName: "",
                      payorEmail: "",
                      payorPhone: "",
                      payorBillingAddress: "",
                      // Special Instructions
                      specialInstructions: "",
                      // Accept Terms and Conditions
                      acceptTermsAndConditions: false,
                      // Submit
                      submit: null,
                    }}
                    validationSchema={Yup.object().shape({
                      numberOfLabels: Yup.number()
                        .min(1, "Number must be greater than or equal to 1")
                        .required("The number of labels is required"),
                      exhibitCompany: Yup.number().required(
                        "The exhibit company is required",
                      ),
                      exhibitContact: Yup.number().required(
                        "The exhibit contact name is required",
                      ),
                      boothNumber: Yup.number().required(
                        t("Insert a booth number for the shipment to be sent."),
                      ),
                      exhibitContactPhone: Yup.string().matches(
                        /^\(\d{3}\) \d{3}-\d{4}$/,
                        "Invalid phone number",
                      ),
                      // Freight Delivery
                      numberOfSeparateBoothShipments: Yup.number()
                        .min(1, "Number must be greater than or equal to 1")
                        .required(
                          "The number of shipment separations is required",
                        ),
                      address: Yup.string().required(
                        t("Please input address for request"),
                      ),
                      company: Yup.number().required(
                        t("Please input company for request"),
                      ),
                      contact: Yup.number().required(
                        "Please input a contact name",
                      ),
                      contactPhone: Yup.string().matches(
                        /^\(\d{3}\) \d{3}-\d{4}$/,
                        "Invalid phone number",
                      ),
                      state: Yup.string().required(t("Please select a state")),
                      city: Yup.string().required(t("Please enter a city")),
                      country: Yup.string().required(
                        t("Please select a country"),
                      ),
                      zipCode: Yup.string().matches(
                        /^[0-9]{5}$/,
                        "Must be exactly 5 digits",
                      ),
                      // Carrier Details
                      carrierName: Yup.number().required(
                        "Carrier name is required",
                      ),
                      typeOfService: Yup.number().required(
                        "Type of service is required",
                      ),
                      typeOfServiceNotes: Yup.string(),
                      // Deadline Missed
                      deadlineMissed: Yup.number().required(
                        "Required for shipment to be sent",
                      ),
                      reRouteCarrierPhone: Yup.string().matches(
                        /^\(\d{3}\) \d{3}-\d{4}$/,
                        "Invalid phone number",
                      ),
                      // Payment Form
                      whoPaid: Yup.string().required("Who paid is required"),
                      payorPhone: Yup.string().matches(
                        /^\(\d{3}\) \d{3}-\d{4}$/,
                        "Invalid phone number",
                      ),
                      payorEmail: Yup.string().email('Invalid email format'),
                      // Special Instructions
                      specialInstructions: Yup.string(),
                      // Accept Terms and Conditions
                      acceptTermsAndConditions: Yup.boolean().required(
                        "You must accept the terms and conditions to submit this form",
                      ),
                    })}
                    onSubmit={async (
                      _values,
                      { resetForm, setErrors, setStatus, setSubmitting },
                    ) => {
                      try {
                        console.log("Submitting form...", _values);
                        const requestOptions = {
                          method: "POST",
                          redirect: "follow",
                          headers: {
                            "Content-Type": "application/json",
                          },
                          body: JSON.stringify(_values),
                          json: true,
                        };

                        await axios
                          .post("/api/customer/post/bill-of-lading", _values, {
                            headers: {
                              "Content-Type": "application/json",
                            },
                            json: true,
                          })
                          .then((response) => {
                            console.log("Response from Outbound Shipment Request submit: ", response);
                            setSubmitting(false);
                            if (response.status === 200) {
                              console.log("Outbound Shipment Request submitted successfully");
                              setSubmitting(false);
                              if (response.data) {
                                console.log("Response body: ", response.data);
                                if (response.data?.error) {
                                  enqueueSnackbar("Outbound Shipment Request Submission Failed!", {
                                    variant: "error",
                                    anchorOrigin: {
                                      vertical: "top",
                                      horizontal: "center",
                                    },
                                    TransitionComponent: Zoom,
                                  });
                                } else {
                                  console.log("Outbound Shipment Request submission successful");
                                  enqueueSnackbar(
                                    "Outbound Shipment Request Submission Successful - New window will open shortly with request.",
                                    {
                                      variant: "success",
                                      anchorOrigin: {
                                        vertical: "top",
                                        horizontal: "center",
                                      },
                                      TransitionComponent: Zoom,
                                    },
                                  );
                                  if (response.data?.printUrl) {
                                    window.open(
                                      response.data.printUrl,
                                      "_blank",
                                    );
                                    resetForm();
                                    setActiveStep(0);
                                  }
                                }
                              }
                            } else {
                              console.log("Outbound Shipment Request submission failed");
                              enqueueSnackbar(
                                "Outbound Shipment Request Submission Failed! - Internal error occurred",
                                {
                                  variant: "error",
                                  anchorOrigin: {
                                    vertical: "top",
                                    horizontal: "center",
                                  },
                                  TransitionComponent: Zoom,
                                },
                              );
                              setSubmitting(false);
                            }
                          });
                      } catch (err) {
                        console.error(err);
                        setStatus({ success: false });
                        setErrors({ submit: err.message });
                        setSubmitting(false);
                      }
                    }}
                  >
                    {({
                      errors,
                      handleBlur,
                      handleChange,
                      handleSubmit,
                      isSubmitting,
                      touched,
                      values,
                      setFieldValue,
                      validateForm,
                      validateField,
                    }) => (
                      <form onSubmit={handleSubmit}>
                        <LadingStepper
                          activeStep={activeStep}
                          setActiveStep={setActiveStep}
                          formik={{
                            errors,
                            handleBlur,
                            handleChange,
                            handleSubmit,
                            isSubmitting,
                            touched,
                            values,
                            setFieldValue,
                            validateForm,
                            validateField,
                          }}
                        />
                      </form>
                    )}
                  </Formik>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
  );
};
