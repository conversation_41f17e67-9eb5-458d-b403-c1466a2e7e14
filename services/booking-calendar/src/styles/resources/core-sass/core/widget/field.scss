@import '../../variables';

@keyframes b-field-updated {
    0% {
        color : $field-color;
    }
    25% {
        color : $field-updated-color;
    }
    75% {
        color : $field-updated-color;
    }
    100% {
        color : $field-color;
    }
}

.b-field label {
    // No selection when clicking rapidly on a field label
    user-select : none;
}

.b-has-label {
    &.b-label-above {
        flex-direction : column;
    }

    &.b-label-before .b-label {
        align-self        : center;
        margin-inline-end : $label-margin-end;
    }

    &.b-label-above .b-label {
        align-self : flex-start;
        flex       : none !important;
        margin     : $field-label-above-margin;
    }

    // Color of label for active field
    &.b-open,
    &:focus-within {
        .b-label {
            color : $field-active-label-color;
        }
    }

    .b-container & {
        .b-label.b-align-end {
            margin-inline-start : 1em;
            text-align          : left;
        }

        &.b-open,
        &:focus-within {
            .b-label {
                color : $field-container-active-label-color;
            }
        }
    }

    // Invalid rendition needs important to override the :hover and focus rules.
    &.b-invalid {
        .b-label {
            color : $field-invalid-border-color !important;
        }
    }

    &.b-disabled {
        .b-label {
            color : $field-disabled-label-color;
        }
    }
}

.b-field-updated {
    animation-name     : b-field-updated;
    animation-duration : .5s;
}

// When in a column-orientated container, the fields allow the container to width them.
.b-vbox.b-box-justify-stretch > .b-field,
.b-flex-column > .b-field {
    width : auto;
}

.b-flex-row > .b-field {
    align-self : flex-start;
    flex       : 1 0 100%;
}

.b-vbox > .b-field {
    &.b-label-above {
        margin-bottom : 1.2em;
    }
}

// The container of the triggers and the input field.
// This carries the border. The input field has no border.
.b-field-inner {
    display          : flex;
    flex             : 1 1 100%;
    align-items      : center;
    background-color : $field-background-color;
    border-radius    : $field-border-radius;
    border-width     : $field-border-width;
    border-style     : solid;
    border-color     : $field-border-color;
    min-width        : 0; // Won't flex shrink without this!
    position         : relative;

    .b-field-container-inline > & {
        flex : 0 1 auto; // natural size w/o shrink
    }
}

.b-field-container:not(.b-field-container-inline) {
    flex-wrap : wrap;
}

.b-field-container-wrap {
    display  : flex;
    flex     : 1 1 auto;
    overflow : hidden;
    position : relative;

    > .b-container {
        width : 100%;
    }

    .b-field.b-collapsed:not(.b-field-container-inline) > & {
        height : 0;
    }

    .b-field.b-collapsed.b-field-container-inline > & {
        opacity : 0;
    }
}

.b-field-container-wrap > .b-container {
    .b-field.b-collapsed:not(.b-field-container-inline) > &,
    .b-field.b-collapsing:not(.b-field-container-inline) > & {
        position : absolute;
        bottom   : 0;
    }
}

.b-field-container-inline:not(.b-no-input) {
    > .b-field-container-wrap {
        margin-inline-start : 1em;
    }
}

.b-field-hint {
    // This causes the element to occupy no width (leaving it for the input el) and have its text overflow to the
    // left (instead of right).
    align-self     : stretch;
    overflow       : visible;
    pointer-events : none;
    position       : relative;
    white-space    : nowrap;
    width          : 0;

    .b-field-hint-content {
        position         : absolute;
        inset-inline-end : 0.6em;
        top              : 50%;
        transform        : translateY(-50%);

        font-size        : $label-font-size;
        text-transform   : $label-text-transform;
        font-weight      : $label-font-weight;

        // TODO: Remove in 6.0
        .b-legacy-inset & {
            right : 0.6em;

            &.b-rtl {
                left : 0.6em;
            }
        }
    }

    .b-field-no-hint & {
        display : none;
    }
}

.b-numberfield,
.b-textareafield,
.b-textfield {
    // Fill height
    align-items   : stretch;
    min-width     : 3em;
    color         : $field-color;
    border-color  : $field-outer-border-color;
    border-radius : $field-border-radius;
    position      : relative;
    width         : 12.5em;

    &.b-has-width {
        width : auto;
    }

    &.b-contains-focus {
        .b-field-inner {
            border-color : $widget-highlight-color;
        }
    }

    .b-fieldtrigger {
        color       : $field-trigger-color;
        font-size   : $field-trigger-font-size;
        cursor      : pointer;
        flex        : 0 0 auto;
        align-items : center;

        // padding is used intentionally to increase clicking area
        // TODO: make equal paddings for both left and right: https://github.com/bryntum/support/issues/760
        &.b-align-start {
            padding-inline : $field-start-trigger-padding;
        }

        &.b-align-end {
            padding-inline : $field-end-trigger-padding;
        }

        &:before {
            font-size  : 1.3em;

            // Rotate is used to indicate open/closed state of dropdowns
            transition : transform .3s;
        }
    }

    .b-spintrigger {
        flex-direction : column;
        font-size      : 1em;

        .b-spin-up:before {
            content        : '\F0D8';
            vertical-align : bottom
        }

        .b-spin-down:before {
            content        : '\F0D7';
            vertical-align : top;
        }
    }

    &.b-hide-spinner {
        .b-spintrigger {
            display : none;
        }
    }

    input, textarea {
        background-color : transparent;
        color            : inherit;
        padding          : $field-input-padding $field-input-padding-horizontal;
        font-weight      : $widget-font-weight;
        flex             : 1 1 0;
        border           : 0 none;
        margin           : 0; // needed for Safari, seems to default to 2px otherwise
        font-family      : inherit;
        font-size        : inherit;
        min-width        : 1em; // Won't flex shrink without this, but make it at least visible!
        text-align       : inherit;

        &:focus {
            outline : none;
        }

        &::-ms-clear {
            display : none;
        }
    }

    textarea {
        align-self : stretch;
    }

    ::-webkit-input-placeholder {
        color : $field-placeholder-color;
    }

    // hide clear trigger in empty field
    &.b-empty {
        .b-fieldtrigger.b-icon-remove {
            visibility : hidden;
        }
    }

    &:focus-within {
        .b-label i {
            color : $widget-highlight-color;
        }
    }

    &:not(.b-disabled):hover {
        .b-label i {
            color : $widget-highlight-color;
        }

        .b-field-inner {
            border-color : $field-highlight-color;
        }
    }

    // Invalid rendition needs important to override the :hover and focus rules.
    &.b-invalid {
        // label {
        //     color : $field-invalid-border-color !important;
        // }

        .b-field-inner {
            border-color : $field-invalid-border-color !important;
        }
    }

    &.b-disabled {
        color  : $field-disabled-label-color;

        // Safari already fades the colour for disabled fields
        .b-safari & {
            color : inherit;
        }

        cursor : default;

        input {
            cursor : text;
        }

        // label,
        .b-fieldtrigger {
            color : $field-disabled-trigger-color;
        }

        .b-fieldtrigger {
            cursor : default;
        }

        .b-field-inner {
            border-style : $field-disabled-border-style;
        }
    }

    &.b-readonly {
        cursor : default;

        input {
            cursor : text;
        }

        // label,
        .b-fieldtrigger {
            color  : $field-disabled-trigger-color;
            cursor : default;
        }
    }
}

.b-numberfield {
    // These are only needed when inputType=number is specified by the user:
    input[type=number]::-webkit-inner-spin-button,
    input[type=number]::-webkit-outer-spin-button {
        -webkit-appearance : none;
        margin             : 0;
    }

    input[type=number] {
        -moz-appearance : textfield;
    }

    // make clearable icon smaller in numberfield, to better match spinner icons
    .b-field-inner .b-fieldtrigger.b-icon-remove {
        font-size : .8em;
    }
}

div.b-tooltip.b-field-error-tip {
    border : 1px solid $field-invalid-border-color;

    .b-popup-content {
        background-color : $error-tip-background-color;
        color        : $error-tip-color;
        font-weight  : bold;
        margin-block : .7em;
    }
}

.b-divider {
    position        : relative;
    justify-content : center;
    margin          : 1em 0 1.5em 0;

    &::before {
        content       : '';
        width         : 100%;
        border-bottom : 1px solid $field-border-color;
        position      : absolute;
        top           : 50%;
    }

    &::after {
        display        : flex;
        content        : attr(data-text);
        color          : $container-label-color;
        @if $tabpanel-background-color != transparent {
            background : $tabpanel-background-color;
        } @else {
            background : $popup-background-color;
        }
        padding        : 0 1em;
        z-index        : 1;
        text-transform : $label-text-transform;
        font-size      : $label-font-size - .1em;
        font-weight    : $label-font-weight;
    }
}
