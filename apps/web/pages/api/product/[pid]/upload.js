import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  addPerformanceHeaders
} from "@event-services/netsuite-http";
import * as fs from "fs";
import { IncomingForm } from "formidable";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function productFileUploader(req, res) {
  const requestStart = Date.now();
  
  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const { pid } = req.query;
    
    // Validate required parameters
    if (!pid) {
      return res.status(400).json({ error: "Product ID is required" });
    }

    const { method } = req;

    switch (method) {
      case "POST": {
        // Handle file upload
        let output = {
          files: [],
          event: {},
          user: {},
          booth: {},
          attachments: {},
        };

        const form = new IncomingForm();
        
        return new Promise((resolve, reject) => {
          form.parse(req, async (err, fields, files) => {
            if (err) {
              return resolve(res.status(400).json({ message: "Error parsing form data" }));
            }

            try {
              console.debug("files", files);
              console.debug("fields", fields);

              // Process each file
              if (files.files) {
                files.files.forEach((file) => {
                  console.debug("THEFILE", file);
                  let fileObj = {
                    name: file.originalFilename,
                    type: file.mimetype,
                    size: file.size,
                  };
                  const buffer = fs.readFileSync(file.filepath);
                  const base64 = buffer.toString("base64");
                  console.debug("File base64", base64);
                  fileObj.data = base64;
                  output.files.push(fileObj);
                });
              }

              // Process form fields
              Object.keys(fields).forEach((fieldKey) => {
                output[fieldKey] = fields[fieldKey];
              });

              console.debug("output", output);

              // Build NetSuite URL
              const url = buildNetSuiteUrl(
                "customscript_rcs_handle_upload",
                "customdeploy_rcs_handle_upload",
                { pid }
              );

              // Make request to NetSuite
              const { data, duration } = await makeNetSuiteRequest({
                url,
                method: "POST",
                body: output,
              });

              // Check for application-level errors
              if (data?.error) {
                return resolve(res.status(500).json(data));
              }

              // Add performance headers
              addPerformanceHeaders(res, requestStart, duration);

              return resolve(res.status(200).json(data));
            } catch (error) {
              console.error(error);
              return resolve(handleApiError(error, { res: res, requestStart: requestStart }));
            }
          });
        });
      }

      case "DELETE": {
        const { attachmentId, attachmentGroup } = req.query;
        
        // Validate required parameters
        if (!attachmentId || !attachmentGroup) {
          return res.status(400).json({ 
            error: "Attachment ID and attachment group are required" 
          });
        }

        // Build NetSuite URL
        const url = buildNetSuiteUrl(
          "customscript_rcs_handle_upload",
          "customdeploy_rcs_handle_upload",
          { pid, attachmentId, attachmentGroup }
        );

        // Make request to NetSuite
        const { data, duration } = await makeNetSuiteRequest({
          url,
          method: "DELETE",
        });

        // Check for application-level errors
        if (data?.error) {
          return res.status(500).json(data);
        }

        // Add performance headers
        addPerformanceHeaders(res, requestStart, duration);

        return res.status(200).json(data);
      }

      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    return handleApiError(error, { res: res, requestStart: requestStart });
  }
}
