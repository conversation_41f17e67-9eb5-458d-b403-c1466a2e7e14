import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Add,
  CheckTwoTone,
  CloseTwoTone,
  CloudUploadTwoTone,
  HelpOutlineOutlined,
  Remove,
} from "@mui/icons-material";
import {
  Alert,
  AlertTitle,
  alpha,
  Avatar,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  Stack,
  styled,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { Montserrat } from "next/font/google";
import LazyImage from "components/LazyImage";
import { H1, H2, H3, H6, Paragraph } from "components/Typography";
import { currency } from "lib";
import Cookies from "js-cookie";
import { useTranslation } from "next-i18next";
import delay from "delay";
import { useTheme } from "@mui/material/styles";
import { useSnackbar } from "notistack";
import { useRouter } from "next/router";
import moment from "moment";
import { useDropzone } from "react-dropzone";
import axios from "axios";
import ReactHtmlParser, { convertNodeToElement } from "react-html-parser";
import { NumericFormat } from "react-number-format";
import PropTypes from "prop-types";
import { DesktopTimePicker, MobileTimePicker } from "@mui/x-date-pickers";
import { BarLoader } from "@/components/animate/bar-loader";
import { AnimatedText, AnimatedTextLoader } from "@/components/animate/animated-text";
import { usePrevious } from "../../utils/customHooks";
import { useSettings } from "../../store/zSettingsStore";
import { useBooth, useEvent } from "../../store/eventStore";
import { useAddToCart, useCart } from "../../store/zustandCartStore";
import { FlexBox, FlexRowCenter } from "../../components/flex-box";
import LaborTablePreview from "../../components/modals/LaborTablePreview";
import FormikDynamic from "../../components/item-attributes/FormikDynamic";
import InlineQuantityEditField from "../../components/InlineTextEditField";
import { useUser } from "../../store/zUserStore";
import { fontWeight } from "@mui/system";

const montserrat = Montserrat({
  weight: ["100", "200", "300", "400", "500", "600", "800", "900"],
  subsets: ["latin"],
  style: ["normal", "italic"],
});

// ================================================================

// ================================================================

// ===========================================================
//  Forwarded Ref Components
// ===========================================================

const NumericWorkerFormat = React.forwardRef(
  function NumericWorkerFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        valueIsNumericString
        allowNegative={false}
        min={1}
      />
    );
  },
);

NumericWorkerFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericFreightFormat = React.forwardRef(
  function NumericFreightFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
        prefix="WT "
      />
    );
  },
);

NumericFreightFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericFreightQuantityFormat = React.forwardRef(
  function NumericFreightQuantityFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
      />
    );
  },
);

NumericFreightQuantityFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericLengthFormat = React.forwardRef(
  function NumericLengthFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
      />
    );
  },
);

NumericLengthFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericWidthFormat = React.forwardRef(
  function NumericLengthFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
      />
    );
  },
);

NumericWidthFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const NumericTotalSquareFootFormat = React.forwardRef(
  function NumericTotalSquareFootFormat(props, ref) {
    const { onChange, ...other } = props;

    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
        thousandSeparator
        valueIsNumericString
        suffix=""
      />
    );
  },
);

NumericTotalSquareFootFormat.propTypes = {
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const InitialQuantity = memo(({ quantity }) => (
  <H3 fontWeight="600" mx={2.5}>
    {quantity.toString().padStart(2, "0")}
  </H3>
));

const BoxUploadWrapper = styled(Box)(
  ({ theme }) => `
    border-radius: ${theme.shape.borderRadius}px;
    padding: ${theme.spacing(3)};
    background: ${alpha(theme.palette.primary.light, 0.3)};
    border: 1px dashed ${alpha(theme.palette.dark.main, 0.8)};
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: ${theme.transitions.create(["border", "background"])};

    &:hover {
      background: ${alpha(theme.palette.common.white, 0.3)};
      border-color: ${theme.palette.primary.main};
    }
`,
);

const AvatarWrapper = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.primary.light};
    color: ${theme.palette.primary.main};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const AvatarSuccess = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.success.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const AvatarDanger = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.palette.error.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`,
);

const ProductIntro = ({ product, collection }) => {
  process.env.NEXT_PUBLIC_DEBUG_MODE === "true" &&
    console.log("Server loaded product: ", product);

  const { cart, setItemQuantity, removeCartItem } = useCart();
  const { t } = useTranslation();
  const { event } = useEvent();
  const { booth } = useBooth();
  const { user } = useUser();
  const { addToCart } = useAddToCart();
  const { setCart } = useCart();

  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  const router = useRouter();
  const { slug } = router.query;
  let eventIdCookie = Cookies.get("eventId");
  let eventNameCookie = Cookies.get("eventName");
  let eventJobCookie = Cookies.get("job");
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));

  const { settings } = useSettings();
  const [quantity, setQuantity] = useState(1);

  console.log("Theme: ", theme);

  // Special Item type state
  const [cwtFreightWeight, setCwtFreightWeight] = useState(0);
  const [cwtFreightWeightEntered, setCwtFreightWeightEntered] = useState(0);
  const [optionWidth, setOptionWidth] = useState(10);
  const [optionLength, setOptionLength] = useState(10);
  const [optionTotalSqFt, setOptionTotalSqFt] = useState(100);
  const [daysCalcDateSelected, setDaysCalcDateSelected] = useState("");
  const [laborDateSelected, setLaborDateSelected] = useState("");
  const [memoText, setMemoText] = useState("");
  const [laborStartTimeSelected, setLaborStartTimeSelected] = useState(
    new Date(new Date().setHours(8, 0)),
  );
  const [laborEndTimeSelected, setLaborEndTimeSelected] = useState(
    new Date(new Date().setHours(17, 0)),
  );
  const [laborSupervision, setLaborSupervision] = useState(false);
  const [laborBucketBuild, setLaborBucketBuild] = useState([]);
  const [laborSupervisionCost, setLaborSupervisionCost] = useState(0);
  const [laborTableOpen, setLaborTableOpen] = useState(false);
  const [laborWorkerQuantity, setLaborWorkerQuantity] = useState(1);
  const [laborCostTotal, setLaborCostTotal] = useState("");
  const [laborBareCostTotal, setLaborBareCostTotal] = useState(0);
  const [laborHoursTotal, setLaborHoursTotal] = useState(0);
  const [laborHourlyAvg, setLaborHourlyAvg] = useState(0);
  const [unaccountedLaborHoursTotal, setUnaccountedLaborHoursTotal] =
    useState(0);
  const prevSelectedLaborDate = usePrevious(laborDateSelected);
  const prevWorkerQuantity = usePrevious(laborWorkerQuantity);
  const prevStartSelected = usePrevious(laborStartTimeSelected);
  const prevEndSelected = usePrevious(laborEndTimeSelected);
  const prevSupervisionSelected = usePrevious(laborSupervision);

  const [colorError, setColorError] = useState(false);
  const [sizeError, setSizeError] = useState(false);
  const [materialError, setMaterialError] = useState(false);
  const [screenSizeError, setScreenSizeError] = useState(false);
  const [variantError, setVariantError] = useState(false);
  const [validatingItem, setValidatingItem] = useState(false);
  const [itemOptions, setItemOptions] = useState({});
  const [isError, setCartError] = React.useState(false);
  const [attributes, setAttributes] = useState({});
  const menuItemRef = useRef(null);
  const formRef = useRef();

  // Netsuite Payload build
  const [netSize, setNetSize] = useState({});
  const [netColor, setNetColor] = useState({});
  const [netMaterial, setNetMaterial] = useState({});
  const [netScreenSize, setNetScreenSize] = useState({});
  const [netVariant, setNetVariant] = useState({});

  const [selectedOptions, setSelectedOptions] = useState({
    color: "",
    sizes: "",
    materials: "",
    variants: "",
  });

  // const [collection, setCollection] = useState("");
  const [saleUnit, setSaleUnit] = useState("");
  const [primaryImage, setPrimaryImage] = useState([]);

  // Matrix item data itemOptions
  const [itemColors, setItemColors] = useState("");
  const [itemSizes, setItemSizes] = useState("");
  const [itemMaterials, setItemMaterials] = useState("");
  const [itemVariants, setItemVariants] = useState("");

  // Matrix entry variant values.
  const [color, setColor] = useState("");
  const [size, setSize] = useState("");
  const [material, setMaterial] = useState("");
  const [screenSize, setScreenSize] = useState("");
  const [variantSelected, setVariantSelected] = useState("");

  // Modal preview for labor
  const [optionModalVisible, setOptionModalVisible] = useState(false);
  const [isAddToCartDisabled, setIsAddToCartDisabled] = useState(true);

  // Item integrity validation
  const [validatedItem, setValidatedItem] = useState({
    childId: "",
    price: null,
  });
  const [retryCount, setRetryCount] = useState(0);

  // Data item type bools
  let productData = useMemo(
    () => ({
      ...product.item,
      images:
        product.item.images.length === 0
          ? ["/assets/images/products/NO-PRODUCT-IMAGE.jpg"]
          : product.item.images,
      currentPrice: product.itemPrice,
      internalId: validatedItem.childId || product.item.id,
      ...(validatedItem.childId && {
        parentProductId: product.item.id,
      }),
      // Include collection information from props or fall back to product data
      collection: collection || product?.collection || {
        id: null,
        name:'Unknown Collection'
      }
    }),
    [product, validatedItem],
  );

  const {
    itemPrice: price,
    discount,
    comparePrice,
    daysCalcDates,
    laborDates,
    showDates,
    laborScheduleNew: laborSchedule,
  } = product;

  const {
    hasColorOptions,
    hasOrientOptions,
    hasGraphicOptions,
    hasSizeOptions,
    isShowDuration,
    isFreight,
    isLabor,
    isSquareFt,
    isEstimated,
    isDaysCalc,
    isMemoItem,
    isUpload,
    images,
    id,
    itemAttributes,
    title,
    webDescription: description,
    minimumQuantity,
    maxQuantity = Infinity,
    enforceMinQty,
    materialHandlingSchedule = null,
  } = productData;

  const isPerPieceItem =
    isFreight && materialHandlingSchedule.chargeType.value === "2";

  console.log("Is per piece item: ", isPerPieceItem);

  // Item Upload State
  const [filesUploaded, setFilesUploaded] = useState([]);
  const [fileAttachments, setFilesAttachments] = useState({
    attachments: [],
    attachmentGroup: -1,
  });
  const [deleteingFile, setDeletingFile] = useState(new Set());
  const [uploadingFile, setUploadingFile] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const attachedFiles = useMemo(() => {
    console.log("attached files: ", fileAttachments);
    let updatedAttachments = {
      attachments: [],
      attachmentGroup: -1,
    };

    if (fileAttachments.attachments.length !== 0) {
      updatedAttachments = fileAttachments;
    }

    return updatedAttachments;
  }, [fileAttachments]);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      // Do something with the files
      console.log("Files Accepted: ", acceptedFiles);
      let fileData = [];
      if (filesUploaded.length !== 0) {
        // Always upload the last file in the array
        // Append each file to the formData
        let updatedFiles = [...filesUploaded];
        let updatedDropFiles = [...acceptedFiles];
        let updatedFileNames = updatedFiles.map((fil) => fil.name);
        let updatedDropFileNames = updatedDropFiles.map((fil) => fil.name);

        console.log("Current files uploaded: ", updatedFileNames);
        console.log("Current files being uploaded: ", updatedDropFileNames);

        // Check if the file already exists in the array
        let isDuplicate = updatedFileNames.some((name) =>
          updatedDropFileNames.includes(name),
        );

        console.log("❌ Is duplicate: ", isDuplicate);

        if (!isDuplicate) {
          let updatedAttachments = [...filesUploaded, ...acceptedFiles];
          updatedAttachments.forEach((file, i) => {
            if (!file?.id) {
              fileData.push(file);
            }
          });
        }
      } else {
        acceptedFiles.forEach((file, i) => {
          fileData.push(file);
        });
      }
      console.log("Files Uploaded: ", fileData);

      const formData = new FormData();
      formData.append("event", JSON.stringify(event.details));
      formData.append("booth", JSON.stringify(booth));
      formData.append("user", JSON.stringify(user));
      formData.append("slug", slug);
      formData.append("attachments", JSON.stringify(attachedFiles));

      Array.from(fileData).forEach((file, i) => {
        formData.append(`files`, file);
      });
      setUploadingFile(true);

      let requestOptions = {
        method: "POST",
        body: formData,
        onUploadProgress: (progressEvent) => {
          const percentage = (progressEvent.loaded * 100) / progressEvent.total;
          setUploadProgress(+percentage.toFixed(2));
        },
      };

      if (!formData.get("files")) {
        setUploadingFile(false);
        setUploadProgress(0);
        enqueueSnackbar(
          "File is a duplicate or currently uploading. Please try again.",
          {
            variant: "warning",
          },
        );
        return;
      }

      await axios
        .post(`/api/product/${slug}/upload`, formData, requestOptions)
        .then((res) => {
          console.log("POST Response:", res);
          if (res.status === 200) {
            enqueueSnackbar("Files uploaded successfully!", {
              variant: "success",
            });

            // Set the files to the state
            setFilesAttachments(res.data.upload);

            if (cartItem?.id) {
              let updatedItem = {
                ...cartItem,
                fileAttachments: res.data.upload,
              };
              const updatedCart = [...cart];
              const index = updatedCart.findIndex(
                (item) => item.id === cartItem.id,
              );
              updatedCart[index] = updatedItem;
              setCart(updatedCart);
            }

            setUploadingFile(false);
            setUploadProgress(0);
          } else {
            enqueueSnackbar("Files failed to upload!", {
              variant: "error",
            });
            setUploadingFile(false);
            setUploadProgress(0);
          }
        })
        .catch((err) => {
          console.log("POST ERROR:", err);
          enqueueSnackbar("Files failed to upload!", {
            variant: "error",
          });
          setUploadingFile(false);
          setUploadProgress(0);
        });
    },
    [
      attachedFiles,
      booth,
      enqueueSnackbar,
      event.details,
      filesUploaded,
      slug,
      user,
    ],
  );
  const {
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps,
    fileRejections,
  } = useDropzone({
    accept: {
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg"],
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "application/vnd.ms-excel": [".xls"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
      "model/gltf-binary": [".glb"],
      "model/gltf+json": [".gltf"],
    },
    onDrop,
    disabled: !isUpload || !event.details.id,
  });

  useEffect(() => {
    if (acceptedFiles.length !== 0 && filesUploaded.length !== 0) {
      let updatedFiles = [...filesUploaded];
      let updatedDropFiles = [...acceptedFiles];
      let updatedFileNames = updatedFiles.map((fil) => fil.name);
      let updatedDropFileNames = updatedDropFiles.map((fil) => fil.name);

      console.log("Current files uploaded: ", updatedFileNames);
      console.log("Current files being uploaded: ", updatedDropFileNames);

      // Check if the file already exists in the array
      let isDuplicate = updatedFileNames.some((name) =>
        updatedDropFileNames.includes(name),
      );

      console.log("❌ Is duplicate: ", isDuplicate);

      if (!isDuplicate) {
        setFilesUploaded((prevState) => [...prevState, ...updatedDropFiles]);
      } else {
        return () => {
          setFilesUploaded((prevState) => updatedFiles);
          console.log("File is duplicate");
        };
      }
    } else if (acceptedFiles.length !== 0 && filesUploaded.length === 0) {
      setFilesUploaded(acceptedFiles);
    }

    return () => {
      console.log("File upload cleanup");
    };
  }, [acceptedFiles, slug]);

  const handleFileRemove = async (e, file) => {
    console.log("File removed: ", file);
    e.preventDefault();
    e.stopPropagation();
    let updatedFiles = [...filesUploaded];
    const index = updatedFiles.indexOf(file);

    // Remove the file from the array and network API
    let found = fileAttachments.attachments.find((f) => f.name === file.name);
    let attachmentGroup = fileAttachments.attachmentGroup;
    let updatedDeletedFiles = new Set(deleteingFile);
    updatedDeletedFiles.add(file.name);
    setDeletingFile(updatedDeletedFiles);
    console.log("Found file for deletion: ", found);

    if (found) {
      await axios
        .delete(`/api/product/${slug}/upload`, {
          params: {
            attachmentGroup,
            attachmentId: found.id,
            pid: slug,
          },
        })
        .then((res) => {
          console.log("DELETE Response:", res);
          if (res.status === 200) {
            enqueueSnackbar("File removed successfully!", {
              variant: "success",
            });

            // Reset the file upload attachments when deleting last file
            if (updatedFiles.length === 1) {
              updatedFiles.splice(index, 1);
              setFilesAttachments({
                attachments: [],
                attachmentGroup: -1,
              });
            } else {
              updatedFiles.splice(index, 1);
            }

            updatedDeletedFiles = new Set(deleteingFile);
            updatedDeletedFiles.delete(id);
            setDeletingFile(updatedDeletedFiles);
            setFilesUploaded(updatedFiles);

            // Delete file in cart item options while still in cart
            if (cartItem?.id) {
              let updatedItem = {
                ...cartItem,
                fileAttachments: res.data.upload,
              };
              const updatedCart = [...cart];
              const index = updatedCart.findIndex(
                (item) => item.id === cartItem.id,
              );
              updatedCart[index] = updatedItem;
              setCart(updatedCart);
            }
          } else {
            enqueueSnackbar("File failed to remove!", {
              variant: "error",
            });
          }
        })
        .catch((err) => {
          console.log("DELETE ERROR:", err);
          if (err?.error?.error?.code === "RCRD_DSNT_EXIST") {
            enqueueSnackbar("File failed to remove! Already has been deleted", {
              variant: "error",
            });
            setFilesUploaded([]);
          } else {
            enqueueSnackbar("File failed to remove!", {
              variant: "error",
            });
          }
        });
    } else {
      setFilesUploaded(updatedFiles);
    }
  };

  const files = filesUploaded.map((file, index) => (
    <ListItem disableGutters component="div" key={index}>
      <ListItemText primary={file.name} />
      <Divider />
      <ListItemSecondaryAction>
        <b>{file.size} bytes</b>
        <IconButton
          disabled={deleteingFile.size !== 0}
          onClick={(e) => handleFileRemove(e, file, index)}
        >
          {deleteingFile && deleteingFile.has(file.name) ? (
            <CircularProgress size={10} disableShrink />
          ) : (
            <CloseTwoTone />
          )}
        </IconButton>
      </ListItemSecondaryAction>
    </ListItem>
  ));

  let hasVariants = [
    { exists: hasColorOptions, name: "color" },
    { exists: hasOrientOptions, name: "orientations" },
    { exists: hasGraphicOptions, name: "materials" },
    { exists: hasSizeOptions, name: "sizes" },
  ];

  let variantOptions = hasVariants.filter((v) => v.exists);

  const cartOptions = useMemo(
    () => ({
      detail: true,
      laborDateSelected,
      laborHoursTotal,
      laborSupervision,
      laborSupervisionCost,
      laborWorkerQuantity,
      laborStartTimeSelected,
      laborEndTimeSelected,
      laborBucketBuild,
      laborHourlyAvg,
      memoText,
      attributes,
      setAttributes,
      optionTotalSqFt,
      optionWidth,
      optionLength,
      color,
      size,
      material,
      orientation: variantSelected,
      screenSize,
      daysCalcDateSelected,
      quantity,
      validatedItem,
      validatingItem,
      cwtFreightWeightEntered,
      fileAttachments,
      // functions
      setQuantity,
      setDaysCalcDateSelected,
      setOptionWidth,
      setOptionLength,
      setOptionTotalSqFt,
      setColor,
      setSize,
      setMaterial,
      setScreenSize,
      setOrientation: setVariantSelected,
      setCwtFreightWeight,
      setCwtFreightWeightEntered,
      setLaborTableOpen,
      setLaborBucketBuild,
      setLaborDateSelected,
      setLaborCostTotal,
      setLaborHoursTotal,
      setLaborHourlyAvg,
      setLaborWorkerQuantity,
      setLaborSupervision,
      setLaborSupervisionCost,
      setLaborStartTimeSelected,
      setLaborEndTimeSelected,
      setMemoText,
      setColorError,
      setSizeError,
      setMaterialError,
      setOrientationError: setVariantError,
      colorError,
      sizeError,
      materialError,
      orientationError: variantError,
      setFilesUploaded,
      setFilesAttachments,
    }),
    [
      laborDateSelected,
      laborHoursTotal,
      laborSupervision,
      laborSupervisionCost,
      laborWorkerQuantity,
      laborStartTimeSelected,
      laborEndTimeSelected,
      laborBucketBuild,
      laborHourlyAvg,
      memoText,
      optionTotalSqFt,
      optionWidth,
      optionLength,
      color,
      size,
      material,
      variantSelected,
      screenSize,
      daysCalcDateSelected,
      quantity,
      validatedItem,
      validatingItem,
      cwtFreightWeightEntered,
      fileAttachments,
      colorError,
      sizeError,
      materialError,
      variantError,
    ],
  );

  const handleSubmit = (e) => {
    let submittedAttributes = formRef?.current.values;

    formRef?.current.validateForm().then((res) => {
      formRef?.current?.submitForm().then((ret) => {
        if (Object.keys(res).length === 0) {
          cartOptions.attributes = submittedAttributes;
          formRef?.current.resetForm();
          return addToCart(e, "", productData, cartOptions);
        }
      });
    });
  };

  // ====================================================
  // Effects
  // ==================================================

  // Testing payload preview before sent to netsuite.
  useEffect(() => {
    let payloadVariants = {
      color: netColor,
      size: netSize,
      material: netMaterial,
      screenSize: netScreenSize, // Will get excluded
      orientation: netVariant,
    };

    console.log("Selected Vars: ", selectedOptions);
    console.log("NetPayload Vars: ", payloadVariants);
  }, [selectedOptions]);

  // Set state of item once dom loads
  useEffect(() => {
    console.log("⚡ Building product bools...");
    if (product?.item) {
      console.log("⚡ Building product bools...", product);
      setSaleUnit(product?.item?.saleUnit);
      setPrimaryImage(
        images.length !== 0 ? images[0] : "/NO-PRODUCT-IMAGE.jpg",
      );
      productData.image = primaryImage;
      productData.index = cart.length - 1;
      // setItemColors(product?.item?.colors);
      // setItemSizes(product?.item?.sizes);
      // setItemMaterials(product?.item?.materials);
      // setItemVariants(product?.item?.orientations);
    }
  }, [images, primaryImage, product, productData]);

  useEffect(() => {
    console.log("Data: ", product);
    if (product) {
      /*
		  // Color:
		  custitem27
		  // Size:
		  custitem28
		  // Orientation:
		  custitem_orientation
		  // Graphic Material:
		  custitem42
		  // Screen Size:
		  custitem43

		  Matrix check bools:
		  custitem_cost_is_estimated
		  custitem_has_color_options
		  custitem_has_orient_options
		  custitem_has_size_options
		  custitem_is_days
		  custitem_is_freight
		  custitem_is_sqft
		  custitem_labor_item
		  custitem_ng_cs_has_graphic_options
		  custitem_show_duration
	  * */

      let variantCheck = [
        { name: "color", value: product.item.colors || [] },
        { name: "sizes", value: product.item.sizes || [] },
        { name: "variants", value: product.item.orientations || [] },
        { name: "materials", value: product.item.materials || [] },
      ];

      let verifiedVariants = variantCheck.filter(
        (variant) => variant.value.length !== 0,
      );

      console.log("verified matrix options: ", verifiedVariants);

      if (verifiedVariants.length !== 0) {
        let variants = {};

        verifiedVariants.forEach((variant) => {
          variants[variant.name] = variant.value;
        });

        setItemOptions(variants);
      } else {
        setItemOptions({});
        setSelectedOptions({});
      }
    }
  }, [product?.item, slug]);

  useEffect(() => {
    let active = true;
    console.log("running prefetch function (pre)");
    handleItemPrefetchValidation(active);

    return () => {
      active = false;
    };
  }, [selectedOptions]);

  useEffect(() => {
    setMemoText(memoText);
  }, [memoText]);

  // SqFt Calc
  useEffect(() => {
    let active = true;

    function getTotalSqFt() {
      if (active) {
        let squareFoot = optionWidth * optionLength;
        setOptionTotalSqFt(squareFoot);
      }
    }

    getTotalSqFt();

    return () => {
      active = false;
    };
  }, [optionLength, optionWidth]);

  // Freight Item change detect
  useEffect(() => {
    if (Object.keys(settings).length !== 0) {
      let weightMinimum =
        Number(event?.details.rates.freight.minimum) ||
        Number(settings.custrecord_ng_cs_freight_minimum);

      setCwtFreightWeight(weightMinimum);
      setCwtFreightWeightEntered(weightMinimum);
    }
  }, [settings]);

  useEffect(() => {
    let active = true;
    let weightMinimum =
      Number(event?.details.rates.freight.minimum) ||
      Number(settings.custrecord_ng_cs_freight_minimum);

    function getFreightItemQuantity() {
      let freightQuantity = Math.ceil(cwtFreightWeight / 100);
      if (active) {
        if (isFreight && Object.keys(settings).length !== 0) {
          if (quantity < freightQuantity) {
            setQuantity(freightQuantity);
          } else if (
            !Number.isNaN(cwtFreightWeight) &&
            cwtFreightWeight > weightMinimum
          ) {
            setQuantity(Math.ceil(cwtFreightWeight / 100));
          } else if (
            !Number.isNaN(cwtFreightWeight) &&
            cwtFreightWeight < weightMinimum
          ) {
            // alert('Number must be above ' + settings.custrecord_ng_cs_freight_minimum)
            setQuantity(Math.ceil(parseInt(weightMinimum) / 100));
          }
        }
      }
    }
    getFreightItemQuantity();
  }, [cwtFreightWeight, quantity]);

  // Labor item field detect and hourly handler
  useEffect(() => {
    let active = true;

    console.log("Labor date selected:", laborDateSelected);
    console.log("Product:", product);

    const calculateLabor = () => {
      let allLaborCostPrices = [];
      let laborAvgArr = [];
      let supervisionCostPerBucket = [];

      const reducer = (accumulator, currentValue) => accumulator + currentValue;
      // Retrieve the rate based on the labor date, start time, and end time
      if (laborDateSelected && laborStartTimeSelected && laborEndTimeSelected) {
        let laborDate = laborDateSelected.date;

        if (
          product?.laborScheduleNew &&
          product.laborScheduleNew.length !== 0
        ) {
          const rates = getRatesByDate(laborDate);
          let totalHourlyDifference = calculateHours(
            moment(laborStartTimeSelected),
            moment(laborEndTimeSelected),
          ); // Total labor hours set
          console.log("Hours span: ", totalHourlyDifference);
          console.log("Found rates: ", rates);

          const missingTimeSlots = findMissingTimeSlots(rates);
          if (missingTimeSlots.length !== 0)
            console.warn("Time slots missing: ", missingTimeSlots);

          // Grabbed dated from labor schedule to get modifying price data.
          let startTimeString = moment(laborStartTimeSelected).format(
            "hh:mm a",
          );
          let endTimeString = moment(laborEndTimeSelected).format("hh:mm a");
          let startTimeMilitaryString = moment(laborStartTimeSelected).format(
            "HH:mm",
          );
          let endTimeMilitaryString =
            moment(laborEndTimeSelected).format("HH:mm");

          const ratesWithinTimeWindow = getEventsWithinTimeWindow(
            rates,
            startTimeMilitaryString,
            endTimeMilitaryString,
          );
          let laborHoursSum = 0;

          // Build the labor buckets
          if (ratesWithinTimeWindow.length !== 0) {
            ratesWithinTimeWindow.forEach((rate) => {
              let multiplier = parseFloat(rate.multiplier);
              let supervisorMarkup = parseFloat(rate.supervision) / 100;
              let laborRate =
                multiplier * product.itemPrice * laborWorkerQuantity;
              let laborType = rate.type;
              let totalHoursApplied = rate.durationMinutes / 60;
              let totalLaborCost = laborRate * totalHoursApplied;
              let supervisorCost =
                laborRate * totalHoursApplied * supervisorMarkup;
              let amountCharged = laborSupervision
                ? totalLaborCost + supervisorCost
                : totalLaborCost;

              console.log(
                "Bucket check:",
                `
								${startTimeString} Start time picked,
								${endTimeString} End Time picked,
								${rate.start} Start,
								${rate.end} End,
								${multiplier} Multiplier,
								${supervisorMarkup} markup,
								${laborType}
								`,
              );
              // Push to average array
              laborAvgArr.push(totalLaborCost);

              // Push to supervision cost array
              supervisionCostPerBucket.push(supervisorCost);

              // Push to labor bucket
              setLaborBucketBuild((prevState) => [
                ...prevState,
                createData(
                  rate.id,
                  laborType,
                  laborDate,
                  laborWorkerQuantity,
                  roundOff(totalHoursApplied, 2),
                  `${moment(rate.start, "HH:mm").format("hh:mm a")}`,
                  `${moment(rate.end, "HH:mm").format("hh:mm a")}`,
                  currency(laborRate),
                  currency(totalLaborCost),
                  laborSupervision ? `Yes @ ${rate.supervision}` : "No",
                  laborSupervision ? currency(supervisorCost) : "None",
                  currency(amountCharged),
                ),
              ]);

              allLaborCostPrices.push(amountCharged);
              laborHoursSum += totalHoursApplied;
            });

            setLaborHoursTotal(laborHoursSum);
          }

          if (allLaborCostPrices.length !== 0) {
            setLaborCostTotal(currency(allLaborCostPrices.reduce(reducer)));
          }

          if (laborAvgArr.length !== 0) {
            let allHourlyCost = laborAvgArr.reduce(reducer);
            let totalSupervisorCost = supervisionCostPerBucket.reduce(reducer);
            let avgRate = roundOff(allHourlyCost / totalHourlyDifference, 2);
            console.log("avgRate: ", roundOff(avgRate, 2));
            setLaborHourlyAvg(avgRate);
            setLaborBareCostTotal(roundOff(avgRate * totalHourlyDifference, 2));
            // let supervisorCost = (avgRate * totalHourlyDifference) * parseFloat(settings.custrecord_ng_cs_default_sprvisor_markup || 0) / 100
            setLaborSupervisionCost(roundOff(totalSupervisorCost, 2));
            console.log(
              "supervisor cost from buckets",
              roundOff(totalSupervisorCost, 2),
            );
          }

          console.log("Rates within time window: ", ratesWithinTimeWindow);
        } else {
          // No rates to find
        }
      }
    };

    // Reset the labor preview table on any change to the time selection.
    if (
      prevStartSelected !== laborStartTimeSelected ||
      prevEndSelected !== laborEndTimeSelected ||
      prevSupervisionSelected !== laborSupervision ||
      prevWorkerQuantity !== laborWorkerQuantity ||
      prevSelectedLaborDate !== laborDateSelected
    ) {
      setLaborBucketBuild([]);
    }

    calculateLabor();

    return () => {
      active = false;
    };
  }, [
    laborDateSelected,
    laborEndTimeSelected,
    laborStartTimeSelected,
    laborSupervision,
    laborWorkerQuantity,
    product.itemPrice,
    product.laborScheduleNew,
  ]);

  // ==================================================== \\
  // Item Validation
  // ==================================================== \\

  // Item validation against created payload including the value & text.
  const checkNetsuiteCartItem = async () => {
    if (!validatingItem) {
      // eslint-disable-next-line no-inner-declarations
      async function runVariantCheck(id) {
        let chosenVariants = {
          color: netColor,
          size: netSize,
          orientation: netVariant,
          material: netMaterial,
          screenSize: netScreenSize,
        };

        let netPayload = {
          parentId: id,
          variantsChosen: chosenVariants,
          eventId: Cookies.get("eventId"),
        };

        let raw = JSON.stringify(netPayload);

        let requestOptions = {
          method: "POST",
          redirect: "follow",
          body: raw,
        };

        let res = "";

        async function fetchRetry(url, timeout, tries, fetchOptions = {}) {
          setRetryCount(tries);
          function onError(err) {
            setRetryCount((tries = -1));
            if (retryCount === 0) {
              throw err;
            }
            return delay(timeout).then(() =>
              fetchRetry(url, timeout, retryCount, fetchOptions),
            );
          }
          return fetch(url, fetchOptions).catch(onError);
        }
        setValidatingItem(true);
        await fetchRetry(
          `/api/customer/cart/item_check`,
          1500,
          5,
          requestOptions,
        )
          .then((res) => res.json())
          .then((resJson) => {
            console.log("POST Response:", resJson);
            res = resJson;
            setValidatingItem(false);
            return resJson;
          })
          .catch((err) => {
            console.log("POST ERROR:", err);
            res = err;
            setValidatingItem(false);
            return err;
          });
        return res;
      }

      // eslint-disable-next-line no-return-await
      return await runVariantCheck(product?.item?.id);
    }
    return {
      error: {
        name: "Spam Detect",
        message:
          "Add item spam detected, your item was still being validated. Items are not added to the cart at this time. Please try again after it finished.",
      },
    };
  };

  // eslint-disable-next-line consistent-return
  const handleItemPrefetchValidation = async (active) => {
    let hasVariants = [
      { exists: hasColorOptions, name: "color" },
      { exists: hasOrientOptions, name: "variants" },
      { exists: hasGraphicOptions, name: "materials" },
      { exists: hasSizeOptions, name: "sizes" },
    ];

    let variantOptions = hasVariants.filter((v) => v.exists);

    let variantsChecking = ["color", "materials", "sizes", "variants"];
    console.log(
      "Vars",
      `
       
        `,
    );

    const buildVariantCheckObjArr = (checkingArr) => {
      if (Array.isArray(checkingArr)) {
        return checkingArr.map((variantName) => {
          switch (variantName) {
            case "color":
              return {
                name: variantName,
                value: selectedOptions.color,
              };
            case "materials":
              return {
                name: variantName,
                value: selectedOptions.materials,
              };
            case "variants":
              return {
                name: variantName,
                value: selectedOptions.variants,
              };
            case "sizes":
              return {
                name: variantName,
                value: selectedOptions.sizes,
              };
            default:
              return null;
          }
        });
      }
      return "Need an array of variant names to check when items get added to cart have itemOptions.";
    };
    const buildVariantNameList = () => {
      return buildVariantCheckObjArr(variantsChecking).filter((v) => v.value);
    };
    console.log("Variant name list item check", buildVariantNameList());

    if (variantOptions.length !== 0 && active) {
      if (buildVariantNameList().length !== variantOptions.length) {
        console.log("Not all variants ready...");
      } else {
        // eslint-disable-next-line no-return-await
        return await checkNetsuiteCartItem()
          .then((res) => {
            console.log("Then");
            if (res) {
              if (Object.keys(res).includes("error")) {
                if (res.error.name === "Spam Detect") {
                  // renderCartErrorNotification(res.error.message)
                } else {
                  // renderCartErrorNotification('There was a problem with this item. Didn\'t get added to your cart')
                }
                console.error("Cart validation error", res);
                return;
              }
              if (
                res.message === "Item not a matrix type." &&
                buildVariantNameList().length === 0
              ) {
                console.error("Cart validation error", res);
                setColor("");
                setSize("");
                setMaterial("");
                setVariantSelected("");
                setScreenSize("");
                return;
              }
              console.log("Item prevalidated: ", res);
              setValidatedItem(res);

              console.log("Item validated");
            } else {
              // Do logic if nothing comes back.
            }
          })
          .catch((err) => {
            console.error("A function error occurred: ", err);
          })
          .finally((res) => {
            console.log("Check state: ", validatedItem);
          });
      }
    }
  };

  // ====================================================
  // Labor functions
  // ==================================================
  function createData(
    id,
    type,
    date,
    workerQuantity,
    hours,
    start,
    end,
    rate,
    totalLabor,
    supervision,
    supervisionCost,
    amount,
  ) {
    return {
      id,
      type,
      date,
      workerQuantity,
      hours,
      start,
      end,
      rate,
      totalLabor,
      supervision,
      supervisionCost,
      amount,
    };
  }

  function findMissingTimeSlots(timeSlots) {
    const startTime = moment("00:00", "HH:mm");
    const endTime = moment("23:59", "HH:mm");
    const missingSlots = [];

    // Sort the time slots based on start time
    timeSlots.sort((a, b) => {
      return moment(a.start, "HH:mm").diff(moment(b.start, "HH:mm"));
    });

    let previousEndTime = startTime;

    // Iterate over the sorted time slots
    for (let i = 0; i < timeSlots.length; i++) {
      const currentSlot = timeSlots[i];
      const currentStartTime = moment(currentSlot.start, "HH:mm");
      const currentEndTime = moment(currentSlot.end, "HH:mm");

      // Check if there is a gap between the previous end time and current start time
      if (previousEndTime.isBefore(currentStartTime)) {
        const missingSlot = {
          start: previousEndTime.format("HH:mm"),
          end: currentStartTime.format("HH:mm"),
        };

        missingSlots.push(missingSlot);
      }

      // Update the previous end time
      if (currentEndTime.isAfter(previousEndTime)) {
        previousEndTime = currentEndTime;
      }
    }

    // Check if there is a gap between the last time slot's end time and the day's end time
    if (previousEndTime.isBefore(endTime)) {
      const missingSlot = {
        start: previousEndTime.format("HH:mm"),
        end: endTime.format("HH:mm"),
      };

      missingSlots.push(missingSlot);
    }

    return missingSlots;
  }

  function getEventsWithinTimeWindow(events, startTime, endTime) {
    const dayStart = moment(startTime, "HH:mm");
    const dayEnd = moment(endTime, "HH:mm");
    const dayDuration = moment.duration(dayEnd.diff(dayStart));

    const dayEvents = [];
    let currentEventStart = dayStart.clone();
    let lastEvent = null;

    for (let i = 0; i < events.length; i++) {
      const eventStart = moment(events[i].start, "HH:mm");
      const eventEnd = moment(events[i].end, "HH:mm");
      const eventDuration = moment.duration(eventEnd.diff(eventStart));

      if (eventStart.isBefore(dayEnd) && eventEnd.isAfter(dayStart)) {
        const start = moment.max(eventStart, dayStart);
        const end = moment.min(eventEnd, dayEnd);
        const duration = moment.duration(end.diff(start));

        dayEvents.push({
          id: events[i].id,
          start: start.format("HH:mm"),
          end: end.format("HH:mm"),
          duration: `${duration.asMinutes()} minutes`,
          durationMinutes: duration.asMinutes(),
          multiplier: events[i].multiplier,
          type: events[i].type,
          supervision: events[i].supervision,
        });

        currentEventStart = eventEnd;
        lastEvent = events[i];
      }
    }

    if (currentEventStart.isBefore(dayEnd)) {
      const duration = moment.duration(dayEnd.diff(currentEventStart));

      dayEvents.push({
        id: lastEvent.id,
        start: currentEventStart.format("HH:mm"),
        end: dayEnd.format("HH:mm"),
        duration: `${duration.asMinutes()} minutes`,
        durationMinutes: duration.asMinutes(),
        multiplier: lastEvent.multiplier,
        type: lastEvent.type,
        supervision: lastEvent.supervision,
      });
    }

    return dayEvents;
  }

  /**
   *  Function to retrieve the rate(s) based on the labor dates format (YYYY-MM-DD)
   *
   * @param {string} date - Date string from on change event
   * @returns {Array<Object>} foundPriceDates
   * */
  const getRatesByDate = useCallback(
    /**
     * @param {string} date
     * @returns {Array<Object>} foundPriceDates
     * */
    (date) => {
      // Implement your logic to retrieve the rate based on the labor date, start time, and end time
      // You can use an array or an API call to fetch the rates for different dates and times
      // For simplicity, let's assume we have a fixed rate of $10 per hour for all dates and times
      // console.log("Rate args:", date);
      let foundPriceDates = product?.laborScheduleNew.filter((laborDate) => {
        // console.log('Filtering: ', new Date(laborDate.values.custrecord_ng_cs_labor_date).toISOString())
        // console.log('Selected: ', selectedDate)
        return moment(moment(date).format("yyyy-MM-DD")).isSame(laborDate.date);
      });

      if (foundPriceDates) {
        // Calculate the labor hours based on the start and end time
        console.log("Date selected: ", date);
      } else {
        foundPriceDates = [];
      }

      return foundPriceDates;
    },
    [product.laborScheduleNew],
  );

  /**
   * Function to calculate the labor hours based on the start and end time
   * @param {Moment} startTime
   * @param {Moment} endTime
   * */
  const calculateHours = (startTime, endTime) => {
    // Implement your logic to calculate the labor hours based on the start and end time
    // Calculate the duration in milliseconds
    const duration = endTime.diff(startTime);

    // Convert the duration to hours
    const hours = duration / (1000 * 60 * 60);

    // Return the duration in decimal form
    return hours;
  };

  function handleLaborTimeDisabled(date, view) {
    let timeDisabled = false;
    // look at current labor date to fetch the labor schedule
    const laborDate = laborDateSelected?.date;
    const timeChosen = moment(date).format("HH:mm");
    if (date && laborDate) {
      // Check for rates on the labor date
      const rates = getRatesByDate(laborDate);
      const missingTimeSlots = findMissingTimeSlots(rates);
      if (missingTimeSlots.length !== 0) {
        missingTimeSlots.forEach((slot) => {
          if (
            moment(timeChosen, "HH:mm").isBetween(
              moment(slot.start, "HH:mm"),
              moment(slot.end, "HH:mm"),
            )
          ) {
            timeDisabled = true;
          }
        });
      } else {
        timeDisabled = false;
      }
    }

    return timeDisabled;
  }

  // Labor Worker change handler
  const handleWorkerQuantityChange = (e) => {
    console.log("Worker #: ", laborWorkerQuantity, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setLaborWorkerQuantity(parseInt(e.target.value));
    } else {
      setLaborWorkerQuantity("");
    }
  };

  const handleWorkerQuantityCapture = (e) => {
    console.log("Worker #: ", laborWorkerQuantity, `"${e.target.value}"`);
    console.log(
      "Worker # is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setLaborWorkerQuantity(1);
    } else {
      setLaborWorkerQuantity(parseInt(e.target.value));
    }
  };

  // ====================================================
  // Square Footage
  // ==================================================
  // Sq Ft change field handling
  const handleLengthChange = (e) => {
    console.log("Length: ", optionLength, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setOptionLength(parseInt(e.target.value));
    } else {
      setOptionLength("");
    }
  };

  const handleLengthCapture = (e) => {
    console.log("Length Capture: ", optionLength, `"${e.target.value}"`);
    console.log(
      "Length Capture is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setOptionLength(1);
    } else {
      setOptionLength(parseInt(e.target.value));
    }
  };

  const handleWidthChange = (e) => {
    console.log("Width: ", optionWidth, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setOptionWidth(parseInt(e.target.value));
    } else {
      setOptionWidth("");
    }
  };

  const handleWidthCapture = (e) => {
    console.log("Width Capture: ", optionWidth, `"${e.target.value}"`);
    console.log(
      "Width Capture is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setOptionWidth(1);
    } else {
      setOptionWidth(parseInt(e.target.value));
    }
  };

  const handleTotalFootChange = (e) => {
    console.log("Total Ft: ", optionTotalSqFt, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setOptionTotalSqFt(parseInt(e.target.value));
    } else {
      setOptionTotalSqFt("");
    }
  };

  const handleTotalFootCapture = (e) => {
    console.log("Total Ft Capture: ", optionTotalSqFt, `"${e.target.value}"`);
    console.log(
      "Total Ft Capture is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setOptionTotalSqFt(1);
    } else {
      setOptionTotalSqFt(parseInt(e.target.value));
    }
  };

  // ==================================================
  // Matrix
  // ==================================================
  // Regular & matrix item change handlers
  const handleQuantityChange = (e) => {
    console.log("Quantity: ", quantity, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setQuantity(parseInt(e.target.value));
    } else {
      setQuantity("");
    }
  };

  const handleQuantityCapture = (e) => {
    console.log("Quantity Capture: ", quantity, `"${e.target.value}"`);
    console.log(
      "Quantity Capture is NaN: ",
      Number.isNaN(e.target.value),
      `"${e.target.value}"`,
    );
    if (Number.isNaN(e.target.value) || e.target.value === "") {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setQuantity(1);
    } else {
      setQuantity(parseInt(e.target.value));
    }
  };

  const quantityChange = (value) => {
    const minQty = Number(minimumQuantity) || 1;
    const maxQty = Number(maxQuantity) || Infinity;
    let updatedQuantity = value;

    const setItemQty = (item, qty) => setItemQuantity(item, Number(qty));
    const deleteItem = (item) => {
      return confirm("Are you sure you want to delete this item?")
        ? removeCartItem(item)
        : setItemQty(item, minQty);
    };

    // Check if the updated quantity exceeds the maximum quantity
    if (updatedQuantity > maxQty) {
      console.log(
        `Cannot set quantity to ${updatedQuantity}. Maximum quantity allowed is ${maxQty}.`,
      );
      updatedQuantity = maxQty;
    }

    // Check if enforceMinQty is enabled and the updated quantity drops below the minimum quantity
    if (enforceMinQty && updatedQuantity < minQty) {
      console.log(
        `Cannot set quantity to ${updatedQuantity}. Minimum quantity required is ${minQty}. Removing item from cart.`,
      );
      // Remove the item from the cart or perform any necessary action
      deleteItem(cartItem);
      return;
    }

    if (!value) {
      setItemQty(cartItem, minQty);
    } else if (value > 0) {
      setItemQty(cartItem, updatedQuantity);
    } else if (!enforceMinQty && value <= 0) {
      deleteItem(cartItem);
    }
  };

  const increaseQuantity = () => {
    setQuantity((prevState) => prevState + 1);
  };

  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity((prevState) => prevState - 1);
    }
  };

  // ====================================================
  // Freight
  // ==================================================
  // CWT Fright item change handler
  const handleFreightWeightChange = (e) => {
    console.log("Weight: ", cwtFreightWeight, `"${e.target.value}"`);
    if (!Number.isNaN(parseInt(e.target.value))) {
      setCwtFreightWeight(parseInt(e.target.value));
    } else {
      setCwtFreightWeight("");
    }
  };

  const handleFreightWeightCapture = (e) => {
    console.log("Weight Capture: ", quantity, `"${e.target.value}"`);

    const cleanedInput = e.target.value.replace(/,/g, "").replace("WT ", "");

    // Parse the cleaned input into a number
    const parsedNumber = parseFloat(cleanedInput);

    // eslint-disable-next-line no-restricted-globals
    console.log(
      "Weight Capture is NaN?: ",
      Number.isNaN(parsedNumber),
      `"${parsedNumber}"`,
    );
    let weightMinimum =
      Number(event?.details.rates.freight.minimum) ||
      Number(settings.custrecord_ng_cs_freight_minimum);

    if (!Number.isNaN(parsedNumber) && parsedNumber > weightMinimum) {
      setCwtFreightWeightEntered(parsedNumber);
      setCwtFreightWeight(Math.ceil(parsedNumber / 100) * 100);
    } else if (!Number.isNaN(parsedNumber) && parsedNumber < weightMinimum) {
      console.log("Weight min not met:", { parsedNumber, weightMinimum });
      enqueueSnackbar(
        `Minimum weight must be above ${weightMinimum}. Resetting to minimum.`,
        {
          variant: "warning",
          autoHideDuration: 3000,
        },
      );
      setCwtFreightWeight(Math.ceil(weightMinimum / 100) * 100);
      setCwtFreightWeightEntered(weightMinimum);
    } else {
      // console.log('Quantity Capture is NaN OR empty: ', Number.isNaN(e.target.value), `"${e.target.value}"`)
      setCwtFreightWeight(weightMinimum);
      setCwtFreightWeightEntered(weightMinimum);
    }
  };

  const renderBlurb = (blurb) => {
    return ReactHtmlParser(blurb, { decodeEntities: true, transform });
  };

  // ====================================================
  // Add to cart handler
  // ==================================================

  // eslint-disable-next-line consistent-return
  const understandAddCartItem = async (e, modalState) => {
    // If any pre calcs need down this method is where it should happen prior to running the addToCart() Func.
    // renderVariantSelectors().length === 0 ? addToCart(e) : openOptionModal(optionModalVisible)
    if (Object.keys(itemOptions).length === 0) {
      if (isSquareFt) {
        console.log("Detected Area calc item type.");
        if (isShowDuration) {
          return addToCart(e, "", productData, cartOptions);
        }
        if (isDaysCalc) {
          // Check if a date has been selected
          if (!daysCalcDateSelected) {
            alert("Please select a date before adding this item to your cart.");
            return;
          }
          return addToCart(e, daysCalcDateSelected, productData, cartOptions);
        }
        return addToCart(e, "", productData, cartOptions);
      }
      if (isFreight) {
        return addToCart(e, "", productData, cartOptions);
      }
      if (isLabor) {
        if (laborBucketBuild.length !== 0) {
          return setLaborTableOpen(!laborTableOpen);
        }
        alert("Choose a date, following a START and END time to continue.");
      } else if (isDaysCalc) {
        // Check if a date has been selected
        if (!daysCalcDateSelected) {
          alert("Please select a date before adding this item to your cart.");
          return;
        }
        return addToCart(e, daysCalcDateSelected, productData, cartOptions);
      } else if (isShowDuration) {
        return addToCart(e, "", productData, cartOptions);
        // eslint-disable-next-line no-empty
      } else if (isEstimated) {
        return addToCart(e, "", productData, cartOptions);
      } else {
        console.log("Running cart spite of itemOptions");
        return addToCart(e, "", productData, cartOptions);
      }
    } else {
      // Run matrix and general item logic
      await addToCart(e, "", productData, cartOptions).then(() => {
        setSelectedOptions({
          color: "",
          sizes: "",
          materials: "",
          variants: "",
        });
      });
    }
  };

  const handleVariantSelect = (n, variant, variantVal) => {
    setSelectedOptions({ ...selectedOptions, [variant]: variantVal });
    let variantData = JSON.parse(n.props.dataset);
    console.log("Variant Dataload:", variantData);
    console.log("Variant value:", variantVal);
    switch (variant) {
      case "color":
        {
          setColor(variantVal);
          setNetColor(variantData);
        }
        break;
      case "sizes":
        {
          setSize(variantVal);
          setNetSize(variantData);
        }
        break;
      case "materials":
        {
          setMaterial(variantVal);
          setNetMaterial(variantData);
        }
        break;
      case "variants":
        {
          setVariantSelected(variantVal);
          setNetVariant(variantData);
        }
        break;
      default:
        console.error("No variant type specified in onChange handler.");
    }
  };

  const decreaseExistingQuantity = () => {
    const minQty = Number(minimumQuantity) || 1;
    console.log("Enforce Min Qty: ", enforceMinQty);
    if (enforceMinQty && cartItem.quantity > minQty) {
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", product);

        cart
          .filter(
            (el) => el.isShowDuration && Number(el.internalid) === Number(id),
          )
          .forEach((existingItem) => {
            if (existingItem.quantity > 1) {
              setItemQuantity(existingItem, existingItem.quantity - 1);
            }
          });
      } else {
        setItemQuantity(cartItem, cartItem.quantity - 1);
      }

      enqueueSnackbar(`Removed a "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (enforceMinQty && cartItem.quantity <= minQty) {
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", product);
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
        cart
          .filter((el) => el.isShowDuration && el.internalid === id)
          .forEach((existingItem) => {
            if (existingItem.quantity <= 1) {
              removeCartItem(existingItem);
            }
          });
      } else {
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
        removeCartItem(cartItem);
      }

      enqueueSnackbar(`Removed "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (cartItem.quantity > 1) {
      console.log("Quantity is 1: ", cartItem);
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", product);

        cart
          .filter(
            (el) => el.isShowDuration && Number(el.internalid) === Number(id),
          )
          .forEach((existingItem) => {
            if (existingItem.quantity > 1) {
              setItemQuantity(existingItem, existingItem.quantity - 1);
            }
          });
      } else {
        setItemQuantity(cartItem, cartItem.quantity - 1);
      }

      enqueueSnackbar(`Removed a "${cartItem.name}" from cart`, {
        variant: "error",
      });
    } else if (cartItem.quantity <= 1) {
      console.log("Quantity is 1: ", cartItem);
      if (cartItem.isShowDuration) {
        console.log("Show Duration item: ", cartItem);
        console.log("Current Item: ", product);
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
        cart
          .filter((el) => el.isShowDuration && el.internalid === id)
          .forEach((existingItem) => {
            if (existingItem.quantity <= 1) {
              removeCartItem(existingItem);
            }
          });
      } else {
        removeCartItem(cartItem);
        if (cartItem.isUpload) {
          setFilesUploaded([]);
          setFilesAttachments({
            attachments: [],
            attachmentGroup: -1,
          });
        }
      }

      enqueueSnackbar(`Removed "${cartItem.name}" from cart`, {
        variant: "error",
      });
    }
  };

  // ====================================================
  // Utility Functions
  // ====================================================

  function transform(node, index) {
    // console.log('Working Nodes: ', node)
    if (node.type === "tag" && node.name === "b") {
      node.name = "strong";
      return convertNodeToElement(node, index, transform);
    }
    // if (node.type === 'tag' && node.name === 'span') {
    // 	return null
    // }

    // if (node.type === 'tag' && node.name === 'ul') {
    // 	node.name = 'ol';
    // 	return convertNodeToElement(node, index, transform);
    // }

    if (node.type === "tag" && node.name === "table") {
      node.name = "div";
      // console.log('table node: ', node)
      node.attribs = {
        ...node.attribs,
        width: null,
        class: "container",
        "product-test": "container",
        style: `padding: 0;`,
      };
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tr") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "td") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    if (node.type === "tag" && node.name === "tbody") {
      node.name = "div";
      return convertNodeToElement(node, index, transform);
    }

    return convertNodeToElement(node, index, transform);
  }

  const roundOff = (num, places) => {
    const x = 10 ** places;
    return Math.round(num * x) / x;
  };

  // ====================================================

  const [selectedImage, setSelectedImage] = useState(0);

  // CHECK PRODUCT EXIST OR NOT IN THE CART
  const cartItem = cart.find((item) => {
    if (variantOptions.length !== 0 && !isDaysCalc && !isSquareFt) {
      return (
        item.internalid === productData.internalId &&
        item.variant.color === color &&
        item.variant.sizes === size &&
        item.variant.materials === material &&
        item.variant.orientations === variantSelected
      );
    }

    if (isSquareFt) {
      if (isShowDuration) {
        return (
          item.internalid === id &&
          showDates.map((dt) => dt?.date).includes(item.showDate) &&
          item.squareFtWidth === optionWidth &&
          item.squareFtLength === optionLength
        );
      }

      if (isDaysCalc) {
        return (
          item.internalid === id &&
          item.showDate === daysCalcDateSelected?.date &&
          item.squareFtWidth === optionWidth &&
          item.squareFtLength === optionLength
        );
      }
    }

    if (isMemoItem && (!isDaysCalc || !isShowDuration)) {
      return item.internalid === id && item.memoText === memoText;
    }

    if (isDaysCalc && isMemoItem) {
      return (
        item.internalid === id &&
        item.showDate === daysCalcDateSelected?.date &&
        item.memoText === memoText
      );
    }

    if (isShowDuration && isMemoItem) {
      return (
        item.internalid === id &&
        showDates.map((dt) => dt?.date).includes(item.showDate) &&
        item.memoText === memoText
      );
    }

    if (isDaysCalc) {
      return (
        item.internalid === id && item.showDate === daysCalcDateSelected?.date
      );
    }

    return item.internalid === id;
  });

  // File upload Starter Function
  useEffect(() => {
    let active = true;
    if (cartItem?.fileAttachments && !isSquareFt && active) {
      setFilesAttachments(cartItem.fileAttachments);
      setFilesUploaded(cartItem.fileAttachments?.attachments);
      return () => {
        active = false;
      };
    }

    if (
      active &&
      cartItem &&
      fileAttachments.attachments.length !== 0 &&
      !Object.prototype.hasOwnProperty.call(cartItem, "fileAttachments")
    ) {
      setFilesUploaded([]);
      setFilesAttachments({
        attachments: [],
        attachmentGroup: -1,
      });
    }

    return () => {
      active = false;
    };
  }, [cartItem, slug, fileAttachments.attachments.length]);

  // HANDLE SELECT IMAGE
  const handleImageClick = (ind) => () => setSelectedImage(ind);

  // Update the useEffect for isAddToCartDisabled state
  useEffect(() => {
    if (validatedItem.childId && validatedItem.price) {
      setIsAddToCartDisabled(false);
    } else if (variantOptions.length === 0 && !itemAttributes?.length) {
      // Enable button if there are no variants or attributes to validate
      setIsAddToCartDisabled(false);
    } else {
      setIsAddToCartDisabled(true);
    }
  }, [validatedItem, variantOptions.length, itemAttributes]);

  return (
    <Box width="100%">
      <Grid container spacing={3} justifyContent="space-around">
        <Grid item md={6} xs={12} alignItems="center">
          <FlexBox justifyContent="center" mb={6}>
            <LazyImage
              alt={title}
              width={300}
              height={300}
              loading="eager"
              src={productData.images[selectedImage]}
              sx={{
                objectFit: "contain",
              }}
            />
          </FlexBox>

          <FlexBox overflow="auto">
            {images.map((url, ind) => (
              <FlexRowCenter
                key={ind}
                width={64}
                height={64}
                minWidth={64}
                bgcolor="white"
                border="1px solid"
                borderRadius="10px"
                ml={ind === 0 ? "auto" : 0}
                style={{
                  cursor: "pointer",
                }}
                onClick={handleImageClick(ind)}
                mr={ind === images.length - 1 ? "auto" : "10px"}
                borderColor={
                  selectedImage === ind ? "primary.main" : "grey.400"
                }
              >
                <Avatar
                  src={url}
                  variant="square"
                  sx={{
                    height: 40,
                  }}
                />
              </FlexRowCenter>
            ))}
          </FlexBox>
        </Grid>

        <Grid item md={6} xs={12} alignItems="center">
          <H1 mb={1}>{title}</H1>

          <FlexBox alignItems="center" mb={1}>
            <Box>Category:&nbsp;</Box>
            <H6>{productData.category}</H6>
          </FlexBox>

          {/* <FlexBox alignItems="center" mb={2}> */}
          {/*  <Box lineHeight="1">Rated:</Box> */}
          {/*  <Box mx={1} lineHeight="1"> */}
          {/*    <BazaarRating */}
          {/*      color="warn" */}
          {/*      fontSize="1.25rem" */}
          {/*      value={4} */}
          {/*      readOnly */}
          {/*    /> */}
          {/*  </Box> */}
          {/*  <H6 lineHeight="1">(50)</H6> */}
          {/* </FlexBox> */}

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box display="flex" py={3}>
                {/* Render the variants for the item dynamically */}
                {/* {console.log('Variants detected: ', Object.keys(renderProduct.itemOptions)) /* Detect itemOptions *!/ */}
                {
                  console.log(
                    "Selected product: ",
                    selectedOptions,
                  ) /* Detect itemOptions */
                }
                {
                  console.log(
                    "Selected product options: ",
                    itemOptions,
                  ) /* Detect itemOptions */
                }
                {Object.keys(itemOptions).length !== 0 ? (
                  <Grid container spacing={3}>
                    {Object.keys(itemOptions).map((variant, vindex) => {
                      let variantLabel = `${variant[0].toUpperCase()}${variant.slice(
                        1,
                      )}`;
                      const variantErrors = {
                        color: colorError,
                        sizes: sizeError,
                        variants: variantError,
                        materials: materialError,
                      };

                      const variantOptionState = {
                        color,
                        sizes: size,
                        variants: variantSelected,
                        materials: material,
                      };
                      return (
                        <Grid
                          item
                          xs={12}
                          sx={{ paddingTop: "2vh" }}
                          key={`${variant}-${vindex}`}
                        >
                          <Grid container flexDirection="column" display="flex">
                            <Box display="flex" justifyContent="center" pb={2}>
                              <Grid
                                item
                                xs={12}
                                sm={4}
                                justifyContent="flex-end"
                              >
                                <Box
                                  pr={3}
                                  sx={{
                                    pt: `${theme.spacing(0)}`,
                                    pb: { xs: 1, md: 0 },
                                  }}
                                  alignSelf="center"
                                >
                                  <b>{t(variantLabel)}:</b>
                                </Box>
                              </Grid>
                              <Grid
                                sx={{
                                  mb: `${theme.spacing(0)}`,
                                }}
                                item
                                xs
                              >
                                <FormControl
                                  fullWidth
                                  error={variantErrors[variant]}
                                >
                                  <InputLabel
                                    id={`variant-${variant}-label`}
                                  >{`Choose ${variantLabel}`}</InputLabel>
                                  <Select
                                    labelId={`variant-${variant}-label`}
                                    fullWidth
                                    disabled={validatingItem}
                                    sx={{
                                      fontFamily: montserrat.style.fontFamily,
                                    }}
                                    color="primary"
                                    aria-label="text primary button group"
                                    label={`Choose ${variantLabel}`}
                                    onChange={(e, n) =>
                                      handleVariantSelect(
                                        n,
                                        variant,
                                        e.target.value,
                                      )
                                    }
                                    value={variantOptionState[variant]}
                                  >
                                    {itemOptions[variant].map((variantVal) => {
                                      return (
                                        <MenuItem
                                          key={`${variant}-${variantVal.text}`}
                                          value={variantVal.text}
                                          ref={menuItemRef}
                                          dataset={JSON.stringify(variantVal)}
                                          sx={{
                                            fontFamily:
                                              montserrat.style.fontFamily,
                                          }}
                                        >
                                          {variantVal.text}
                                        </MenuItem>
                                      );
                                    })}
                                  </Select>
                                </FormControl>
                              </Grid>
                            </Box>
                          </Grid>
                        </Grid>
                      );
                    })}
                  </Grid>
                ) : null}
              </Box>
            </Grid>
          </Grid>

          <Grid item xs={12} alignItems="center">
            {/*  Render Show duration options */}
            {isDaysCalc && (
              <Box>
                <Divider sx={{ mb: 2 }} />
                <Stack
                  direction="row"
                  spacing={1}
                  useFlexGap
                  flexWrap="wrap"
                  sx={{ pb: 1 }}
                >
                  <Paragraph
                    sx={{
                      fontSize: 13,
                      fontFamily: montserrat.style.fontFamily,
                    }}
                  >
                    Select a date:
                  </Paragraph>
                  {daysCalcDates
                    .slice()
                    .sort((d1, d2) => new Date(d1.date) - new Date(d2.date))
                    .map((item, i) => (
                      <Chip
                        sx={{ position: "relative", borderRadius: 1.5 }}
                        key={`chip-${i}`}
                        onClick={() => setDaysCalcDateSelected(item)}
                        label={item.date}
                        variant={
                          daysCalcDateSelected !== item ? "outlined" : "filled"
                        }
                        color={
                          daysCalcDateSelected !== item ? "default" : "primary"
                        }
                      />
                    ))}
                </Stack>
              </Box>
            )}
            {isLabor && (
              <>
                <Divider sx={{ mb: 2 }} />
                <FlexBox>
                  <Box my={0.5} pr={2}>
                    Select a date:
                  </Box>
                  <Stack
                    direction="row"
                    spacing={2}
                    useFlexGap
                    flexWrap="wrap"
                    sx={{ pb: 3 }}
                  >
                    {laborDates
                      .slice()
                      .sort((d1, d2) => new Date(d1.date) - new Date(d2.date))
                      .map((item, i) => (
                        <Chip
                          sx={{ borderRadius: 1.5 }}
                          key={`labor-date-${i}`}
                          onClick={() => setLaborDateSelected(item)}
                          variant={
                            laborDateSelected !== item ? "outlined" : "filled"
                          }
                          label={item.date}
                          color={
                            laborDateSelected !== item ? "default" : "primary"
                          }
                        />
                      ))}
                  </Stack>
                </FlexBox>
                <Grid container spacing={3}>
                  <Grid xs item>
                    {mobile ? (
                      <MobileTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        label="Labor Start"
                        margin="normal"
                        value={laborStartTimeSelected}
                        onChange={(newValue) =>
                          setLaborStartTimeSelected(newValue)
                        }
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    ) : (
                      <DesktopTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        label="Labor Start"
                        value={laborStartTimeSelected}
                        onChange={(newValue) =>
                          setLaborStartTimeSelected(newValue)
                        }
                        minutesStep={1}
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    )}
                  </Grid>
                  <Grid xs item flexDirection="column">
                    {mobile ? (
                      <MobileTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        margin="normal"
                        label="Labor End"
                        value={laborEndTimeSelected}
                        onChange={(newValue) =>
                          setLaborEndTimeSelected(newValue)
                        }
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    ) : (
                      <DesktopTimePicker
                        fullWidth
                        disabled={!laborDateSelected}
                        label="Labor End"
                        value={laborEndTimeSelected}
                        onChange={(newValue) =>
                          setLaborEndTimeSelected(newValue)
                        }
                        KeyboardTimePickerProps={{
                          "aria-label": "change time",
                        }}
                        shouldDisableTime={(date, view) =>
                          handleLaborTimeDisabled(date, view)
                        }
                      />
                    )}
                  </Grid>
                </Grid>
                <Grid container pt={2}>
                  <Grid xs item>
                    <TextField
                      fullWidth
                      sx={{ py: 2, pr: 2 }}
                      label="Worker Quantity"
                      onChange={(e) => handleWorkerQuantityChange(e)}
                      onBlur={(e) => handleWorkerQuantityCapture(e)}
                      variant="outlined"
                      value={laborWorkerQuantity}
                      InputProps={{
                        inputComponent: NumericWorkerFormat,
                      }}
                    />
                  </Grid>
                  <Grid
                    xs
                    item
                    justifySelf="end"
                    alignSelf="end"
                    sx={{ textAlign: "center" }}
                  >
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={(e, v) => setLaborSupervision(v)}
                          checked={laborSupervision}
                          name="checkedH"
                        />
                      }
                      label={
                        <span>
                          <Typography variant="caption">
                            Supervision Required?
                          </Typography>
                        </span>
                      }
                    />
                  </Grid>
                </Grid>
              </>
            )}
            {isFreight && (
              <Box>
                <Divider sx={{ mb: 2 }} />
                <Grid container display="flex" flexDirection="row" pt={2}>
                  <Grid item md={6} xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      onChange={(e) => handleFreightWeightChange(e)}
                      onBlur={(e) => handleFreightWeightCapture(e)}
                      variant="outlined"
                      value={cwtFreightWeight}
                      InputProps={{
                        inputComponent: NumericFreightFormat,
                      }}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
            {isSquareFt && (
              <Box>
                <Divider sx={{ mt: 1, mx: 1, mb: 2 }} />
                <Typography variant="caption" sx={{ pt: 3 }}>
                  Enter Dimensions
                </Typography>
                <Grid
                  container
                  display="flex"
                  flexDirection="row"
                  sx={{ py: 2 }}
                  spacing={2}
                >
                  <Grid item md={4} xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      className="text-center"
                      onChange={(e) => handleLengthChange(e)}
                      onBlur={(e) => handleLengthCapture(e)}
                      variant="outlined"
                      value={optionLength}
                      InputProps={{
                        inputComponent: NumericLengthFormat,
                      }}
                    />
                  </Grid>
                  <Grid item xs={0}>
                    x
                  </Grid>
                  <Grid item md={4} xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      className="text-center"
                      onChange={(e) => handleWidthChange(e)}
                      onBlur={(e) => handleWidthCapture(e)}
                      variant="outlined"
                      value={optionWidth}
                      InputProps={{
                        inputComponent: NumericWidthFormat,
                      }}
                    />
                  </Grid>
                  <Grid item xs={0}>
                    =
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      size="small"
                      className="text-center"
                      onChange={(e) => handleTotalFootChange(e)}
                      onBlur={(e) => handleTotalFootCapture(e)}
                      variant="outlined"
                      value={optionTotalSqFt}
                      InputProps={{
                        inputComponent: NumericTotalSquareFootFormat,
                      }}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
            {isShowDuration && (
              <FlexBox mb={2}>
                <Divider sx={{ mb: 2 }} />
                <Box px={2}>
                  <Tooltip
                    arrow
                    placement="right"
                    title={
                      <Typography variant="caption" sx={{ py: 1 }}>
                        All dates will be included for the duration of the event
                      </Typography>
                    }
                  >
                    <IconButton>
                      <HelpOutlineOutlined fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Stack
                  direction="row"
                  spacing={2}
                  useFlexGap
                  flexWrap="wrap"
                  sx={{ pb: 1, width: "100%" }}
                >
                  {showDates.map((item) => (
                    <Chip
                      key={item.id}
                      sx={{ borderRadius: 1.5 }}
                      color="primary"
                      disabled
                      label={item.date}
                    />
                  ))}
                </Stack>
              </FlexBox>
            )}
          </Grid>

          {isLabor && laborBucketBuild.length !== 0 && (
            <LaborTablePreview
              size="xl"
              frame={mobile}
              rows={laborBucketBuild}
              isOpen={laborTableOpen}
              itemAttributes={itemAttributes}
              handleAttributeSubmit={handleSubmit}
              innerRef={formRef}
              workerCount={laborWorkerQuantity}
              totalAllLaborCost={laborCostTotal}
              laborDateSelected={laborDateSelected}
              laborEndTimeSelected={laborEndTimeSelected}
              laborStartTimeSelected={laborStartTimeSelected}
              supervisionRequired={laborSupervision}
              laborBareCostTotal={currency(laborBareCostTotal)}
              totalSupervisonCost={currency(laborSupervisionCost)}
              toggle={() => setLaborTableOpen(!laborTableOpen)}
              addToCart={(e) =>
                addToCart(
                  e,
                  moment(laborDateSelected).format("m/d/yyyy"),
                  productData,
                  cartOptions,
                )
              }
              totalHoursChosen={roundOff(
                Math.abs(
                  laborStartTimeSelected.getTime() -
                    laborEndTimeSelected.getTime(),
                ) / 36e5,
                2,
              )}
            />
          )}

          {itemAttributes.length !== 0 && !isLabor ? (
            <Grid item xs={12}>
              <FormikDynamic
                itemAttributes={itemAttributes}
                setAttributes={setAttributes}
                innerRef={formRef}
              />
            </Grid>
          ) : null}

          <FlexBox pt={1} mb={3} alignItems="center" gap={1}>
            <Box>
              <H2 color="primary.main" mb={0.5} lineHeight="1">
                {validatingItem ? (
                  <Box sx={{ width: 200 }}>
                    <BarLoader color={settings?.custrecord_ng_cs_accent_color || "#FFF555"} />
                      <AnimatedText
                        sx={{ color: settings?.custrecord_ng_cs_accent_color || "#FFF555", fontWeight: 400 }}
                        phrases={["Validating Selection", "Validating Price", "Gathering Details", "Price Fetched"]}
                      />
                  </Box>
                ) : validatedItem.price ? (
                  currency(validatedItem.price)
                ) : variantOptions.legnth !== 0 && price === "" || price === null || Number.isNaN(price) ? (
                  "Prices Vary"
                ) : (
                  currency(price)
                  )}
              </H2>
            </Box>
            {!!discount && (
              <Box>
                <H3 color="grey.600" fontWeight="600">
                  {validatingItem ? (
                    <BarLoader
                      color={
                        settings?.custrecord_ng_cs_accent_color
                          ? settings.custrecord_ng_cs_accent_color
                          : "#FFF555"
                      }
                    />
                  ) : validatedItem.compare_price ? (
                    <del>{currency(validatedItem.compare_price)}</del>
                  ) : (
                    <del>{currency(comparePrice)}</del>
                  )}
                </H3>
              </Box>
            )}
          </FlexBox>

          {!cartItem?.quantity ? (
            <Button
              disabled={
                validatingItem || 
                uploadingFile || 
                isPerPieceItem ||
                (variantOptions.length > 0 && isAddToCartDisabled) 
              }
              color="primary"
              variant="contained"
              onClick={
                itemAttributes.length !== 0 && !isLabor
                  ? handleSubmit
                  : (e) => understandAddCartItem(e, optionModalVisible)
              }
              sx={{
                mb: 4.5,
                px: "1.75rem",
                height: 40,
              }}
            >
              {validatingItem ? "Validating Product" : "Add to Cart"}
              {validatedItem.price ? ` (${currency(validatedItem.price)})` : null}
            </Button>
          ) : (
            <Box>
              {!isFreight && !isLabor ? (
                <FlexBox alignItems="center" mb={4.5}>
                  <Button
                    size="small"
                    sx={{
                      p: 1,
                    }}
                    color="primary"
                    variant="outlined"
                    onClick={() => decreaseExistingQuantity()}
                  >
                    <Remove fontSize="small" />
                  </Button>

                  <InlineQuantityEditField
                    disabled={isLabor || isFreight}
                    initialValue={cartItem?.quantity}
                    onClickAway={quantityChange}
                    textVariant="h4"
                    min={Number(minimumQuantity) || 1}
                    max={Number(maxQuantity) || Infinity}
                    inputStyle={{
                      mx: 3,
                      px: 1,
                      fontSize: "1.5rem",
                      border: "1px solid #c4c4c4",
                      borderRadius: "5px",
                    }}
                  />

                  <Button
                    size="small"
                    sx={{
                      p: 1,
                    }}
                    color="primary"
                    variant="outlined"
                    onClick={(e) =>
                      understandAddCartItem(e, optionModalVisible)
                    }
                  >
                    <Add fontSize="small" />
                  </Button>
                </FlexBox>
              ) : (
                <Button
                  disabled={validatingItem || uploadingFile || isPerPieceItem}
                  color="primary"
                  variant="contained"
                  onClick={(e) => understandAddCartItem(e, optionModalVisible)}
                  sx={{
                    mb: 4.5,
                    px: "1.75rem",
                    height: 40,
                  }}
                >
                  Add to Cart
                </Button>
              )}
            </Box>
          )}

          {isPerPieceItem ? (
            <Alert severity="warning" sx={{ "padding-bottom": "1em" }}>
              <AlertTitle>Not Available</AlertTitle>
              This is a Per Piece Item - It is not yet available for web
              purchasing. Please contact exhibitor services to process this type
              of item.
            </Alert>
          ) : null}

          {isEstimated ? (
            <Box sx={{ "padding-bottom": "1em" }}>
              <Typography variant="caption">
                This is an estimated item, you will not be charged on checkout
              </Typography>
            </Box>
          ) : null}

          {isMemoItem ? (
            <Box
              // display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              <TextField
                multiline
                fullWidth
                label="Memo"
                variant="outlined"
                placeholder="Add your memo..."
                minRows={3}
                value={memoText}
                onChange={(e) => {
                  setMemoText(e.target.value);
                }}
              />
            </Box>
          ) : null}

          {isUpload && (
            <Grid
              sx={{
                mb: `${theme.spacing(3)}`,
                pt: 3,
              }}
              item
              xs={12}
            >
              <BoxUploadWrapper {...getRootProps()}>
                <input {...getInputProps()} />
                {isDragAccept && (
                  <>
                    <AvatarSuccess variant="rounded">
                      <CheckTwoTone />
                    </AvatarSuccess>
                    <Typography
                      sx={{
                        mt: 2,
                      }}
                    >
                      {t("Drop the files to start uploading")}
                    </Typography>
                  </>
                )}
                {isDragReject && (
                  <>
                    <AvatarDanger variant="rounded">
                      <CloseTwoTone />
                    </AvatarDanger>
                    <Typography
                      sx={{
                        mt: 2,
                      }}
                    >
                      {t("You cannot upload these file types")}
                    </Typography>
                  </>
                )}
                {!isDragActive && (
                  <>
                    <AvatarWrapper variant="rounded">
                      <CloudUploadTwoTone />
                    </AvatarWrapper>
                    <Typography
                      sx={{
                        mt: 2,
                      }}
                    >
                      {t("Drag & drop files here to attach to item")}
                    </Typography>
                  </>
                )}
              </BoxUploadWrapper>
              {(acceptedFiles.length > 0 || filesUploaded.length > 0) && (
                <>
                  <Alert
                    sx={{
                      py: 0,
                      mt: 2,
                    }}
                    severity="success"
                  >
                    {t("You have uploaded")} <b>{filesUploaded.length}</b>{" "}
                    {t("files")}!
                  </Alert>
                  <Divider
                    sx={{
                      mt: 2,
                    }}
                  />
                  <List disablePadding component="div">
                    {files}
                  </List>
                </>
              )}
            </Grid>
          )}

          {/* <FlexBox alignItems="center" gap={1} mb={2}> */}
          {/*  <Box>Sold By:</Box> */}
          {/*  <Link href="/shops/scarlett-beauty"> */}
          {/*    <H6>Mobile Store</H6> */}
          {/*  </Link> */}
          {/* </FlexBox> */}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProductIntro;
