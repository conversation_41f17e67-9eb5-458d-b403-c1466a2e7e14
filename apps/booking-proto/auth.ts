import { UserProfile } from "@/types/user"

// Configure one or more authentication providers
export const config = {
	providers: [
		{
			id: "netsuite",
			name: "NetSuite",
			type: "oauth",
			authorization: {
				url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.app.netsuite.com/app/login/oauth2/authorize.nl`,
				params: {
					client_id: `${process.env.OAUTH_CLIENT_ID}`,
					prompt: "login",
					response_type: "code",
					scope: "restlets rest_webservices",
				},
			},
			token: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token`,
			userinfo: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_rcs_rl_get_current_user&deploy=customdeploy_rcs_rl_get_current_user`,
			checks: ["state"],
			profile(profile: UserProfile, token: any) {
				// You can use the tokens, in case you want to fetch more profile information
				// For example several OAuth providers do not return email by default.
				// Depending on your provider, will have tokens like `access_token`, `id_token` and or `refresh_token`
				console.debug("Profile: ", profile);
				console.debug("Token: ", token);

				return {
					id: profile.id,
					name: profile.name,
					email: profile.email,
					location: profile.location,
					role: profile.role,
					contact: profile?.contact,
					isBookingUser: profile?.isBookingUser,
				};
			},
			clientId: `${process.env.OAUTH_CLIENT_ID}`,
			clientSecret: `${process.env.OAUTH_CLIENT_SECRET}`,
		},
	],
	strategy: "jwt",
	pages: {
		signIn: "/auth/login",
		error: "/auth/login",
	},
	callbacks: {
		async signIn({ user, account, profile, email, credentials }: { user: any, account: any, profile: any, email: any, credentials: any }) {
			if (!user?.isBookingUser) {
				throw new Error('invalidUser')
			}
			return true
		},
		async jwt({ token, account, profile }: { token: any, account: any, profile: any }) {
			console.debug("jwt token cb obj: ", token);
			console.debug("jwt account cb obj: ", account);
			console.debug("jwt profile cb obj: ", profile);
			if (account) {
				// First-time login, save the `access_token`, its expiry and the `refresh_token`
				return {
					...token,
					access_token: account.access_token,
					expires_at: account.expires_at,
					refresh_token: account.refresh_token,
				}
			} else if (Date.now() < token.expires_at * 1000) {
				// Subsequent logins, but the `access_token` is still valid
				return token
			} else {
				// Subsequent logins, but the `access_token` has expired, try to refresh it
				if (!token.refresh_token) throw new TypeError("Missing refresh_token")

				try {
					// The `token_endpoint` can be found in the provider's documentation. Or if they support OIDC,
					// at their `/.well-known/openid-configuration` endpoint.
					// i.e. https://accounts.google.com/.well-known/openid-configuration
					const url = `https://${
						process.env.NEXT_PUBLIC_ACCOUNT_URL_ID
					}.suitetalk.api.netsuite.com/services/rest/auth/oauth2/v1/token?${new URLSearchParams(
						// @ts-ignore
						{
							client_id: process.env.OAUTH_CLIENT_ID,
							client_secret: process.env.OAUTH_CLIENT_SECRET,
							grant_type: "refresh_token",
							refresh_token: token.refreshToken,
						}
					)}`;

					const response = await fetch(url, {
						headers: {
							"Content-Type": "application/x-www-form-urlencoded",
						},
						method: "POST",
					});

					const tokensOrError = await response.json()

					if (!response.ok) throw tokensOrError

					const newTokens = tokensOrError as {
						access_token: string
						expires_in: number
						refresh_token?: string
					}

					token.access_token = newTokens.access_token
					token.expires_at = Math.floor(
						Date.now() / 1000 + newTokens.expires_in
					)
					// Some providers only issue refresh tokens once, so preserve if we did not get a new one
					if (newTokens.refresh_token)
						token.refresh_token = newTokens.refresh_token
					return token
				} catch (error) {
					console.error("Error refreshing access_token", error)
					// If we fail to refresh the token, return an error so we can handle it on the page
					token.error = "RefreshTokenError"
					return token
				}
			}
		},
		async session({ session, user, token }: any) {
			// Add property to session, like an access_token from a provider.
			console.debug("session cb obj: ", session);
			console.debug("user session cb obj: ", user);
			console.debug("session cb token: ", token);

			if (token) {
				session.accessToken = token.accessToken;
				session.error = token.error;
				session.user = {
					name: token.name,
					email: token.email,
					id: token.sub
				};
			}

			return session;
		},
	},
	debug: /* true */ process.env.NODE_ENV === "development",
	logger: {
		error(code: number, metadata: any) {
			console.error("Logging err: ", code, metadata);
		},
		warn(code: number, metadata: any) {
			console.warn("Logging warn: ", code, metadata);
		},
		debug(code: number, metadata: any) {
			console.debug("Logging debug: ", code, metadata);
		},
	},
	secret: process.env.NEXTAUTH_SECRET,
}
