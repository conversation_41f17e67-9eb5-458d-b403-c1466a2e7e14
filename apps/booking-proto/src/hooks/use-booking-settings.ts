import { useQuery } from "@tanstack/react-query"

// Type definitions for settings data
export interface BookingStatus {
  id: string
  name: string
  custrecord_status_color?: string
}

export interface CSEvent {
  id: string
  name: string
}

export interface PricingRate {
  id: string
  name: string
  custrecord_ng_cs_space_rate_label: string
  custrecord_ng_cs_space_rate_type: number
  custrecord_ng_cs_space_rate_rate: number
  custrecord_ng_cs_space_rate_assoc_space: number
  custrecord_ng_cs_space_rate_item?: string
  custrecord_ng_cs_space_price_list?: string
}

export interface RateType {
  id: string
  name: string
  scriptid?: string
}

interface BookingSettings {
  status: {
    list: BookingStatus[]
  }
  event: {
    list: CSEvent[]
  }
  pricing: {
    rates: PricingRate[]
    list: any[]
  }
  rateTypes: RateType[]
}

async function fetchBookingSettings(): Promise<BookingSettings> {
  const response = await fetch("/api/settings")
  if (!response.ok) {
    throw new Error("Failed to fetch booking settings")
  }
  return response.json()
}

export function useBookingSettings() {
  return useQuery<BookingSettings>({
    queryKey: ["bookingSettings"],
    queryFn: fetchBookingSettings,
    staleTime: 1000 * 60 * 60, // 1 hour
    cacheTime: 1000 * 60 * 60 * 24, // 24 hours
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  })
}

// Helper hooks for specific data
export function useCSEvents() {
  const { data, ...rest } = useBookingSettings()
  return {
    events: data?.event?.list || [],
    ...rest,
  }
}

export function useBookingStatuses() {
  const { data, ...rest } = useBookingSettings()
  return {
    statuses: data?.status?.list || [],
    ...rest,
  }
}

export function usePricingRates() {
  const { data, ...rest } = useBookingSettings()
  return {
    rates: data?.pricing?.rates || [],
    rateTypes: data?.rateTypes || [],
    ...rest,
  }
}

// Helper function to get rates for a specific space
export function getRatesForSpace(rates: PricingRate[], spaceId: string) {
  const ratesForSpace = rates.filter(rate => Number(rate.custrecord_ng_cs_space_rate_assoc_space) === Number(spaceId))
  return ratesForSpace
}

// Helper function to group rates by rate type
export function groupRatesByType(rates: PricingRate[]) {
  return rates.reduce((acc, rate) => {
    const typeId = rate.custrecord_ng_cs_space_rate_type
    if (!acc[typeId]) {
      acc[typeId] = []
    }
    acc[typeId].push(rate)
    return acc
  }, {} as Record<string, PricingRate[]>)
}

// Helper function to find the "standard" rate type
export function getStandardRateType(rateTypes: RateType[]): RateType | null {
  // Look for "standard" in name (case insensitive) or scriptid
  return rateTypes.find(type => 
    type.name.toLowerCase().includes('standard') || 
    type.scriptid?.toLowerCase().includes('standard')
  ) || rateTypes[0] || null // Fallback to first rate type if "standard" not found
}

// Helper function to find the rate for a specific space and rate type
export function findRateForSpaceAndType(
  rates: PricingRate[], 
  spaceId: string, 
  rateTypeId: string
): PricingRate | null {
  return rates.find(rate => 
    rate.custrecord_ng_cs_space_rate_assoc_space === spaceId && 
    rate.custrecord_ng_cs_space_rate_type === Number(rateTypeId)
  ) || null
}

// Helper function to apply default rates to a booking object
export function applyDefaultRateToBooking(
  booking: any,
  roomId: string,
  rates: PricingRate[],
  rateTypes: RateType[]
): any {
  try {
    // Find the standard rate type
    const standardRateType = getStandardRateType(rateTypes)
    if (!standardRateType) {
      console.warn('No standard rate type found')
      return booking
    }

    // Find the rate for this room and standard rate type
    const rate = findRateForSpaceAndType(rates, roomId, standardRateType.id)
    if (!rate) {
      console.warn(`No rate found for room ${roomId} and rate type ${standardRateType.id}`)
      return {
        ...booking,
        rateTypeId: standardRateType.id,
        rateId: undefined,
        rate: undefined
      }
    }

    // Apply the rate information
    return {
      ...booking,
      rateTypeId: standardRateType.id,
      rateId: rate.id,
      rate: rate.custrecord_ng_cs_space_rate_rate
    }
  } catch (error) {
    console.error('Error applying default rate to booking:', error)
    return booking
  }
} 