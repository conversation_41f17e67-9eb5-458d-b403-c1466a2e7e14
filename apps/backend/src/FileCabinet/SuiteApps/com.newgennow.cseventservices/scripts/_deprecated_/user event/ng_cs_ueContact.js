/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseDepartments = _NSFeatures.DEPARTMENTS() == "T" ? true : false;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function userEventBeforeLoad(type, form, request){
	
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      approve, reject, cancel (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF)
 *                      markcomplete (Call, Task)
 *                      reassign (Case)
 *                      editforecast (Opp, Estimate)
 * @returns {Void}
 */
function userEventBeforeSubmit(type){
	
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      approve, cancel, reject (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF only)
 *                      dropship, specialorder, orderitems (PO only) 
 *                      paybills (vendor payments)
 * @returns {Void}
 */
function userEventAfterSubmit(type){
	if (_tools.isInArray(type, ["create","edit"])) {
		var newRec = nlapiGetNewRecord();
		if (_scLib.GiveContactsWebStoreAccess) {
			var contactCompany = nlapiLookupField(newRec.getRecordType(), newRec.getId(), "company");
			
			if (!_tools.isEmpty(contactCompany)) {
				var exhibRec = nlapiLoadRecord("customer", contactCompany, { recordmode : "dynamic" });
				var cl = exhibRec.findLineItemValue("contactroles", "contact", newRec.getId());
				
				if (cl > 0 && exhibRec.getLineItemValue("contactroles", "giveaccess", cl) != "T") {
					try {
						var pw = _tools.randomString(null, null, 20, true);
						exhibRec.selectLineItem("contactroles", cl);
						exhibRec.setCurrentLineItemValue("contactroles", "giveaccess", "T");
						exhibRec.setCurrentLineItemValue("contactroles", "role", "14");
						exhibRec.setCurrentLineItemValue("contactroles", "password", pw);
						exhibRec.setCurrentLineItemValue("contactroles", "passwordconfirm", pw);
						exhibRec.commitLineItem("contactroles");
						
						nlapiSubmitRecord(exhibRec, true, true);
					} catch (err) {
						_log.logError(err, "Error encountered giving contact login access");
					}
				} else {
					_log.logInfo("Contact not found on customer");
				}
			} else {
				_log.logInfo("Contact not linked to customer");
			}
		}
	}
}
