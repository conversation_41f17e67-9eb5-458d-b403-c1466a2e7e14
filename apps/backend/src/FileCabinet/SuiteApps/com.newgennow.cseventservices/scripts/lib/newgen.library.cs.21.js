/**
 * newgen.library.cs.21.js
 * @NApiVersion 2.1
 * @NModuleScope Public
 */

define([
  "N/format",
  "N/record",
  "N/search",
  "N/url",
  "N/xml",
  "N/https",
  "./newgen.library.v21",
], /**
 * @param {format} format
 * @param {record} record
 * @param {search} search
 * @param {url} url
 * @param {xml} nXML
 * @param {https} https
 * @param {Object} NG
 */ (format, record, search, url, nXML, https, NG) => {
  const _SettingsFields = [
    "custrecord_ng_cs_accent_color",
    "custrecord_ng_cs_acct_domain_url",
    "custrecord_ng_cs_activity_log_rec_id",
    "custrecord_ng_cs_activity_log_srch_id",
    "custrecord_ng_cs_add_item_forms",
    // "custrecord_ng_cs_algolia_api_key","custrecord_ng_cs_algolia_application_id","custrecord_ng_cs_algolia_index","custrecord_ng_cs_algolia_search_key",
    "custrecord_ng_cs_allow_mass_booth_delete",
    "custrecord_ng_cs_allow_mult_billng_part",
    "custrecord_ng_cs_allow_show_autopay",
    "custrecord_ng_cs_amex",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_auto_charge_web_orders",
    "custrecord_ng_cs_autochrg_cat_excl",
    "custrecord_ng_cs_billed_ord_edit_users",
    "custrecord_ng_cs_booth_num_line_text",
    "custrecord_ng_cs_booth_ord_forms",
    "custrecord_ng_cs_canc_threshold",
    "custrecord_ng_cs_cancl_charge_item",
    "custrecord_ng_cs_canonical_base_url",
    "custrecord_ng_cs_cc_auth_item",
    "custrecord_ng_cs_cc_conv_fee_item",
    "custrecord_ng_cs_cc_conv_fee_order_types",
    "custrecord_ng_cs_cc_conv_fee_rate",
    "custrecord_ng_cs_clear_order_cc_details",
    "custrecord_ng_cs_company_header_logo",
    "custrecord_ng_cs_contact_us_url",
    "custrecord_ng_cs_conv_fee_web_displayed",
    "custrecord_ng_cs_conv_fee_zero_tax",
    "custrecord_ng_cs_csv_import_file",
    "custrecord_ng_cs_csv_import_folder_id",
    "custrecord_ng_cs_cust_web_access_role",
    "custrecord_ng_cs_custom_job_numbering",
    "custrecord_ng_cs_def_canc_chrg_pct",
    "custrecord_ng_cs_def_dep_account",
    "custrecord_ng_cs_def_exhb_dept",
    "custrecord_ng_cs_def_exhb_ord_type",
    "custrecord_ng_cs_def_show_mgmt_dept",
    "custrecord_ng_cs_def_show_mgmt_ord_type",
    "custrecord_ng_cs_default_adv_show_price",
    "custrecord_ng_cs_default_exhib_move_in",
    "custrecord_ng_cs_default_exhib_move_out",
    "custrecord_ng_cs_default_onst_show_price",
    "custrecord_ng_cs_default_show_date",
    "custrecord_ng_cs_default_show_mgmt_price",
    "custrecord_ng_cs_default_show_move_in",
    "custrecord_ng_cs_default_show_move_out",
    "custrecord_ng_cs_default_show_subsidiary",
    "custrecord_ng_cs_default_sprvisor_markup",
    "custrecord_ng_cs_default_std_show_price",
    "custrecord_ng_cs_default_to_as_st_loc",
    "custrecord_ng_cs_default_transfer_from",
    "custrecord_ng_cs_dflt_booth_order_form",
    "custrecord_ng_cs_dflt_d_calc_date_types",
    "custrecord_ng_cs_dflt_exhibtr_form",
    "custrecord_ng_cs_dflt_labor_date_types",
    "custrecord_ng_cs_dflt_show_mgmt_ord_form",
    "custrecord_ng_cs_dflt_shw_tbl_form",
    "custrecord_ng_cs_discover",
    "custrecord_ng_cs_do_not_prompt_terms",
    "custrecord_ng_cs_dpst_rcpt_template",
    "custrecord_ng_cs_enable_freight_opts_opt",
    "custrecord_ng_cs_enable_graphics_option",
    "custrecord_ng_cs_enable_labor_matrix_opt",
    "custrecord_ng_cs_enable_orientation_opt",
    "custrecord_ng_cs_enable_paytrace",
    "custrecord_ng_cs_enable_rentals",
    "custrecord_ng_cs_enable_rentals_by_form",
    "custrecord_ng_cs_enforce_item_max_qty",
    "custrecord_ng_cs_event_selection_info",
    "custrecord_ng_cs_exempt_estimated_items",
    "custrecord_ng_cs_exhb_inv_email_template",
    "custrecord_ng_cs_exhb_kit_folder_id",
    "custrecord_ng_cs_exhb_kit_path",
    "custrecord_ng_cs_exhb_wo_exluded_cats",
    "custrecord_ng_cs_exhib_invoice_sender",
    "custrecord_ng_cs_exhibitor_serv_phone",
    "custrecord_ng_cs_fclty_addy_template",
    "custrecord_ng_cs_file_upload_folder_id",
    "custrecord_ng_cs_freight_minimum",
    "custrecord_ng_cs_gen_rand_email",
    "custrecord_ng_cs_give_contacts_access",
    "custrecord_ng_cs_graphics_item_cat",
    "custrecord_ng_cs_header_logo_url",
    "custrecord_ng_cs_hide_bthchklst_cnt_info",
    "custrecord_ng_cs_import_log_record_id",
    "custrecord_ng_cs_import_log_search_id",
    "custrecord_ng_cs_inv_email_conditions",
    "custrecord_ng_cs_inv_fail_cc",
    "custrecord_ng_cs_inv_fail_recip",
    "custrecord_ng_cs_inv_fail_sender",
    "custrecord_ng_cs_inv_transfer_type",
    "custrecord_ng_cs_item_rprts_exluded_cats",
    "custrecord_ng_cs_job_num_prefix",
    "custrecord_ng_cs_log_time_zone",
    "custrecord_ng_cs_mass_booth_delete_roles",
    "custrecord_ng_cs_mastercard",
    "custrecord_ng_cs_matrix_option_web",
    "custrecord_ng_cs_name_from_subsidiary",
    "custrecord_ng_cs_name_number_ordering",
    "custrecord_ng_cs_name_number_separator",
    "custrecord_ng_cs_navbar_bckgrnd_color",
    "custrecord_ng_cs_no_billed_order_editing",
    "custrecord_ng_cs_no_prompt_under_zero",
    "custrecord_ng_cs_payment_ar_account",
    "custrecord_ng_cs_payment_type",
    "custrecord_ng_cs_pre_invoicing_form",
    "custrecord_ng_cs_prev_adtl_orders",
    "custrecord_ng_cs_prev_bo_redir_alert",
    "custrecord_ng_cs_prompt_exclusion_roles",
    "custrecord_ng_cs_prompt_for_new_line",
    "custrecord_ng_cs_pymnt_fail_eml_template",
    "custrecord_ng_cs_pymt_rcpt_template",
    "custrecord_ng_cs_rand_email_domain",
    "custrecord_ng_cs_rand_email_prefix",
    "custrecord_ng_cs_rental_forms",
    "custrecord_ng_cs_report_item_display",
    "custrecord_ng_cs_report_line_shade_hex",
    "custrecord_ng_cs_report_xml_folder_id",
    "custrecord_ng_cs_retain_last_item_cat",
    "custrecord_ng_cs_retain_last_show",
    "custrecord_ng_cs_sales_tax_is_on_lines",
    "custrecord_ng_cs_send_exhib_invoice",
    "custrecord_ng_cs_send_invoice_fail_email",
    "custrecord_ng_cs_send_web_pymnt_email",
    "custrecord_ng_cs_settings_access",
    "custrecord_ng_cs_shade_alt_report_lines",
    "custrecord_ng_cs_show_audit_form",
    "custrecord_ng_cs_show_calendar_id",
    "custrecord_ng_cs_show_mgmt_ord_types",
    "custrecord_ng_cs_show_mgt_forms",
    "custrecord_ng_cs_simple_job_numbering",
    "custrecord_ng_cs_supervisor_item",
    "custrecord_ng_cs_tax_auto_sel_txt_filter",
    "custrecord_ng_cs_transfer_count_markup",
    "custrecord_ng_cs_trnsfr_exmpt_cats",
    "custrecord_ng_cs_use_alt_forms",
    "custrecord_ng_cs_use_avalara_tax_message",
    "custrecord_ng_cs_use_canadian_sales_tax",
    "custrecord_ng_cs_use_cancl_charge",
    "custrecord_ng_cs_use_cc_conv_fee",
    "custrecord_ng_cs_use_custom_job",
    "custrecord_ng_cs_use_job_numbering",
    "custrecord_ng_cs_use_multi_cc_proc",
    "custrecord_ng_cs_use_pre_invoicing",
    "custrecord_ng_cs_use_scripted_pynt_frm",
    "custrecord_ng_cs_use_show_auditing",
    "custrecord_ng_cs_use_show_tax",
    "custrecord_ng_cs_use_undep_funds",
    "custrecord_ng_cs_visa",
    "custrecord_ng_cs_web_img_folder_id",
    "custrecord_ng_cs_web_pymnt_fail_cc",
    "custrecord_ng_cs_web_pymnt_fail_recip",
    "custrecord_ng_cs_web_pymnt_notice_sender",
    "custrecord_ng_cs_web_welcome_blurb",
    "custrecord_ng_cs_wo_img",
    "custrecord_ng_cs_wo_logo_img_url",
    "custrecord_ng_cs_wrhs_addy_template",
    "custrecord_ng_cs_evt_mm_attch_folder_id",
    "custrecord_ng_cs_evt_mm_temp_folder_id",
  ];

  let _cs_settings = {
    openShowsSearch: "customsearch_open_shows",
    PaymentSearch: "customsearch_ng_cs_payment_refund_search",
    // ,	AlgoliaWriteURL :				"https://{0}.algolia.net/1/indexes/{1}/"
    // ,	AlgoliaReadURL :				"https://{0}-dsn.algolia.net/1/indexes/{1}/"
    LogBaseUrl:
      "/app/common/search/searchresults.nl?rectype={0}&searchtype=Custom&CUSTRECORD_NGLOG_DETAILS={1}&style=NORMAL&CUSTRECORD_NGLOG_DETAILStype=CONTAINS&report=&grid=&searchid={2}&sortcol=Custom_OWNER_raw&sortdir=ASC&csv=HTML&OfficeXML=F&pdf=&size=50&twbx=F",

    sqftItems: [],
    daysItems: [],
    durationItems: [],
    sqdItems: [],
    freightItems: [],
    colorItems: [],
    sizeItems: [],
    orientationItems: [],
    laborItems: [],
    barrelItems: [],
    freightOptsA: [],
    freightOptsB: [],
    laborOptItemsA: [],
    laborOptItemsB: [],
    graphicsItems: [],

    rType: {
      booth: "customrecord_show_booths",
      showtable: "customrecord_show",
      boothorder: "salesorder",
      payment: "customerpayment",
      refund: "customerrefund",
      deposit: "customerdeposit",
      exhibitor: "customer",
      invoice: "invoice",
      credit: "creditmemo",
      displayform: "customrecord_display_forms",
      showdisplayform: "customrecord_show_display_forms",
      showdate: "customrecord_show_date",
      event: "customrecord_show",
      eventdate: "customrecord_show_date",
      order: "salesorder",
    },

    scripts: {
      lastShow: {
        scriptid: "customscript_last_show_get_set",
        deployid: "customdeploy_last_show_get_set_dep",
      },
      lastItemCat: {
        scriptid: "customscript_ng_cs_sl_last_item_cat",
        deployid: "customdeploy_ng_cs_sl_last_item_cat_dep",
      },
      utilityTasks: {
        scriptid: "customscript_ng_cs_sl_utility_tasks",
        deployid: "customdeploy_ng_cs_sl_utility_tasks_dep",
      },
    },

    lists: {},

    fields: {
      tran: {
        boothId: "custbody_booth",
        eventId: "custbody_show_table",
        orderId: "custbody_booth_order",
        toBeDeleted: "",
      },

      item: {
        sqft: "custitem_is_sqft",
        days: "custitem_is_days",
        showdur: "custitem_show_duration",
        freight: "custitem_is_freight",
        color: "custitem_has_color_options",
        size: "custitem_has_size_options",
        orientation: "custitem_has_orient_options",
        labor: "custitem_labor_item",
        graphics: "custitem_ng_cs_has_graphic_options",
        colorSel: "custitem27",
        sizeSel: "custitem28",
        orientSel: "custitem_orientation",
        graphicSel: "custitem42",
      },

      itemOpts: {
        supervision: "custcol_labor_sup_required",
        labordate: "custcol_labor_date",
        laborendtime: "custcol_labor_end_time",
        laboresthours: "custcol_labor_est_hours",
        laborworkers: "custcol_labor_workers",
        labortime: "custcol_labor_time",
        laboritem: "custcol_ng_cs_labor_item",
      },

      entity: {
        lastshow: "",
        lastwebshow: "",
        lastwebbooth: "",
      },

      showTable: {
        complete: "custrecord_cs_st_show_complete",
      },
    },

    matrixOptionMap: {
      custcol_sizeopt: {
        sourcefrom: "custitem28",
        internalid: "custcol_sizeopt",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Size",
        type: "select",
      },
      custcol_coloropt: {
        sourcefrom: "custitem27",
        internalid: "custcol_coloropt",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Color",
        type: "select",
      },
      custcol_graphmat_substrate: {
        sourcefrom: "custitem42",
        internalid: "custcol_graphmat_substrate",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Graphic Material",
        type: "select",
      },
      custcol_orientation: {
        sourcefrom: "custitem_orientation",
        internalid: "custcol_orientation",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Orientation",
        type: "select",
      },
      custcol1: {
        sourcefrom: "custitem43",
        internalid: "custcol1",
        ismandatory: true,
        ismatrixdimension: true,
        label: "Screen Size",
        type: "select",
      },
      custcol_labor_sup_required: {
        internalid: "custcol_labor_sup_required",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Supervision Required",
        type: "text",
      },
      custcol_attach_document: {
        internalid: "custcol_attach_document",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Attach Supporting File",
        type: "text",
      },
      custcol_labor_date: {
        internalid: "custcol_labor_date",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Date",
        type: "text",
      },
      custcol_labor_est_hours: {
        internalid: "custcol_labor_est_hours",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Estimated Hours",
        type: "text",
      },
      custcol_canc_descr: {
        internalid: "custcol_canc_descr",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Cancelled Item",
        type: "text",
      },
      custcol_labor_workers: {
        internalid: "custcol_labor_workers",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Number of Workers",
        type: "text",
      },
      custcol_labor_time: {
        internalid: "custcol_labor_time",
        ismandatory: true,
        ismatrixdimension: false,
        label: "Time",
        type: "text",
      },
      custcol_labor_end_time: {
        internalid: "custcol_labor_end_time",
        ismandatory: true,
        ismatrixdimension: false,
        label: "End Time",
        type: "text",
      },
    },

    separators: {
      1: " ",
      2: " - ",
      3: " -- ",
      4: " | ",
      5: " || ",
      6: " : ",
      7: " :: ",
      8: " <> ",
    },

    messageTypes: {
      CONFIRMATION: 0,
      INFORMATION: 1,
      WARNING: 2,
      ERROR: 3,
    },

    fileSizeLimits: {
      single: 10240,
      all: 15360,
    },

    _COPY_COLUMNS: [
      "custcol_linecode",
      "description",
      "custcol_custom_carpet_size",
      "custcol_cost_is_estimated",
      "custcol_attached_document",
      "custcol_ng_cs_rental_loc",
      "custcol_ng_cs_rental_start_date",
      "custcol_ng_cs_rental_end_date",
    ],

    _EVENT_FIELDS: [
      "custrecord_adv_ord_date",
      "custrecord_adv_price_level",
      "custrecord_std_price_level",
      "custrecord_site_price_level",
      "custrecord_cancellation_pct",
      "custrecord_tax_rate",
      "custrecord_tax_percent",
      "custrecord_show_job",
      "custrecord_fin_show",
      "custrecord_facility",
      "custrecord_show_venue",
      "custrecord_show_subsidiary",
      "custrecord_show_mgmnt_price_lvl",
      "custrecord_cs_st_send_inventory_date",
      "custrecord_cs_st_return_inventory_date",
      "custrecord_ng_cs_evt_gst_pct",
      "custrecord_ng_cs_evt_pst_pct",
    ],

    _EVENT_FIELDS_S: [
      "custrecord_adv_price_level",
      "custrecord_std_price_level",
      "custrecord_site_price_level",
      "custrecord_tax_rate",
      "custrecord_show_job",
      "custrecord_fin_show",
      "custrecord_facility",
      "custrecord_show_venue",
      "custrecord_show_subsidiary",
      "custrecord_show_mgmnt_price_lvl",
    ],
  };

  /**
   * Result from field value selected
   * @param objValue {Array}
   * @returns {Number || String}
   * */
  const resolveSelectValue = (objValue) => {
    let selVal = null;
    if (objValue && objValue.length !== 0 && objValue[0]?.value)
      selVal = objValue[0].value;
    return selVal;
  };

  /**
   * Results values selected from multi-select field into an array
   * @param objValue {Array}
   * @returns {Array}
   * */
  const resolveMultiSelectValue = (objValue) => {
    let arr = [];
    if (objValue && objValue.length !== 0 && objValue[0]?.value) {
      for (let i = 0; i < objValue.length; i++) {
        arr.push(objValue[i].value);
      }
    }
    return arr;
  };

  /**
   * @returns {CS_Settings}
   */
  const _trigger = () => {
    let _NG_SETTINGS;
    try {
      _NG_SETTINGS = LookupSettings();
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving settings data (2.1)");
      return _cs_settings;
    }

    // // Deprecated settings values
    _cs_settings.GenRandomEmail = _NG_SETTINGS.custrecord_ng_cs_gen_rand_email;
    _cs_settings.InvoiceFailureCC =
      _NG_SETTINGS.custrecord_ng_cs_inv_fail_cc || null;
    _cs_settings.RandEmailDomain =
      _NG_SETTINGS.custrecord_ng_cs_rand_email_domain;
    _cs_settings.RandEmailPrefix =
      _NG_SETTINGS.custrecord_ng_cs_rand_email_prefix;
    _cs_settings.SendInvoiceFailureEmail =
      _NG_SETTINGS.custrecord_ng_cs_send_invoice_fail_email;
    _cs_settings.SendExhibitorInvoiceEmail =
      _NG_SETTINGS.custrecord_ng_cs_send_exhib_invoice;
    _cs_settings.UseAlternateForms =
      _NG_SETTINGS.custrecord_ng_cs_use_alt_forms;

    _cs_settings.DefaultExhibitorDepartment = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_exhb_dept
    );
    _cs_settings.DefaultShowMgmtDepartment = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_show_mgmt_dept
    );
    _cs_settings.ExhibitorInvoiceSender = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_exhib_invoice_sender
    );
    _cs_settings.ExhibitorInvoiceTemplate = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_exhb_inv_email_template
    );
    _cs_settings.InvoiceFailureRecipient = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_inv_fail_recip
    );
    _cs_settings.InvoiceFailureSender = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_inv_fail_sender
    );
    _cs_settings.ExhibitorInvoiceConditions = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_inv_email_conditions
    );

    // // Boolean settings values
    _cs_settings.AllowMassBoothDeletion =
      _NG_SETTINGS.custrecord_ng_cs_allow_mass_booth_delete;
    _cs_settings.AllowMultiBillingParties =
      _NG_SETTINGS.custrecord_ng_cs_allow_mult_billng_part;
    _cs_settings.AllowShowAutoPayment =
      _NG_SETTINGS.custrecord_ng_cs_allow_show_autopay;
    _cs_settings.AutoChargeWebOrders =
      _NG_SETTINGS.custrecord_ng_cs_auto_charge_web_orders;
    _cs_settings.BlockedBilledOrderEditing =
      _NG_SETTINGS.custrecord_ng_cs_no_billed_order_editing;
    _cs_settings.ClearBoothOrderCCDetails =
      _NG_SETTINGS.custrecord_ng_cs_clear_order_cc_details;
    _cs_settings.CustomJobNumbering =
      _NG_SETTINGS.custrecord_ng_cs_custom_job_numbering;
    _cs_settings.DefaultTargetFromShow =
      _NG_SETTINGS.custrecord_ng_cs_default_to_as_st_loc;
    _cs_settings.DoNotPromptIfTerms =
      _NG_SETTINGS.custrecord_ng_cs_do_not_prompt_terms;
    _cs_settings.EnableGraphicOption =
      _NG_SETTINGS.custrecord_ng_cs_enable_graphics_option;
    _cs_settings.EnableOrientationOption =
      _NG_SETTINGS.custrecord_ng_cs_enable_orientation_opt;
    _cs_settings.EnablePayTrace = _NG_SETTINGS.custrecord_ng_cs_enable_paytrace;
    _cs_settings.EnableRentals = _NG_SETTINGS.custrecord_ng_cs_enable_rentals;
    _cs_settings.EnableRentalsByForm =
      _NG_SETTINGS.custrecord_ng_cs_enable_rentals_by_form;
    //	_cs_settings.EnableWorkOrderPrinting =		_NG_SETTINGS.custrecord_ng_cs_enable_wo_printing;
    _cs_settings.EnforceItemMaxQuantity =
      _NG_SETTINGS.custrecord_ng_cs_enforce_item_max_qty;
    _cs_settings.ExemptEstimatedItems =
      _NG_SETTINGS.custrecord_ng_cs_exempt_estimated_items;
    _cs_settings.GetNonWebOrderAuth =
      _NG_SETTINGS.custrecord_ng_cs_auth_non_web_orders;
    _cs_settings.GiveContactsWebStoreAccess =
      _NG_SETTINGS.custrecord_ng_cs_give_contacts_access;
    _cs_settings.HideChecklistContact =
      _NG_SETTINGS.custrecord_ng_cs_hide_bthchklst_cnt_info;
    _cs_settings.MultiCCProcessing =
      _NG_SETTINGS.custrecord_ng_cs_use_multi_cc_proc;
    _cs_settings.NoPromptUnderZero =
      _NG_SETTINGS.custrecord_ng_cs_no_prompt_under_zero;
    _cs_settings.PreventAdditionalOrders =
      _NG_SETTINGS.custrecord_ng_cs_prev_adtl_orders;
    _cs_settings.PrevntBthOrderRedirectAlert =
      _NG_SETTINGS.custrecord_ng_cs_prev_bo_redir_alert;
    _cs_settings.ProcessingProfile =
        _NG_SETTINGS.custrecord_ng_cs_prefrd_pymt_processor;
    _cs_settings.PromptForNewLines =
      _NG_SETTINGS.custrecord_ng_cs_prompt_for_new_line;
    _cs_settings.RetainLastItemCat =
      _NG_SETTINGS.custrecord_ng_cs_retain_last_item_cat; // DEPRECATED
    _cs_settings.RetainLastShow =
      _NG_SETTINGS.custrecord_ng_cs_retain_last_show;
    _cs_settings.SalesTaxOnItemLines =
      _NG_SETTINGS.custrecord_ng_cs_sales_tax_is_on_lines;
    _cs_settings.SendWebPaymentNotice =
      _NG_SETTINGS.custrecord_ng_cs_send_web_pymnt_email;
    _cs_settings.ShadeAltRptLines =
      _NG_SETTINGS.custrecord_ng_cs_shade_alt_report_lines;
    _cs_settings.SimpleJobNumbering =
      _NG_SETTINGS.custrecord_ng_cs_simple_job_numbering;
    _cs_settings.UndepositedFunds =
      _NG_SETTINGS.custrecord_ng_cs_use_undep_funds;
    _cs_settings.UseCanadianSalesTax =
      _NG_SETTINGS.custrecord_ng_cs_use_canadian_sales_tax;
    _cs_settings.UseCancellationCharge =
      _NG_SETTINGS.custrecord_ng_cs_use_cancl_charge;
    _cs_settings.UseConvenienceFee =
      _NG_SETTINGS.custrecord_ng_cs_use_cc_conv_fee;
    _cs_settings.UseCustomJob = _NG_SETTINGS.custrecord_ng_cs_use_custom_job;
    _cs_settings.UseJobNumbering =
      _NG_SETTINGS.custrecord_ng_cs_use_job_numbering;
    _cs_settings.UseScriptedPaymentForm =
      _NG_SETTINGS.custrecord_ng_cs_use_scripted_pynt_frm;
    _cs_settings.UseSubsidiaryName =
      _NG_SETTINGS.custrecord_ng_cs_name_from_subsidiary;
    _cs_settings.UseTaxCode = _NG_SETTINGS.custrecord_ng_cs_use_show_tax; // DEPRECATE

    // // Folder ID values (integer or null)
    _cs_settings.ExhibitorKitFolderID =
      _NG_SETTINGS.custrecord_ng_cs_exhb_kit_folder_id || null;
    _cs_settings.ImportCSVFolder =
      _NG_SETTINGS.custrecord_ng_cs_csv_import_folder_id || null;
    _cs_settings.ReportXMLFolderID =
      _NG_SETTINGS.custrecord_ng_cs_report_xml_folder_id || null;
    _cs_settings.WebImageFolderID =
      _NG_SETTINGS.custrecord_ng_cs_web_img_folder_id || null;
    _cs_settings.MassMailerFolderID =
      _NG_SETTINGS.custrecord_ng_cs_evt_mm_attch_folder_id || null;
    _cs_settings.MassMailerTempFolderID =
      _NG_SETTINGS.custrecord_ng_cs_evt_mm_temp_folder_id || null;

    // / Percentage values
    _cs_settings.CancellationChargePct =
      _NG_SETTINGS.custrecord_ng_cs_def_canc_chrg_pct || "0.0%";
    _cs_settings.ConvenienceFeeRate =
      _NG_SETTINGS.custrecord_ng_cs_cc_conv_fee_rate || "0.00%";
    _cs_settings.TransferCountMarkup =
      _NG_SETTINGS.custrecord_ng_cs_transfer_count_markup || "0.00%";

    // // Text values (or empty string)
    _cs_settings.ExhibitorKitFolderPath =
      _NG_SETTINGS.custrecord_ng_cs_exhb_kit_path || "";
    _cs_settings.FacilityAddyTmplt =
      _NG_SETTINGS.custrecord_ng_cs_fclty_addy_template || "";
    _cs_settings.JobNumberPrefix =
      _NG_SETTINGS.custrecord_ng_cs_job_num_prefix || "";
    _cs_settings.NSSystemDomain =
      _NG_SETTINGS.custrecord_ng_cs_acct_domain_url || "";
    _cs_settings.RptLineShadeHex =
      _NG_SETTINGS.custrecord_ng_cs_report_line_shade_hex || "";
    _cs_settings.SalesTaxFilter =
      _NG_SETTINGS.custrecord_ng_cs_tax_auto_sel_txt_filter || "";
    _cs_settings.WarehouseAddyTmplt =
      _NG_SETTINGS.custrecord_ng_cs_wrhs_addy_template || "";

    // // Text values (or null)
    _cs_settings.ActivityLogRecordId =
      _NG_SETTINGS.custrecord_ng_cs_activity_log_rec_id || null;
    _cs_settings.ActivityLogSearchId =
      _NG_SETTINGS.custrecord_ng_cs_activity_log_srch_id || null;
    _cs_settings.BoothNumLineText =
      _NG_SETTINGS.custrecord_ng_cs_booth_num_line_text || null;
    _cs_settings.DefaultDepositAccount =
      _NG_SETTINGS.custrecord_ng_cs_def_dep_account || null;
    _cs_settings.DefaultShowTableForm =
      _NG_SETTINGS.custrecord_ng_cs_dflt_shw_tbl_form || null;
    _cs_settings.ImportLogRecordID =
      _NG_SETTINGS.custrecord_ng_cs_import_log_record_id || null;
    _cs_settings.ImportLogSearchID =
      _NG_SETTINGS.custrecord_ng_cs_import_log_search_id || null;
    _cs_settings.WebPaymentFailureCC =
      _NG_SETTINGS.custrecord_ng_cs_web_pymnt_fail_cc || null;

    // // Integer values
    _cs_settings.ShowCalendar =
      _NG_SETTINGS.custrecord_ng_cs_show_calendar_id || "1";
    _cs_settings.FreightMinimum = !isNaN(
      Number(_NG_SETTINGS.custrecord_ng_cs_freight_minimum)
    )
      ? Number(_NG_SETTINGS.custrecord_ng_cs_freight_minimum || "0")
      : 0;

    // // File and file url values
    _cs_settings.CSVImportTemplate = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_csv_import_file
    );
    _cs_settings.WorkOrderImage = _NG_SETTINGS.custrecord_ng_cs_wo_img || "";
    _cs_settings.WorkOrderImageURL = nXML.escape({
      xmlText: _NG_SETTINGS.custrecord_ng_cs_wo_logo_img_url || "",
    });

    // // Item values
    _cs_settings.AuthItem = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cc_auth_item
    );
    _cs_settings.CancellationChargeID = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cancl_charge_item
    );
    _cs_settings.ConvenienceFeeItem = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cc_conv_fee_item
    );
    _cs_settings.SupervisorItem = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_supervisor_item
    );

    // // Credit Card values
    _cs_settings.AmexPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_amex
    );
    _cs_settings.DiscoverPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_discover
    );
    _cs_settings.MastercardPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_mastercard
    );
    _cs_settings.VisaPayMethodId = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_visa
    );

    // // Forms values
    _cs_settings.DefaultBoothOrderForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_booth_order_form
    );
    _cs_settings.DefaultExhibitorForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_exhibtr_form
    );
    _cs_settings.DepositReceiptForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dpst_rcpt_template
    );
    _cs_settings.PaymentReceiptForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_pymt_rcpt_template
    );
    _cs_settings.DefaultShowMgmtForm = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_show_mgmt_ord_form
    );

    _cs_settings.AddItemFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_add_item_forms
    );
    _cs_settings.BoothOrderFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_booth_ord_forms
    );
    _cs_settings.RentalsFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_rental_forms
    );
    _cs_settings.ShowMgtFormIdListing = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_show_mgt_forms
    );

    // // Date type values
    _cs_settings.DefaultExhibMoveInDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_exhib_move_in
    );
    _cs_settings.DefaultExhibMoveOutDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_exhib_move_out
    );
    _cs_settings.DefaultShowDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_date
    );
    _cs_settings.DefaultShowMoveInDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_move_in
    );
    _cs_settings.DefaultShowMoveOutDateType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_move_out
    );

    _cs_settings.DCalcDateTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_d_calc_date_types
    );
    _cs_settings.LaborDateTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_dflt_labor_date_types
    );

    // // Price level values
    _cs_settings.DefaultAdvPriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_adv_show_price
    );
    _cs_settings.DefaultOnSitePriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_onst_show_price
    );
    _cs_settings.DefaultShowMgmtPriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_mgmt_price
    );
    _cs_settings.DefaultStdPriceLvl = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_std_show_price
    );

    // // Order type values
    _cs_settings.DefaultExhibitorOrderType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_exhb_ord_type
    );
    _cs_settings.DefaultShowMgmtOrderType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_show_mgmt_ord_type
    );

    _cs_settings.ConvenienceFeeOrderTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
    );
    _cs_settings.ShowMgmtOrderTypes = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_show_mgmt_ord_types
    );

    // // Category/Subcategory values
    _cs_settings.GraphicsItemCat = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_graphics_item_cat
    );

    _cs_settings.AutoChargeCategoryExclusions = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_autochrg_cat_excl
    );
    _cs_settings.ExhibWOExlcudeCats = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_exhb_wo_exluded_cats
    );
    _cs_settings.ItemReportsExlcudeCats = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_item_rprts_exluded_cats
    );
    _cs_settings.TransferExemptCats = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_trnsfr_exmpt_cats
    );

    // // Role values
    _cs_settings.CustomerWebAccessRole = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_cust_web_access_role
    );

    _cs_settings.MassBoothDeletionRoles = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_mass_booth_delete_roles
    );
    _cs_settings.NoPromptRoles = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_prompt_exclusion_roles
    );

    // // Other select/multi-select values
    _cs_settings.CancellationChargeThreshold = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_canc_threshold
    );
    _cs_settings.ConvFeeTax = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_conv_fee_zero_tax
    );
    _cs_settings.DefaultDepositAccount = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_def_dep_account
    );
    _cs_settings.DefaultSubsidiary = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_show_subsidiary
    );
    _cs_settings.DefaultTransferSourceLoc = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_default_transfer_from
    );
    _cs_settings.NameNumberOrder = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_name_number_ordering
    );
    _cs_settings.NameNumberSeparator = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_name_number_separator
    );
    _cs_settings.PaymentARAccount = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_payment_ar_account
    );
    _cs_settings.PaymentFailureTemplate = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_pymnt_fail_eml_template
    );
    _cs_settings.PaymentType = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_payment_type
    );
    _cs_settings.RptItemNameDisplay = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_report_item_display
    );
    _cs_settings.TimeZone = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_log_time_zone
    );
    _cs_settings.WebPaymentNoticeSender = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_web_pymnt_notice_sender
    );
    _cs_settings.WebPaymentFailureRecipient = resolveSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_web_pymnt_fail_recip
    );

    _cs_settings.AuthorizedBilledOrderEditors = resolveMultiSelectValue(
      _NG_SETTINGS.custrecord_ng_cs_billed_ord_edit_users
    );

    return _cs_settings;
  };

  /**
   * @returns {CS_Settings_Fields}
   */
  const LookupSettings = () => {
    return search.lookupFields({
      type: "customrecord_ng_cs_settings",
      id: "1",
      columns: _SettingsFields,
    });
  };

  let _csFunc = {
    /**
     * Searches for items flagged for use with the item selection fields and loads the appropriate settings arrays
     * @param {Number|String} subsidiary Internal ID of the subsidiary to filter items on
     * @returns {void}
     */
    getSelectionItems: (subsidiary) => {
      _cs_settings.sqftItems = [];
      _cs_settings.daysItems = [];
      _cs_settings.durationItems = [];
      _cs_settings.sqdItems = [];
      _cs_settings.freightItems = [];
      _cs_settings.colorItems = [];
      _cs_settings.sizeItems = [];
      _cs_settings.orientationItems = [];
      _cs_settings.laborItems = [];
      _cs_settings.graphicsItems = [];

      let searchA;
      let searchB;
      let i;

      let filtA_sub = [
        [_cs_settings.fields.item.sqft, "is", "T"],
        "or",
        [_cs_settings.fields.item.days, "is", "T"],
        "or",
        [_cs_settings.fields.item.showdur, "is", "T"],
        "or",
        [_cs_settings.fields.item.freight, "is", "T"],
        "or",
        [_cs_settings.fields.item.labor, "is", "T"],
      ];
      let filtA = [["isinactive", "is", "F"], "and", filtA_sub];
      if (!NG.tools.isEmpty(subsidiary)) {
        filtA.push("and", ["subsidiary", "anyof", ["@NONE@", subsidiary]]);
      }
      let colsA = [
        search.createColumn({ name: _cs_settings.fields.item.sqft }),
        search.createColumn({ name: _cs_settings.fields.item.days }),
        search.createColumn({ name: _cs_settings.fields.item.showdur }),
        search.createColumn({ name: _cs_settings.fields.item.freight }),
        search.createColumn({ name: _cs_settings.fields.item.labor }),
      ];
      try {
        searchA = NG.tools.getSearchResults("item", filtA, colsA);
      } catch (err) {}

      if (!NG.tools.isEmpty(searchA)) {
        searchA.forEach((resA) => {
          if (
            resA.getValue({ name: _cs_settings.fields.item.sqft }) &&
            resA.getValue({ name: _cs_settings.fields.item.days })
          ) {
            _cs_settings.sqdItems.push(resA.id);
          } else if (resA.getValue({ name: _cs_settings.fields.item.sqft })) {
            _cs_settings.sqftItems.push(resA.id);
          } else if (resA.getValue({ name: _cs_settings.fields.item.days })) {
            _cs_settings.daysItems.push(resA.id);
          }
          if (resA.getValue({ name: _cs_settings.fields.item.showdur })) {
            _cs_settings.durationItems.push(resA.id);
          }
          if (resA.getValue({ name: _cs_settings.fields.item.freight })) {
            _cs_settings.freightItems.push(resA.id);
          }
          if (resA.getValue({ name: _cs_settings.fields.item.labor })) {
            _cs_settings.laborItems.push(resA.id);
          }
        });
      }

      let filtB_sub = [
        [_cs_settings.fields.item.color, "is", "T"],
        "or",
        [_cs_settings.fields.item.size, "is", "T"],
      ];
      if (_cs_settings.EnableOrientationOption) {
        filtB_sub.push("or", [_cs_settings.fields.item.orientation, "is", "T"]);
      }
      if (_cs_settings.EnableGraphicOption) {
        filtB_sub.push("or", [_cs_settings.fields.item.graphics, "is", "T"]);
      }
      let filtB = [
        ["isinactive", "is", "F"],
        "and",
        ["matrix", "is", "T"],
        "and",
        ["matrixchild", "is", "F"],
        "and",
        filtB_sub,
      ];
      if (!NG.tools.isEmpty(subsidiary)) {
        filtB.push("and", ["subsidiary", "anyof", ["@NONE@", subsidiary]]);
      }
      let colsB = [
        search.createColumn({ name: "itemid" }),
        search.createColumn({ name: _cs_settings.fields.item.color }),
        search.createColumn({ name: _cs_settings.fields.item.size }),
      ];
      if (_cs_settings.EnableOrientationOption) {
        colsB.push(
          search.createColumn({ name: _cs_settings.fields.item.orientation })
        );
      }
      if (_cs_settings.EnableGraphicOption) {
        colsB.push(
          search.createColumn({ name: _cs_settings.fields.item.graphics })
        );
      }
      try {
        searchB = NG.tools.getSearchResults("item", filtB, colsB);
      } catch (err) {}
      if (!NG.tools.isEmpty(searchB)) {
        searchB.forEach((resB) => {
          if (resB.getValue({ name: _cs_settings.fields.item.color })) {
            _cs_settings.colorItems.push(resB.id);
          }
          if (resB.getValue({ name: _cs_settings.fields.item.size })) {
            _cs_settings.sizeItems.push(resB.id);
          }
          if (
            _cs_settings.EnableOrientationOption &&
            resB.getValue({ name: _cs_settings.fields.item.orientation })
          ) {
            _cs_settings.orientationItems.push(resB.id);
          }
          if (
            _cs_settings.EnableGraphicOption &&
            resB.getValue({ name: _cs_settings.fields.item.graphics })
          ) {
            _cs_settings.graphicsItems.push(resB.id);
          }
        });
      }
    },

    /**
     * Retrieve the last event set on a transaction by the current user
     * @param values
     * @returns {string|*|null}
     */
    getLastShow: (values) => {
      let path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastShow.scriptid,
        deploymentId: _cs_settings.scripts.lastShow.deployid,
        returnExternalUrl: true,
      });
      let request = https.post({
        url: path,
        body: values,
      });
      return !NG.tools.isEmpty(request) ? request.body : null;
    },

    /**
     * Store the event internal ID value just used by the user on their employee record for later retrieval
     * @param values
     * @returns {void}
     */
    setLastShow: (values) => {
      let path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastShow.scriptid,
        deploymentId: _cs_settings.scripts.lastShow.deployid,
        returnExternalUrl: true,
      });
      https.post({
        url: path,
        body: values,
        headers: {},
      });
    },

    /**
     * Retrieve the last item category set on a booth order by the current user
     * @param values
     * @returns {string|*|null}
     */
    getLastItemCat: (values) => {
      let path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastItemCat.scriptid,
        deploymentId: _cs_settings.scripts.lastItemCat.deployid,
        returnExternalUrl: true,
      });
      let request = https.post({
        url: path,
        body: values,
        headers: {},
      });
      return !NG.tools.isEmpty(request) ? request.body : null;
    },

    /**
     * Store the item category internal ID value just used by the user on their employee record for later retrieval
     * @param values
     * @returns {void}
     */
    setLastItemCat: (values) => {
      let path = url.resolveScript({
        scriptId: _cs_settings.scripts.lastItemCat.scriptid,
        deploymentId: _cs_settings.scripts.lastItemCat.deployid,
        returnExternalUrl: true,
      });
      https.post({
        url: path,
        body: values,
        headers: {},
      });
    },

    /**
     *
     * @param {Field} eventsField NS (multi) select field object to add event select options
     * @param {Boolean} isMulti Flag to designate field as a multi-select field instead of regular select field
     * @returns {Field}
     */
    getOpenShows: (eventsField, isMulti) => {
      if (!isMulti) {
        eventsField.addSelectOption({ value: "", text: "", isSelected: true });
      }
      let sFilt = [
        ["isinactive", "is", "F"],
        "and",
        ["custrecord_cs_st_show_complete", "anyof", ["@NONE@", "2"]],
      ];
      let sCols = [
        search.createColumn({ name: "name" }),
        search.createColumn({ name: "custrecord_show_type" }),
      ];

      let openEvents;
      try {
        openEvents = NG.tools.getSearchResults(
          "customrecord_show",
          sFilt,
          sCols,
          null,
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered getting current show listing");
      }

      if (!NG.tools.isEmpty(openEvents)) {
        let eventData = [];
        openEvents.forEach((res) => {
          eventData.push({
            id: res.id,
            name: res.getValue({ name: "name" }),
          });
        });
        eventData.sort((a, b) => {
          return a.name > b.name ? 1 : b.name > a.name ? -1 : 0;
        });
        eventData.forEach((event) => {
          eventsField.addSelectOption({ value: event.id, text: event.name });
        });
      }

      return eventsField;
    },

    checkDuplicateContact: (email, fName, lName, cName) => {
      //			let filt = [];
      //			if (!NG.tools.isEmpty(email)) {
      //				filt.push(new nlobjSearchFilter("email", null, "is", email, null));
      //			} else if (!NG.tools.isEmpty(fName) && !NG.tools.isEmpty(lName)) {
      //				filt.push(new nlobjSearchFilter("firstname", null, "is", fName, null), new nlobjSearchFilter("lastName", null, "is", lName, null));
      //			} else if (!NG.tools.isEmpty(cName)) {
      //				filt.push(new nlobjSearchFilter("entityid", null, "contains", cName, null));
      //			} else {
      //				return null;
      //			}
      //			let dupeSearch = null;
      //			try {
      //				dupeSearch = nlapiSearchRecord("contact", null, filt, null);
      //			} catch (err) {
      //				NG.log.logError(err, "Error encountered searching for duplicate contacts", "Search values: " + JSON.stringify({ email : email , fname : fName , lName : lName }));
      //			}
      //
      //			if (dupeSearch != null) {
      //				return dupeSearch[0].getId();
      //			} else {
      return null;
      //			}
    },

    checkDuplicateContactAlt: (email, fName, lName, cName, exhbID) => {
      //			let filt = [
      //				new nlobjSearchFilter("company", null, "anyof", [exhbID], null)
      //			);
      //			if (!NG.tools.isEmpty(fName) && !NG.tools.isEmpty(lName)) {
      //				filt.push(new nlobjSearchFilter("firstname", null, "is", fName, null), new nlobjSearchFilter("lastName", null, "is", lName, null));
      //			} else if (!NG.tools.isEmpty(cName)) {
      //				filt.push(new nlobjSearchFilter("entityid", null, "contains", cName, null));
      //			} else {
      //				return null;
      //			}
      //			let dupeSearch = null;
      //			try {
      //				dupeSearch = nlapiSearchRecord("contact", null, filt, null);
      //			} catch (err) {
      //				NG.log.logError(err, "Error encountered searching for duplicate contacts", "Search values: " + JSON.stringify({ email : email , fname : fName , lName : lName }));
      //			}
      //
      //			if (dupeSearch != null) {
      //				return dupeSearch[0].getId();
      //			} else {
      return null;
      //			}
    },

    checkDuplicateExhibitor: (filt, companyName) => {
      //			let csvCNameTest = companyName.replace(/[,.()-;!]/g, "").replace(" & ", " and ").toUpperCase();
      //			let exhbID = null;
      //			let cols = [
      //				new nlobjSearchColumn("companyname", null, null),
      //				new nlobjSearchColumn("entityid", null, null)
      //			);
      //			let exhbSearch = null;
      //			try {
      //				exhbSearch = nlapiCreateSearch("customer", filt, cols);
      //			} catch (err) {
      //				if (err.name == "SSS_INVALID_SRCH_FILTER_EXPR") {
      //					NG.log.logError(err, "Error encountered building exhibitor search", JSON.stringify(filt));
      //					throw err;
      //				} else {
      //					NG.log.logError(err, "Error encountered building exhibitor search", JSON.stringify(filt));
      //				}
      //			}
      //			let search = null;
      //			try {
      //				search = exhbSearch!= null ? exhbSearch.runSearch() : null;
      //			} catch (err) {
      //				NG.log.logError(err, "Error encountered during duplicate exhibitor search");
      //			}
      //			if (search != null) {
      //				let results = NG.tools.getSearchResults(search, false);
      //				if (results != null && results.length > 0) {
      //					for (let i = 0; i < results.length; i++) {
      //						let sCNameTest = !NG.tools.isEmpty(results[i].getValue("companyname")) ? results[i].getValue("companyname").replace(/[,.()-;!]/g, "").replace(" & ", " and ").replace("&amp;", "and").toUpperCase() : "";
      //						let sCEntTest = !NG.tools.isEmpty(results[i].getValue("entityid")) ? results[i].getValue("entityid").replace(/[,.()-;!]/g, "").replace(" & ", " and ").replace("&amp;", "and").toUpperCase() : "";
      //						let m1 = !NG.tools.isEmpty(sCNameTest) ? csvCNameTest.search(sCNameTest) >= 0 : false;
      //						let m2 = !NG.tools.isEmpty(sCNameTest) ? sCNameTest.search(csvCNameTest) >= 0 : false;
      //						let m3 = !NG.tools.isEmpty(sCEntTest) ? csvCNameTest.search(sCEntTest) >= 0 : false;
      //						let m4 = !NG.tools.isEmpty(sCEntTest) ? sCEntTest.search(csvCNameTest) >= 0 : false;
      //						let csvWordCount = csvCNameTest.split(" ").length;
      //						let cNameWordCount = sCNameTest.split(" ").length;
      //						let cEntWordCount = sCEntTest.split(" ").length;
      //						let countMatch = csvWordCount > 1 ? (cNameWordCount > 1 || cEntWordCount > 1) : (csvWordCount == 1 ? (cNameWordCount == 1 || cEntWordCount == 1) : true);
      //						if ((m1 || m2 || m3 || m4) && countMatch) {
      //							exhbID = results[i].getId();
      //							log.audit({ title : "Dupe exhibitor search match" , details : "CSV Name: {0} -- Search Name: {1} ({2})".NG_Format(companyName, results[i].getValue("companyname"), results[i].getValue("entityid")) });
      //							break;
      //						}
      //					}
      //				}
      //			}

      return null; // exhbID;
    },

    /**
     * Returns array with credit card payment method ID values
     * @returns [ ]
     */
    CreditCardPayMethodIDs: () => {
      let cards = [];
      if (_cs_settings.MastercardPayMethodId !== null)
        cards.push(_cs_settings.MastercardPayMethodId);
      if (_cs_settings.VisaPayMethodId !== null)
        cards.push(_cs_settings.VisaPayMethodId);
      if (_cs_settings.AmexPayMethodId !== null)
        cards.push(_cs_settings.AmexPayMethodId);
      if (_cs_settings.DiscoverPayMethodId !== null)
        cards.push(_cs_settings.DiscoverPayMethodId);
      return cards;
    },

    /**
     * Returns object with credit card payment method ID values
     * @returns { {a, d, m, v} }
     */
    CreditCardPayMethodObj: () => {
      let cards = {};
      if (_cs_settings.MastercardPayMethodId != null)
        cards.m = {
          name: "MasterCard",
          id: _cs_settings.MastercardPayMethodId,
        };
      if (_cs_settings.VisaPayMethodId !== null)
        cards.v = { name: "Visa", id: _cs_settings.VisaPayMethodId };
      if (_cs_settings.AmexPayMethodId !== null)
        cards.a = { name: "Amex", id: _cs_settings.AmexPayMethodId };
      if (_cs_settings.DiscoverPayMethodId !== null)
        cards.d = {
          name: "Discover",
          id: _cs_settings.DiscoverPayMethodId,
        };
      return cards;
    },

    /**
     * checks a given string for a valid credit card
     * @param {String} val Credit card string value
     * @param {Boolean} alt Return Payment method ID instead of numeric card type
     * @returns {Number}
     *		-1	invalid
     *		1	mastercard
     *		2	visa
     *		3	amex
     *		4	discover
     */
    checkCC: (val, alt) => {
      String.prototype.startsWith = (str) => {
        return this.match(`^${str}`) === str;
      };

      Array.prototype.has = (v, i) => {
        for (let j = 0; j < this.length; j++) {
          if (this[j] === v) return !i ? true : j;
        }
        return false;
      };

      // get rid of all non-numbers (space etc)
      val = val.replace(/[^0-9]/g, "");

      // now get digits
      let d = [];
      let a = 0;
      let len = 0;
      let cval = val;
      while (cval !== 0) {
        d[a] = cval % 10;
        cval -= d[a];
        cval /= 10;
        a++;
        len++;
      }

      if (len < 13) {
        return -1;
      }

      let cType = -1;

      // mastercard
      if (val.startsWith("5")) {
        if (len !== 16) {
          return -1;
        }
        cType = !alt ? _cs_settings.MastercardPayMethodId || -1 : 1;
      }
      // visa
      else if (val.startsWith("4")) {
        if (len !== 16 && len !== 13) {
          return -1;
        }
        cType = !alt ? _cs_settings.VisaPayMethodId || -1 : 2;
      }
      // amex
      else if (val.startsWith("34") || val.startsWith("37")) {
        if (len !== 15) {
          return -1;
        }
        cType = !alt ? _cs_settings.AmexPayMethodId || -1 : 3;
      }
      // discover
      else if (val.startsWith("6011")) {
        if (len !== 15 && len !== 16) {
          return -1;
        }
        cType = !alt ? _cs_settings.DiscoverPayMethodId || -1 : 4;
      } else {
        return -1; // invalid cc company
      }

      // lets do some calculation
      let sum = 0;
      let i;
      for (i = 1; i < len; i += 2) {
        let s = d[i] * 2;
        sum += s % 10;
        sum += (s - (s % 10)) / 10;
      }

      for (i = 0; i < len; i += 2) {
        sum += d[i];
      }

      // must be %10
      if (sum % 10 !== 0) {
        return -1;
      }

      return cType;
    },

    selectCardType: (cardNumber, isClient, numberField, methodField) => {
      let ccType;
      switch (this.checkCC(cardNumber, true)) {
        case 1: // Mastercard
          ccType = _cs_settings.MastercardPayMethodId;
          break;
        case 2: // VISA
          ccType = _cs_settings.VisaPayMethodId;
          break;
        case 3: // AmEx
          ccType = _cs_settings.AmexPayMethodId;
          break;
        case 4: // Discover
          ccType = _cs_settings.DiscoverPayMethodId;
          break;
        default:
          if (isClient) {
            window.alert("Invalid card number entered. Please try again.");
            require(["N/currentRecord"], (currentRecord) => {
              currentRecord.get().setValue({
                fieldId: numberField,
                value: "",
                ignoreFieldChange: false,
              });
            });
            document.getElementById(numberField).focus();
            return;
          }
          return null;
      }
      if (!NG.tools.isEmpty(ccType)) {
        if (isClient) {
          require(["N/currentRecord"], (currentRecord) => {
            currentRecord.get().setValue({
              fieldId: methodField,
              value: ccType,
              ignoreFieldChange: false,
            });
          });
        }
      }
      if (!isClient) {
        return ccType;
      }
    },

    getShowDates: (showTableId, summary, addTimes, dateTypes) => {
      let showDateTypeId = _cs_settings.DefaultShowDateType;

      if (!NG.tools.isEmpty(showDateTypeId)) {
        let dateFilt = [
          ["custrecord_show_number_date", "anyof", [showTableId]],
        ];
        if (!NG.tools.isEmpty(dateTypes) && dateTypes.length > 0) {
          dateFilt.push("and", ["custrecord_date_type", "anyof", dateTypes]);
        } else {
          dateFilt.push("and", [
            "custrecord_date_type",
            "anyof",
            [showDateTypeId],
          ]);
        }
        let dateCols = [
          search.createColumn({ name: "custrecord_date", summary }),
        ];
        if (NG.tools.isEmpty(summary)) {
          dateCols[0].sort = "ASC";
        }
        if (addTimes) {
          if (!NG.tools.isEmpty(summary)) {
            dateCols.push(
              search.createColumn({
                name: "custrecord_start_time",
                summary: "group",
              }),
              search.createColumn({
                name: "custrecord_end_time",
                summary: "group",
              })
            );
          } else {
            dateCols.push(
              search.createColumn({ name: "custrecord_start_time" }),
              search.createColumn({ name: "custrecord_end_time" })
            );
          }
        }
        let dateSearch;
        try {
          dateSearch = NG.tools.getSearchResults(
            "customrecord_show_date",
            dateFilt,
            dateCols,
            null,
            25,
            false,
            false
          );
        } catch (err) {
          NG.log.logError(err, "Error encountered searching for show dates");
          dateSearch = [];
        }
        if (!NG.tools.isEmpty(dateSearch) && dateSearch.length > 0) {
          let response;
          if (!NG.tools.isEmpty(summary)) {
            if (addTimes) {
              response = {
                date: dateSearch[0].getValue(dateCols[0]),
                start: dateSearch[0].getValue(dateCols[1]),
                end: dateSearch[0].getValue(dateCols[2]),
              };
            } else {
              response = dateSearch[0].getValue(dateCols[0]);
            }
          } else {
            let dateList = [];
            response = [];
            for (let i = 0; i < dateSearch.length; i++) {
              let date = dateSearch[i].getValue(dateCols[0]);
              if (!NG.tools.isInArray(date, dateList)) {
                let data = {
                  date,
                };
                if (addTimes) {
                  data.start = dateSearch[i].getValue(dateCols[1]);
                  data.end = dateSearch[i].getValue(dateCols[2]);
                }
                response.push(data);
                dateList.push(date);
              }
            }
          }
          return response;
        }
      } else {
        return null;
      }
    },

    getStartDate: (showTable) => {
      let startDateT = this.getShowDates(showTable, "min", false, null);
      let startDateD = !NG.tools.isEmpty(startDateT)
        ? format.parse({
            value: startDateT,
            type: format.Type.DATE,
          })
        : null;
      return !NG.tools.isEmpty(startDateD) ? startDateD.getTime() : null;
    },

    getRateType: (showTableId, dateTxt) => {
      let date = format
        .parse({ value: dateTxt, type: format.Type.DATE })
        .getTime();
      let advanced = false;
      let standard = false;
      let onsite = false;

      let startDate;

      let showData;
      try {
        showData = search.lookupFields({
          type: "customrecord_show",
          id: showTableId,
          columns: [
            "custrecord_adv_ord_date",
            "custrecord_adv_price_level",
            "custrecord_std_price_level",
            "custrecord_site_price_level",
          ],
        });
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered getting show table price levels"
        );
      }

      if (!NG.tools.isEmpty(showData)) {
        startDate = this.getStartDate(showTableId);
        if (!NG.tools.isEmpty(showData.custrecord_adv_ord_date)) {
          let advDate = format
            .parse({
              value: showData.custrecord_adv_ord_date,
              type: format.Type.DATE,
            })
            .getTime();

          if (date <= advDate) {
            advanced = true;
          } else if (!NG.tools.isEmpty(startDate) && date >= startDate) {
            if (!NG.tools.isEmpty(showData.custrecord_site_price_level)) {
              onsite = true;
            } else {
              standard = true;
            }
          } else {
            standard = true;
          }
        } else if (!NG.tools.isEmpty(startDate) && date >= startDate) {
          if (!NG.tools.isEmpty(showData.custrecord_site_price_level))
            onsite = true;
          else standard = true;
        } else {
          standard = true;
        }
      } else {
        standard = true;
      }

      return {
        a: advanced,
        s: standard,
        o: onsite,
      };
    },

    getExemptedTotalOld: (showTableId, booth, entity, orderID, catExempt) => {
      let billableTotal = Number(0);

      let filt = [
        ["mainline", "is", "F"],
        "and",
        ["cogs", "is", "F"],
        "and",
        ["taxline", "is", "F"],
        "and",
        ["shipping", "is", "F"],
        "and",
        ["custbody_to_be_deleted", "is", "F"],
        "and",
        ["item.type", "noneof", ["Subtotal"]],
      ];
      if (_cs_settings.AllowMultiBillingParties && !NG.tools.isEmpty(entity)) {
        filt.push("and", ["entity", "anyof", [entity]]);
      }
      if (!NG.tools.isEmpty(orderID)) {
        filt.push("and", ["internalid", "anyof", [orderID]]);
      } else {
        filt.push(
          "and",
          ["custbody_show_table", "anyof", [showTableId]],
          "and",
          ["custbody_booth", "anyof", [booth]]
        );
      }
      let cols = [
        search.createColumn({ name: "line", sort: "ASC" }),
        search.createColumn({ name: "item" }),
        search.createColumn({ name: "taxamount" }),
        search.createColumn({ name: "amount" }),
        search.createColumn({ name: "custcol_cost_is_estimated" }),
        search.createColumn({ name: "type", join: "item" }),
      ];
      let etSearch;
      try {
        etSearch = NG.tools.getSearchResults(
          "salesorder",
          filt,
          cols,
          null,
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving order values");
      }
      if (!NG.tools.isEmpty(etSearch)) {
        let itemCatData = {};
        let amount = 0;
        let tax = 0;
        let i;
        if (catExempt) {
          let itemList = [];
          for (i = 0; i < etSearch.length; i++) {
            itemList.push(etSearch[i].getValue("item"));
          }
          itemCatData = this.getItemCatData(itemList);
        }
        let _IN_GROUP = false;
        for (i = 0; i < etSearch.length; i++) {
          let itemId = etSearch[i].getValue({ name: "item" });
          let itemType = etSearch[i].getValue({ name: "type", join: "item" });
          tax = Number(etSearch[i].getValue({ name: "taxamount" }));
          if (isNaN(tax)) {
            tax = Number(0);
          }

          if (!_IN_GROUP && itemType === "Group") {
            _IN_GROUP = true;
          } else if (_IN_GROUP && itemType === "EndGroup") {
            _IN_GROUP = false;
            amount = Number(etSearch[i].getValue({ name: "amount" }));
            billableTotal = NG.M.roundToHundredths(
              billableTotal + tax + amount
            );
          } else if (!_IN_GROUP) {
            if (
              etSearch[i].getValue({ name: "custcol_cost_is_estimated" }) !==
              "T"
            ) {
              if (
                !catExempt ||
                (catExempt &&
                  !NG.tools.isInArray(
                    itemCatData[itemId],
                    _cs_settings.AutoChargeCategoryExclusions
                  ))
              ) {
                amount = Number(etSearch[i].getValue({ name: "amount" }));
                billableTotal = NG.M.roundToHundredths(
                  billableTotal + tax + amount
                );
              }
            }
          }
        }
      } else {
        log.audit({ title: "empty total search" });
      }

      return billableTotal;
    },

    getExemptedTotal: (order, catExempt, eventData, convFeeItem) => {
      log.audit({
        title: "getExemptedTotal()",
        details: `catExempt: ${catExempt}`,
      });
      let billableTotalT = Number(0);
      let billableTotalNT = Number(0);
      let lineCount = order.getLineCount({ sublistId: "item" });
      // let currTax = Number(order.getValue({ fieldId : "taxtotal" }) || "0.00");
      // let currTax2 = _cs_settings.UseCanadianSalesTax ? Number(order.getValue({ fieldId : "taxtotal" }) || "0.00") : 0;
      let topTax;
      let topTaxRate;
      let taxCalc;
      let taxTotal1;
      let taxTotal2;
      let taxRate1;
      let taxRate2;
      let l;

      if (!_cs_settings.UseCanadianSalesTax) {
        topTax = !NG.tools.isEmpty(order)
          ? (String(order.getValue({fieldId: 'taxrate'})) || "").replace("%", "")
          : "0";
        topTaxRate = Number(topTax || 0) / 100;
        if (_cs_settings.SalesTaxOnItemLines) {
          taxCalc = 0;
        }
      } else {
        taxRate1 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData.custrecord_ng_cs_evt_gst_pct || "").replace("%", "")
              : "0"
          ) / 100;
        taxRate2 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData.custrecord_ng_cs_evt_pst_pct || "").replace("%", "")
              : "0"
          ) / 100;
        taxTotal1 = 0;
        taxTotal2 = 0;
      }

      let itemCatData = {};
      if (catExempt) {
        let itemList = [];
        for (l = 0; l < lineCount; l++) {
          itemList.push(
            order.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: l,
            })
          );
        }
        itemCatData = _csFunc.getItemCatData(itemList);
      }

      let _IN_GROUP = false;
      for (l = 0; l < lineCount; l++) {
        let itemId = order.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: l,
        });
        let itemType = order.getSublistValue({
          sublistId: "item",
          fieldId: "itemtype",
          line: l,
        });
        let lineTaxable = order.getSublistValue({
          sublistId: "item",
          fieldId: "istaxable",
          line: l,
        });
        let amount = Number(
          order.getSublistValue({
            sublistId: "item",
            fieldId: "amount",
            line: l,
          })
        );
        if (!_IN_GROUP && itemType === "Group") {
          _IN_GROUP = true;
        } else if (!_IN_GROUP && itemType === "EndGroup") {
          _IN_GROUP = false;
        } else if (_IN_GROUP && itemType !== "EndGroup") {
          if (
            !_cs_settings.SalesTaxOnItemLines &&
            !_cs_settings.UseCanadianSalesTax
          ) {
            if (lineTaxable) {
              if (itemId !== convFeeItem) {
                billableTotalT = NG.M.roundToHundredths(billableTotalT + amount);
              }
            } else {
              if (itemId !== convFeeItem) {
                billableTotalNT = NG.M.roundToHundredths(
                    billableTotalNT + amount
                );
              }
            }
          } else {
            if (itemId !== convFeeItem) {
              billableTotalT = NG.M.roundToHundredths(billableTotalT + amount);
              taxRate1 =
                  Number(
                      order
                          .getSublistValue({
                            sublistId: "item",
                            fieldId: "taxrate1",
                            line: l,
                          })
                          .replace("%", "")
                  ) / 100;
              let taxAmount1 = NG.M.roundToHundredths(
                  NG.M.roundToThousandths(taxRate1 * amount)
              );
              taxTotal1 += taxAmount1;
              if (_cs_settings.UseCanadianSalesTax) {
                taxRate2 =
                    Number(
                        order
                            .getSublistValue({
                              sublistId: "item",
                              fieldId: "taxrate2",
                              line: l,
                            })
                            .replace("%", "")
                    ) / 100;
                let taxAmount2 = NG.M.roundToHundredths(
                    NG.M.roundToThousandths(taxRate2 * amount)
                );
                taxTotal2 += taxAmount2;
              }
            }
          }
        } else if (!_IN_GROUP) {
          if (
            !order.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_cost_is_estimated",
              line: l,
            })
          ) {
            if (
              !catExempt ||
              (catExempt &&
                !NG.tools.isInArray(
                  itemCatData[itemId],
                  _cs_settings.AutoChargeCategoryExclusions
                ))
            ) {
              if (
                !_cs_settings.SalesTaxOnItemLines &&
                !_cs_settings.UseCanadianSalesTax
              ) {
                if (lineTaxable) {
                  if (itemId !== convFeeItem) {
                    billableTotalT = NG.M.roundToHundredths(
                        billableTotalT + amount
                    );
                  }
                } else {
                  if (itemId !== convFeeItem) {
                    billableTotalNT = NG.M.roundToHundredths(
                        billableTotalNT + amount
                    );
                  }
                }
              } else {
                if (itemId !== convFeeItem) {
                  billableTotalT = NG.M.roundToHundredths(
                      billableTotalT + amount
                  );
                }
                if (_cs_settings.UseCanadianSalesTax) {
                  if (itemId !== convFeeItem) {
                    let taxAmnt1 = NG.M.roundToHundredths(
                        NG.M.roundToThousandths(taxRate1 * amount)
                    );
                    taxTotal1 += taxAmnt1;
                    let taxAmnt2 = NG.M.roundToHundredths(
                        NG.M.roundToThousandths(taxRate2 * amount)
                    );
                    taxTotal2 += taxAmnt2;
                  }
                } else {
                  if (itemId !== convFeeItem) {
                    let taxAmount = NG.M.roundToHundredths(
                        NG.M.roundToThousandths(topTaxRate * amount)
                    );
                    taxCalc += taxAmount;
                  }
                }
              }
            }
          }
        }
      }

      if (!NG.tools.isEmpty(taxCalc)) {
        taxCalc = NG.M.roundToHundredths(taxCalc);
      }
      if (!NG.tools.isEmpty(taxTotal1)) {
        taxTotal1 = NG.M.roundToHundredths(taxTotal1);
      }
      if (!NG.tools.isEmpty(taxTotal2)) {
        taxTotal2 = NG.M.roundToHundredths(taxTotal2);
      }

      let taxTotal =
        !_cs_settings.SalesTaxOnItemLines && !_cs_settings.UseCanadianSalesTax
          ? NG.M.roundToHundredths(
              NG.M.roundToThousandths(topTaxRate * billableTotalT)
            )
          : !_cs_settings.UseCanadianSalesTax
          ? taxCalc
          : NG.M.roundToHundredths(taxTotal1 + taxTotal2);
      let billableTotal = NG.M.roundToHundredths(
        billableTotalT + billableTotalNT + taxTotal
      );

      return {
        sub: NG.M.roundToHundredths(billableTotalT + billableTotalNT),
        tax: taxTotal,
        total: billableTotal,
        taxrate: topTaxRate,
        t: billableTotalT,
        nt: billableTotalNT,
        tax1: taxTotal1,
        tax2: taxTotal2,
      };
    },

    /**
     * Searches for payment/refund transactions based on the current event/booth/order combination and calculates the total paid
     * @param {String|Number} eventId
     * @param {String|Number} boothId
     * @param {String|Number} entityId
     * @param {String|Number} orderId
     * @returns {Number}
     */
    getPaymentTotalsOld: (eventId, boothId, entityId, orderId) => {
      let paidTotal = Number(0);

      let filt = [];
      if (!NG.tools.isEmpty(orderId)) {
        filt.push(["custbody_booth_order", "anyof", [orderId]]);
      } else if (!NG.tools.isEmpty(eventId) && !NG.tools.isEmpty(boothId)) {
        filt.push(["custbody_show_table", "anyof", [eventId]], "and", [
          "custbody_booth",
          "is",
          boothId,
        ]);
      }
      if (
        _cs_settings.AllowMultiBillingParties &&
        !NG.tools.isEmpty(entityId)
      ) {
        if (filt.length > 0) {
          filt.push("and");
        }
        filt.push(["entityId", "anyof", [entityId]]);
      }
      let ptSearch;
      try {
        ptSearch = NG.tools.getSearchResults(
          "transaction",
          filt,
          null,
          _cs_settings.PaymentSearch,
          1000,
          false,
          false
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving order values");
      }
      if (!NG.tools.isEmpty(ptSearch)) {
        for (let i = 0; i < ptSearch.length; i++) {
          paidTotal = NG.M.roundToHundredths(
            paidTotal + Number(ptSearch[i].getValue({ name: "amount" }))
          );
        }
      }

      return paidTotal;
    },

    getPaymentTotals: (eventId, boothId, entityId, orderId) => {
      let paidTotal = Number(0);

      let filt = [];
      if (!NG.tools.isEmpty(orderId)) {
        filt.push(["custbody_booth_order", "anyof", [orderId]]);
      } else if (!NG.tools.isEmpty(eventId) && !NG.tools.isEmpty(boothId)) {
        filt.push(["custbody_show_table", "anyof", [eventId]], "and", [
          "custbody_booth",
          "anyof",
          [boothId],
        ]);
      }
      if (
        _cs_settings.AllowMultiBillingParties &&
        !NG.tools.isEmpty(entityId)
      ) {
        if (filt.length > 0) {
          filt.push("and");
        }
        filt.push(["entity", "anyof", [entityId]]);
      }
      let pmtSearch;
      try {
        // PaymentSearch
        pmtSearch = NG.tools.getSearchResults(
          "transaction",
          filt,
          [],
          _cs_settings.PaymentSearch
        );
        // search = NG.tools.getSearchResultsAdv({
        // 		type : "transaction"
        // 	,	filterExp : filt
        // 	,	searchId : _cs_settings.PaymentSearch
        // });
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving order values");
      }
      if (!NG.tools.isEmpty(pmtSearch)) {
        for (let i = 0; i < pmtSearch.length; i++) {
          paidTotal = NG.M.roundToHundredths(
            paidTotal + Number(pmtSearch[i].getValue({ name: "amount" }))
          );
        }
      }

      return paidTotal;
    },

    /**
     *
     * @param {[String|Number]} itemList
     * @returns { Object: catData }
     */
    getItemCatData: (itemList) => {
      let itemData = {};
      let filt = [["internalid", "anyof", itemList]];
      let cols = [
        search.createColumn({ name: "custitem_item_category" }),
        search.createColumn({ name: "internalid" }),
      ];
      let results;
      try {
        results = NG.tools.getSearchResults("item", filt, cols);
      } catch (err) {
        NG.log.logError(err, "Error encountered getting item category data");
      }
      if (!NG.tools.isEmpty(results)) {
        results.forEach((res) => {
          itemData[res.id] = res.getValue({ name: "custitem_item_category" });
        });
      }

      return itemData;
    },

    calcWithoutExclCatsOld: (orderId, balance) => {
      let order = record.load({ type: "salesorder", id: orderId });
      let taxRate =
        Number(order.getValue({ fieldId: "taxrate" }).replace("%", "")) / 100;
      let totalPaid = Number(
        order.getValue({ fieldId: "custbody_total_paid" })
      );
      let taxAmount = Number(0);
      let txTotal = Number(0);
      let ntxTotal = Number(0);
      let items = [];
      let l;
      let lines = order.getLineCount({ sublistId: "item" });
      for (l = 1; l <= lines; l++) {
        items.push(
          order.getSublistValue({ sublistId: "item", fieldId: "item", line: l })
        );
      }
      let itemCats = this.getItemCatData(items);
      for (l = 1; l <= lines; l++) {
        let taxable = order.getSublistValue({
          sublistId: "item",
          fieldId: "istaxable",
          line: l,
        });
        let category =
          itemCats[
            order.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: l,
            })
          ];
        let amount = Number(
          order.getSublistValue({
            sublistId: "item",
            fieldId: "amount",
            line: l,
          })
        );
        if (
          NG.tools.isEmpty(category) ||
          !NG.tools.isInArray(
            category,
            _cs_settings.AutoChargeCategoryExclusions
          )
        ) {
          if (taxable === "T") {
            txTotal += amount;
          } else {
            ntxTotal += amount;
          }
        }
      }

      if (txTotal > 0) {
        taxAmount += NG.M.roundToHundredths(
          NG.M.roundToThousandths(taxRate * NG.M.roundToHundredths(txTotal))
        );
      }

      let FinalValue = NG.M.roundToHundredths(
        NG.M.roundToHundredths(txTotal) +
          NG.M.roundToHundredths(ntxTotal) +
          taxAmount -
          totalPaid
      );

      return FinalValue > 0 ? FinalValue : 0;
    },

    calcWithoutExclCats: (order, eventData, convFeeItem, exemptEstimatedType) => {
      log.audit({ title: "calcWithoutExclCats()", details: "" });
      let balance = Number(order.getValue({ fieldId: "total" }));
      let tTotal = 0;
      let tTotal1 = 0;
      let tTotal2 = 0;
      let ntTotal = 0;
      let items = [];
      let topTax;
      let taxRate;
      let taxRate1;
      let taxRate2;
      let l;

      if (!_cs_settings.UseCanadianSalesTax) {
        topTax = !NG.tools.isEmpty(order)
          ? (String(order.getValue({fieldId: 'taxrate'})) || "").replace("%", "")
          : "";
        taxRate = Number(topTax || 0) / 100;
        if (_cs_settings.SalesTaxOnItemLines) {
          taxRate1 = Number(topTax || 0) / 100;
          taxRate2 = 0;
        }
      } else {
        taxRate1 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData.custrecord_ng_cs_evt_gst_pct || "").replace("%", "")
              : "0"
          ) / 100;
        taxRate2 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData.custrecord_ng_cs_evt_pst_pct || "").replace("%", "")
              : "0"
          ) / 100;
        tTotal1 = 0;
        tTotal2 = 0;
      }

      let lines = order.getLineCount({ sublistId: "item" });
      for (l = 0; l < lines; l++) {
        let estimatedTypeItem = order.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_cost_is_estimated",
          line: l,
        })
        if (exemptEstimatedType && estimatedTypeItem) {
          continue;
        }
        items.push(
          order.getSublistValue({ sublistId: "item", fieldId: "item", line: l })
        );
      }
      let itemCats = _csFunc.getItemCatData(items);
      for (l = 0; l < lines; l++) {
        let estimatedTypeItem = order.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_cost_is_estimated",
          line: l,
        })
        if (exemptEstimatedType && estimatedTypeItem) {
          continue;
        }
        let taxable =
          order.getSublistValue({
            sublistId: "item",
            fieldId: "istaxable",
            line: l,
          });
        let category =
          itemCats[
            order.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: l,
            })
          ];
        let amount = Number(
          order.getSublistValue({
            sublistId: "item",
            fieldId: "amount",
            line: l,
          })
        );
        let itemId = order.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: l,
        })
        if (
          NG.tools.isEmpty(category) ||
          !NG.tools.isInArray(
            category,
            _cs_settings.AutoChargeCategoryExclusions
          )
        ) {
          if (
            !_cs_settings.UseCanadianSalesTax
          ) {
            if (taxable) {
              if (itemId !== convFeeItem) {
                tTotal += amount;
              }
            } else {
              if (itemId !== convFeeItem) {
                ntTotal += amount;
              }
            }
          } else {
            if (itemId !== convFeeItem) {
              tTotal += amount;
              let taxAmount1 = NG.M.roundToHundredths(
                  NG.M.roundToThousandths(taxRate1 * amount)
              );
              tTotal1 += taxAmount1;
              if (_cs_settings.UseCanadianSalesTax) {
                let taxAmount2 = NG.M.roundToHundredths(
                    NG.M.roundToThousandths(taxRate2 * amount)
                );
                tTotal2 += taxAmount2;
              }
            }
          }
        }
      }

      tTotal = NG.M.roundToHundredths(tTotal);
      ntTotal = NG.M.roundToHundredths(ntTotal);
      tTotal1 = NG.M.roundToHundredths(tTotal1);
      tTotal2 = NG.M.roundToHundredths(tTotal2);

      let taxTotal =
        !_cs_settings.UseCanadianSalesTax
          ? NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate * tTotal))
          : NG.M.roundToHundredths(tTotal1 + tTotal2);
      let billableTotal = NG.M.roundToHundredths(tTotal + ntTotal + taxTotal);

      return {
        sub: NG.M.roundToHundredths(tTotal + ntTotal),
        tax: taxTotal,
        total: billableTotal,
        taxrate: taxRate,
        t: tTotal,
        nt: ntTotal,
        tax1: tTotal1,
        tax2: tTotal2,
      };
    },

    defaultTotal(rec, eventData) {

      log.audit('defaultTotal cs.21')
      let tTotal = 0;
      let tTotal1 = 0;
      let tTotal2 = 0;
      let ntTotal = 0;
      let topTax;
      let taxRate;
      let taxRate1;
      let taxRate2;
      let l;
      let lines = rec.getLineCount({ sublistId: "item" });

      if (!_cs_settings.UseCanadianSalesTax) {
        topTax = !NG.tools.isEmpty(eventData)
          ? (eventData.custrecord_tax_percent || "").replace("%", "")
          : "";
        taxRate = Number(topTax || 0) / 100;
        if (_cs_settings.SalesTaxOnItemLines) {
          taxRate1 = Number(topTax || 0) / 100;
          taxRate2 = 0;
        }
      } else {
        taxRate1 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData.custrecord_ng_cs_evt_gst_pct || "").replace("%", "")
              : "0"
          ) / 100;
        taxRate2 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData.custrecord_ng_cs_evt_pst_pct || "").replace("%", "")
              : "0"
          ) / 100;
        tTotal1 = 0;
        tTotal2 = 0;
      }

      for (l = 0; l < lines; l++) {
        let taxable =
          rec.getSublistValue({
            sublistId: "item",
            fieldId: "istaxable",
            line: l,
          }) || true;
        let amount = Number(
          rec.getSublistValue({ sublistId: "item", fieldId: "amount", line: l })
        );
        if (
          !_cs_settings.SalesTaxOnItemLines &&
          !_cs_settings.UseCanadianSalesTax
        ) {
          if (taxable) {
            tTotal += amount;
          } else {
            ntTotal += amount;
          }
        } else {
          tTotal += amount;
          let taxAmount1 = NG.M.roundToHundredths(
            NG.M.roundToThousandths(taxRate1 * amount)
          );
          tTotal1 += taxAmount1;
          if (_cs_settings.UseCanadianSalesTax) {
            let taxAmount2 = NG.M.roundToHundredths(
              NG.M.roundToThousandths(taxRate2 * amount)
            );
            tTotal2 += taxAmount2;
          }
        }
      }

      tTotal = NG.M.roundToHundredths(tTotal);
      ntTotal = NG.M.roundToHundredths(ntTotal);
      tTotal1 = NG.M.roundToHundredths(tTotal1);
      tTotal2 = NG.M.roundToHundredths(tTotal2);

      if (!_cs_settings.UseCanadianSalesTax) {
        let taxTotal = !_cs_settings.SalesTaxOnItemLines
          ? NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate * tTotal))
          : NG.M.roundToHundredths(tTotal1 + tTotal2);
        let billableTotal = NG.M.roundToHundredths(tTotal + ntTotal + taxTotal);

        return {
          total: billableTotal,
          tax: taxTotal,
          sub: NG.M.roundToHundredths(
            Number(rec.getValue({ fieldId: "total" })) -
              Number(rec.getValue({ fieldId: "taxtotal" }))
          ),
          taxrate: taxRate,
          t: tTotal,
          nt: ntTotal,
          tax1: tTotal1,
          tax2: tTotal2,
        };
      }
      let taxTotal = NG.M.roundToHundredths(
        Number(rec.getValue({ fieldId: "taxtotal" })) +
          Number(rec.getValue({ fieldId: "tax2total" }))
      );
      return {
        total: Number(rec.getValue({ fieldId: "total" })),
        tax: taxTotal,
        sub: NG.M.roundToHundredths(
          Number(rec.getValue({ fieldId: "total" })) - taxTotal
        ),
        taxrate: taxRate,
        t: tTotal,
        nt: ntTotal,
        tax1: tTotal1,
        tax2: tTotal2,
      };
    },

    updatePayTotal(data) {
      log.audit({ title: "func.updatePayTotal()", details: "" });
      let totalData = data.totalData;
      let finalTotal = {};

      if (_cs_settings.PreventAdditionalOrders) {
        let sFilt = [
          ["custbody_show_table", "anyof", [data.show]],
          "and",
          ["custbody_booth", "anyof", [data.booth]],
          "and",
          [
            "custbody_ng_cs_order_type",
            "anyof",
            [_cs_settings.DefaultExhibitorOrderType],
          ],
          "and",
          ["mainline", "is", "F"],
          "and",
          ["cogs", "is", "F"],
          "and",
          ["shipping", "is", "F"],
          "and",
          ["taxline", "is", "F"],
        ];
        if (_cs_settings.AllowMultiBillingParties) {
          sFilt.push("and", ["entity", "anyof", [data.billParty]]);
        }
        let sCols = [
          search.createColumn({ name: "taxtotal" }),
          search.createColumn({ name: "total" }),
          search.createColumn({ name: "amount" }),
          search.createColumn({ name: "taxamount" }),
        ];
        let sSearch;
        try {
          sSearch = NG.tools.getSearchResults("salesorder", sFilt, sCols);
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered searching for existing sales orders"
          );
        }
        if (!NG.tools.isEmpty(sSearch)) {
          let eoTotal = Number(sSearch[0].getValue({ name: "total" }));
          let eoTax = Number(sSearch[0].getValue({ name: "taxtotal" }));
          let eoTotalT = Number(0);
          let eoTotalNT = Number(0);
          sSearch.forEach(function (res) {
            let lineAmount = Number(res.getValue({ name: "amount" }));
            let lineTax = Number(res.getValue({ name: "taxamount" }));
            if (lineTax > 0) {
              eoTotalT = NG.M.roundToHundredths(eoTotalT + lineAmount);
            } else {
              eoTotalNT = NG.M.roundToHundredths(eoTotalNT + lineAmount);
            }
          });
          let eoSubTotal = NG.M.roundToHundredths(eoTotalT + eoTotalNT);

          let fullSubTotal = NG.M.roundToHundredths(eoSubTotal + totalData.sub);
          let taxableSubTotal = NG.M.roundToHundredths(eoTotalT + totalData.t);
          let summedTax = NG.M.roundToHundredths(eoTax + totalData.tax);
          let fullTax = NG.M.roundToHundredths(
            NG.M.roundToThousandths(totalData.taxrate * taxableSubTotal)
          );
          if (fullTax > summedTax) {
            log.audit({ title: "updating tax amounts", details: "" });
            let updTax = NG.M.roundToHundredths(fullTax - eoTax);
            let updTotal = NG.M.roundToHundredths(totalData.sub + updTax);
            finalTotal = {
              sub: totalData.sub,
              tax: updTax,
              taxInit: NG.M.roundToHundredths(fullTax - eoTax),
              taxRate: totalData.taxRate,
              total: updTotal,
              totalInit: NG.M.roundToHundredths(updTotal),
              t: totalData.t,
              nt: totalData.nt,
              prevTax: eoTax,
              nonRoundedTax: totalData.taxrate * totalData.t,
              taxToThous: NG.M.roundToThousandths(
                totalData.taxrate * totalData.t
              ),
              taxToThousUpd: NG.M.roundToThousandths(
                totalData.taxrate * totalData.t
              ),
            };
          } else {
            log.audit({
              title: "reporting calculated tax amounts",
              details: "",
            });
            finalTotal = JSON.parse(JSON.stringify(totalData));
            finalTotal.nonRoundedTax = fullTax - eoTax;
            finalTotal.taxToThous = NG.M.roundToThousandths(fullTax - eoTax);
            finalTotal.taxToHund = NG.M.roundToHundredths(
              NG.M.roundToThousandths(fullTax - eoTax)
            );
            finalTotal.total = NG.M.roundToHundredths(
              totalData.sub + (fullTax - eoTax)
            );
            finalTotal.totalInit = totalData.total;
            finalTotal.fullTax = fullTax;
            finalTotal.eoTax = eoTax;
            finalTotal.taxInit = totalData.tax;
            finalTotal.tax = NG.M.roundToHundredths(
              NG.M.roundToThousandths(fullTax - eoTax)
            );
            finalTotal.summedTax = NG.M.roundToHundredths(fullTax - eoTax);
          }
        } else {
          log.audit({ title: "not updating tax amounts", details: "" });
          finalTotal = totalData;
        }
      }

      return finalTotal;
    },

    getPaidTotal(options) {
      let paidAmount = Number(0);
      let appliedFilt = [["custbody_booth_order", "anyof", [options.orderId]]];
      if (
        _cs_settings.AllowMultiBillingParties &&
        !NG.tools.isEmpty(options.entityId)
      ) {
        appliedFilt.push("and", ["entity", "anyof", [options.entityId]]);
      }
      let appliedResults;
      try {
        appliedResults = NG.tools.getSearchResults(
          "transaction",
          appliedFilt,
          [],
          _cs_settings.PaymentSearch
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered searching for payment data");
      }

      if (!NG.tools.isEmpty(appliedResults)) {
        appliedResults.forEach(function (res) {
          let rt = res.recordType;
          let initAmount = Math.abs(Number(res.getValue({ name: "amount" })));
          let amt = NG.tools.isInArray(rt, _cs_settings._NEG)
            ? initAmount * -1
            : Number(initAmount);
          paidAmount = NG.M.roundToHundredths(paidAmount + amt);
        });
      }

      return paidAmount;
    },

    hideCSJobField: (form) => {
      require(["N/ui/serverWidget"], (widget) => {
        let jobFieldA;
        let jobFieldB;
        let jobFieldC;
        let jobFieldD;
        if (!_cs_settings.UseCustomJob) {
          jobFieldA = form.getField({ id: "custbody_cseg_ng_cs_job" });
          if (!NG.tools.isEmpty(jobFieldA)) {
            jobFieldA.updateDisplayType({
              displayType: widget.FieldDisplayType.HIDDEN,
            });
            jobFieldA.isMandatory = false;
          }
          jobFieldB = form.getField({ id: "custentity_cseg_ng_cs_job" });
          if (!NG.tools.isEmpty(jobFieldB)) {
            jobFieldB.updateDisplayType({
              displayType: widget.FieldDisplayType.HIDDEN,
            });
            jobFieldB.isMandatory = false;
          }
          jobFieldC = form.getField({ id: "custrecord_show_job" });
          if (!NG.tools.isEmpty(jobFieldC)) {
            jobFieldC.updateDisplayType({
              displayType: widget.FieldDisplayType.HIDDEN,
            });
            jobFieldC.isMandatory = false;
          }
          jobFieldD = form.getField({ id: "class" });
          if (!NG.tools.isEmpty(jobFieldD)) {
            jobFieldD.label = "Job";
          }
        } else {
          jobFieldA = form.getField({ id: "custbody_cseg_ng_cs_job" });
          if (!NG.tools.isEmpty(jobFieldA)) {
            jobFieldA.updateDisplayType({
              displayType: widget.FieldDisplayType.NORMAL,
            });
          }
          jobFieldB = form.getField({ id: "custentity_cseg_ng_cs_job" });
          if (!NG.tools.isEmpty(jobFieldB)) {
            jobFieldB.updateDisplayType({
              displayType: widget.FieldDisplayType.NORMAL,
            });
          }
          jobFieldC = form.getField({ id: "custrecord_show_job" });
          if (!NG.tools.isEmpty(jobFieldC)) {
            jobFieldC.updateDisplayType({
              displayType: widget.FieldDisplayType.NORMAL,
            });
          }
          jobFieldD = form.getField({ id: "class" });
          if (!NG.tools.isEmpty(jobFieldD)) {
            jobFieldD.label = "Class";
          }
        }
      });
    },

    selectSalesTaxCode: (options) => {
      let taxCode;
      let filt1 = [["isinactive", "is", "F"]];
      if (!_cs_settings.UseCanadianSalesTax) {
        filt1.push("and", ["city", "contains", options.city], "and", [
          "zip",
          "contains",
          options.zip,
        ]);
      } else {
        filt1.push("and", ["state", "is", options.state]);
      }
      if (!NG.tools.isEmpty(_cs_settings.SalesTaxFilter)) {
        filt1.push("and", ["itemid", "contains", _cs_settings.SalesTaxFilter]);
      }
      let cols = [
        search.createColumn({ name: "itemid" }),
        search.createColumn({ name: "rate" }),
        search.createColumn({ name: "zip" }),
      ];
      if (_cs_settings.UseCanadianSalesTax) {
        cols.push(
          search.createColumn({ name: "unitprice1" }),
          search.createColumn({ name: "unitprice2" })
        );
      }
      let tSearch;
      try {
        tSearch = NG.tools.getSearchResults("taxgroup", filt1, cols);
      } catch (err) {
        console.log(
          "Error encountered searching for applicable tax groups (A)\n",
          err
        );
      }

      if (NG.tools.isEmpty(tSearch) && !_cs_settings.UseCanadianSalesTax) {
        let cityPieces = options.city.split(" ");
        if (cityPieces.length > 1) {
          let filt2 = [
            ["isinactive", "is", "F"],
            "and",
            [
              "city",
              "contains",
              "{0} {1}".NG_Format(cityPieces[0], cityPieces[1].substr(0, 1)),
            ],
            "and",
            ["zip", "contains", options.zip],
          ];
          try {
            tSearch = NG.tools.getSearchResults("taxgroup", filt2, cols);
          } catch (err) {
            console.log(
              "Error encountered searching for applicable tax groups (B)\n",
              err
            );
          }
        }
      }

      if (!NG.tools.isEmpty(tSearch)) {
        if (tSearch.length === 1) {
          taxCode = tSearch[0].id;
        } else if (tSearch.length > 1 && !_cs_settings.UseCanadianSalesTax) {
          let maxRate = 0;
          let maxTax;
          tSearch.forEach((res) => {
            let resRate = parseFloat(
              res.getValue({ name: "rate" }).replace("%", "")
            );
            if (resRate > maxRate) {
              maxRate = resRate;
              maxTax = res.id;
            }
          });
          if (!NG.tools.isEmpty(maxTax)) {
            taxCode = maxTax;
          }
        } else if (!_cs_settings.UseCanadianSalesTax) {
          taxCode = "-8";
        } else {
          taxCode = "";
        }
      } else if (!_cs_settings.UseCanadianSalesTax) {
        taxCode = "-8";
      } else {
        taxCode = "";
      }

      return taxCode;
    },

    getStateAbbrev: (state) => {
      let stateList = NG.lib.states.US;
      let stateAbbrev = "";
      for (let s = 0; s < stateList.length; s++) {
        if (stateList[s].text === state) {
          stateAbbrev = stateList[s].value;
        }
      }

      return stateAbbrev;
    },

    buildTaxGroupField: (context, currRec, taxField) => {
      let taxGroupField = context.form.addField({
        id: "custpage_tax_group",
        type: "select",
        label: "Tax Group",
      });
      taxGroupField.helpText = "Select the tax code for the show.";
      taxGroupField.updateBreakType({ breakType: "startcol" });
      let tgFilt = [["isinactive", "is", "F"]];
      let tgCols = [search.createColumn({ name: "itemid", sort: "ASC" })];
      let results = NG.tools.getSearchResults("taxgroup", tgFilt, tgCols);
      if (!NG.tools.isEmpty(results)) {
        taxGroupField.addSelectOption({
          value: "",
          text: "",
          isSelected: true,
        });
        results.forEach((res) => {
          taxGroupField.addSelectOption({
            value: res.id,
            text: res.getValue({ name: "itemid" }),
          });
        });
      }

      context.form.insertField({ field: taxGroupField, nextfield: taxField });
      let taxRateField = context.form.getField({ id: taxField });
      context.form.insertField({
        field: taxRateField,
        nextfield: "custpage_tax_group",
      });
      taxRateField.updateDisplayType({ displayType: "hidden" });

      if (context.type === "edit") {
        taxGroupField.defaultValue = currRec.getValue({ fieldId: taxField });
      } else if (context.type === "copy") {
        context.form.getField({ id: "custrecord_tax_rate" }).defaultValue = "";
      }
    },

    addSuccessMessage: (message, delay) => {
      let html = '<script type="text/javascript">\n';
      html += "setTimeout(function() {\n";
      html += "	require(['N/ui/message'], function(message) {\n";
      html += "		let sMsg = message.create({\n";
      html += '				title : "Success"\n';
      html += '			,	message : "{0}"\n'.NG_Format(message.replace(/"/g, '\\"'));
      html += "			,	type : message.Type.CONFIRMATION\n";
      html += "		});\n";
      html += "		sMsg.show();\n";
      html += "	});\n";
      html += "}, {0});\n".NG_Format(delay.toFixed(0));
      html += "</script>";

      return html;
    },

    addAltSuccessMessage: (message, delay) => {
      let html = '<script type="text/javascript">\n';
      html += "setTimeout(function() {\n";
      html += "	require(['N/ui/message'], function(message) {\n";
      html += "		let sMsg = message.create({\n";
      html += '				title : "Success With Errors"\n';
      html += '			,	message : "{0}"\n'.NG_Format(message.replace(/"/g, '\\"'));
      html += "			,	type : message.Type.WARNING\n";
      html += "		});\n";
      html += "		sMsg.show();\n";
      html += "	});\n";
      html += "}, {0});\n".NG_Format(delay.toFixed(0));
      html += "</script>";

      return html;
    },

    addFailureMessage: (message, delay) => {
      let html = '<script type="text/javascript">\n';
      html += "setTimeout(function() {\n";
      html += "	require(['N/ui/message'], function(message) {\n";
      html += "		let fMsg = message.create({\n";
      html += '				title : "Failure"\n';
      html += '			,	message : "{0}"\n'.NG_Format(message.replace(/"/g, '\\"'));
      html += "			,	type : message.Type.ERROR\n";
      html += "		});\n";
      html += "		fMsg.show();\n";
      html += "	});\n";
      html += "}, {0});\n".NG_Format(delay.toFixed(0));
      html += "</script>";

      return html;
    },

    /**
     * @param {Object} options
     * @param {string} options.title
     * @param {string} options.message
     * @param {number} options.type
     * @param {number} options.delay
     * @returns {string}
     */
    addStatusAlert: (options) => {
      let html = '<script type="text/javascript">\n';
      html += "setTimeout(function() {\n";
      html += "	require(['N/ui/message'], function(message) {\n";
      html += "		let fMsg = message.create({\n";
      html += '				title : "{0}"\n'.NG_Format(options.title.replace(/"/g, '\\"'));
      html += '			,	message : "{0}"\n'.NG_Format(
        options.message.replace(/"/g, '\\"')
      );
      html += "			,	type : {0}\n".NG_Format(options.type);
      html += "		});\n";
      html += "		fMsg.show();\n";
      html += "	});\n";
      html += "}, {0});\n".NG_Format((options.delay || 0).toFixed(0));
      html += "</script>";

      return html;
    },
  };

  let _OpenShowIdCol = 0;
  let _OpenShowNameCol = 2;
  let _CS_TimeZones = [null, "EST", "CST", "MST", "PST"];

  // /////////// JSDoc Object Definitions

  /**
   * @typedef {Object} catData
   * @property {String} custitem_item_category_2
   * @property {String} internalid
   */

  /**
   * @typedef {Object} ScriptData
   * @property {String} scriptid
   * @property {String} deployid
   */

  /**
   * @typedef {Object} RecordTypes
   * @property {String} booth
   * @property {String} showtable
   * @property {String} boothorder
   * @property {String} payment
   * @property {String} refund
   * @property {String} deposit
   * @property {String} exhibitor
   * @property {String} invoice
   * @property {String} credit
   * @property {String} displayform
   * @property {String} showdisplayform
   * @property {String} showdate
   */

  /**
   * @typedef {Object} TransactionFields
   * @property {String} booth
   * @property {String} event
   */

  /**
   * @typedef {Object} ItemFields
   * @property {String} sqft
   * @property {String} days
   * @property {String} showdur
   * @property {String} freight
   * @property {String} color
   * @property {String} size
   * @property {String} orientation
   * @property {String} labor
   * @property {String} graphics
   * @property {String} colorSel
   * @property {String} sizeSel
   * @property {String} orientSel
   * @property {String} graphicSel
   */

  /**
   * @typedef {Object} ItemOptions
   * @property {String} supervision
   * @property {String} labordate
   * @property {String} laborendtime
   * @property {String} laboresthours
   * @property {String} laborworkers
   * @property {String} labortime
   */

  /**
   * @typedef {Object} EntityFields
   * @property {String} lastshow
   * @property {String} lastwebshow
   * @property {String} lastwebbooth
   */

  /**
   * @typedef {Object} EventFields
   * @property {String} complete
   */

  /**
   * @typedef {Object} CS_Fields
   * @property {TransactionFields} tran
   * @property {ItemFields} item
   * @property {ItemOptions} itemOpts
   * @property {EntityFields} entity
   * @property {EventFields} event
   */

  /**
   * @typedef {Object} MatrixFieldInfo
   * @property {String} sourcefrom
   * @property {String} internalid
   * @property {Boolean} ismandatory
   * @property {Boolean} ismatrixdimension
   * @property {String} label
   * @property {String} type
   */

  /**
   * @typedef {Object} CS_Settings
   * @property {String} openShowsSearch
   * @property {String} PaymentSearch
   * @property {String} AlgoliaWriteURL
   * @property {String} AlgoliaReadURL
   * @property {String} LogBaseUrl
   * @property {[Number|String]} sqftItems
   * @property {[Number|String]} daysItems
   * @property {[Number|String]} durationItems
   * @property {[Number|String]} sqdItems
   * @property {[Number|String]} freightItems
   * @property {[Number|String]} colorItems
   * @property {[Number|String]} sizeItems
   * @property {[Number|String]} orientationItems
   * @property {[Number|String]} laborItems
   * @property {[Number|String]} barrelItems
   * @property {[Number|String]} freightOptsA
   * @property {[Number|String]} freightOptsB
   * @property {[Number|String]} laborOptItemsA
   * @property {[Number|String]} laborOptItemsB
   * @property {[Number|String]} graphicsItems
   * @property {RecordTypes} rType
   * @property {Object: ScriptData} scripts
   * @property {CS_Fields} fields
   * @property {Object: MatrixFieldInfo} matrixOptionMap
   * @property {String} internalid
   * @property {String} internalid
   * @property {String} internalid
   * @property {String} internalid
   * @property {String} internalid
   * @property {String} internalid
   */

  /**
   * @typedef {Object} CS_Settings_Fields
   * @property {string} custrecord_ng_cs_accent_color
   * @property {string} custrecord_ng_cs_acct_domain_url
   * @property {string} custrecord_ng_cs_activity_log_rec_id
   * @property {string} custrecord_ng_cs_activity_log_srch_id
   * @property {string} custrecord_ng_cs_add_item_forms
   * @property {string} custrecord_ng_cs_algolia_api_key
   * @property {string} custrecord_ng_cs_algolia_application_id
   * @property {string} custrecord_ng_cs_algolia_index
   * @property {string} custrecord_ng_cs_algolia_search_key
   * @property {boolean} custrecord_ng_cs_allow_mass_booth_delete
   * @property {boolean} custrecord_ng_cs_allow_mult_billng_part
   * @property {boolean} custrecord_ng_cs_allow_show_autopay
   * @property {string} custrecord_ng_cs_amex
   * @property {boolean} custrecord_ng_cs_auth_non_web_orders
   * @property {boolean} custrecord_ng_cs_auto_charge_web_orders
   * @property {string} custrecord_ng_cs_autochrg_cat_excl
   * @property {string} custrecord_ng_cs_billed_ord_edit_users
   * @property {string} custrecord_ng_cs_booth_num_line_text
   * @property {string} custrecord_ng_cs_booth_ord_forms
   * @property {string} custrecord_ng_cs_canc_threshold
   * @property {string} custrecord_ng_cs_cancl_charge_item
   * @property {string} custrecord_ng_cs_canonical_base_url
   * @property {string} custrecord_ng_cs_cc_auth_item
   * @property {string} custrecord_ng_cs_cc_conv_fee_item
   * @property {string} custrecord_ng_cs_cc_conv_fee_order_types
   * @property {string} custrecord_ng_cs_cc_conv_fee_rate
   * @property {boolean} custrecord_ng_cs_clear_order_cc_details
   * @property {string} custrecord_ng_cs_company_header_logo
   * @property {string} custrecord_ng_cs_contact_us_url
   * @property {string} custrecord_ng_cs_conv_fee_web_displayed
   * @property {string} custrecord_ng_cs_conv_fee_zero_tax
   * @property {string} custrecord_ng_cs_csv_import_file
   * @property {string} custrecord_ng_cs_csv_import_folder_id
   * @property {string} custrecord_ng_cs_cust_web_access_role
   * @property {boolean} custrecord_ng_cs_custom_job_numbering
   * @property {string} custrecord_ng_cs_def_canc_chrg_pct
   * @property {string} custrecord_ng_cs_def_dep_account
   * @property {string} custrecord_ng_cs_def_exhb_dept
   * @property {string} custrecord_ng_cs_def_exhb_ord_type
   * @property {string} custrecord_ng_cs_def_show_mgmt_dept
   * @property {string} custrecord_ng_cs_def_show_mgmt_ord_type
   * @property {string} custrecord_ng_cs_default_adv_show_price
   * @property {string} custrecord_ng_cs_default_exhib_move_in
   * @property {string} custrecord_ng_cs_default_exhib_move_out
   * @property {string} custrecord_ng_cs_default_onst_show_price
   * @property {string} custrecord_ng_cs_default_show_date
   * @property {string} custrecord_ng_cs_default_show_mgmt_price
   * @property {string} custrecord_ng_cs_default_show_move_in
   * @property {string} custrecord_ng_cs_default_show_move_out
   * @property {string} custrecord_ng_cs_default_show_subsidiary
   * @property {string} custrecord_ng_cs_default_sprvisor_markup
   * @property {string} custrecord_ng_cs_default_std_show_price
   * @property {boolean} custrecord_ng_cs_default_to_as_st_loc
   * @property {string} custrecord_ng_cs_default_transfer_from
   * @property {string} custrecord_ng_cs_dflt_booth_order_form
   * @property {string} custrecord_ng_cs_dflt_d_calc_date_types
   * @property {string} custrecord_ng_cs_dflt_exhibtr_form
   * @property {string} custrecord_ng_cs_dflt_labor_date_types
   * @property {string} custrecord_ng_cs_dflt_show_mgmt_ord_form
   * @property {string} custrecord_ng_cs_dflt_shw_tbl_form
   * @property {string} custrecord_ng_cs_discover
   * @property {boolean} custrecord_ng_cs_do_not_prompt_terms
   * @property {string} custrecord_ng_cs_dpst_rcpt_template
   * @property {boolean} custrecord_ng_cs_enable_freight_opts_opt
   * @property {boolean} custrecord_ng_cs_enable_graphics_option
   * @property {boolean} custrecord_ng_cs_enable_labor_matrix_opt
   * @property {boolean} custrecord_ng_cs_enable_orientation_opt
   * @property {boolean} custrecord_ng_cs_enable_paytrace
   * @property {boolean} custrecord_ng_cs_enable_rentals
   * @property {boolean} custrecord_ng_cs_enable_rentals_by_form
   * @property {boolean} custrecord_ng_cs_enforce_item_max_qty
   * @property {boolean} custrecord_ng_cs_event_selection_info
   * @property {boolean} custrecord_ng_cs_exempt_estimated_items
   * @property {string} custrecord_ng_cs_exhb_inv_email_template
   * @property {string} custrecord_ng_cs_exhb_kit_folder_id
   * @property {string} custrecord_ng_cs_exhb_kit_path
   * @property {string} custrecord_ng_cs_exhb_wo_exluded_cats
   * @property {string} custrecord_ng_cs_exhib_invoice_sender
   * @property {string} custrecord_ng_cs_exhibitor_serv_phone
   * @property {string} custrecord_ng_cs_evt_mm_attch_folder_id
   * @property {string} custrecord_ng_cs_evt_mm_temp_folder_id
   * @property {string} custrecord_ng_cs_fclty_addy_template
   * @property {string} custrecord_ng_cs_file_upload_folder_id
   * @property {string} custrecord_ng_cs_freight_minimum
   * @property {boolean} custrecord_ng_cs_gen_rand_email
   * @property {boolean} custrecord_ng_cs_give_contacts_access
   * @property {string} custrecord_ng_cs_graphics_item_cat
   * @property {string} custrecord_ng_cs_header_logo_url
   * @property {boolean} custrecord_ng_cs_hide_bthchklst_cnt_info
   * @property {string} custrecord_ng_cs_import_log_record_id
   * @property {string} custrecord_ng_cs_import_log_search_id
   * @property {string} custrecord_ng_cs_inv_email_conditions
   * @property {string} custrecord_ng_cs_inv_fail_cc
   * @property {string} custrecord_ng_cs_inv_fail_recip
   * @property {string} custrecord_ng_cs_inv_fail_sender
   * @property {string} custrecord_ng_cs_inv_transfer_type
   * @property {string} custrecord_ng_cs_item_rprts_exluded_cats
   * @property {string} custrecord_ng_cs_job_num_prefix
   * @property {string} custrecord_ng_cs_log_time_zone
   * @property {string} custrecord_ng_cs_mass_booth_delete_roles
   * @property {string} custrecord_ng_cs_mastercard
   * @property {string} custrecord_ng_cs_matrix_option_web
   * @property {string} custrecord_ng_cs_name_from_subsidiary
   * @property {string} custrecord_ng_cs_name_number_ordering
   * @property {string} custrecord_ng_cs_name_number_separator
   * @property {string} custrecord_ng_cs_navbar_bckgrnd_color
   * @property {boolean} custrecord_ng_cs_no_billed_order_editing
   * @property {string} custrecord_ng_cs_no_prompt_under_zero
   * @property {string} custrecord_ng_cs_payment_ar_account
   * @property {string} custrecord_ng_cs_payment_type
   * @property {string} custrecord_ng_cs_pre_invoicing_form
   * @property {string} custrecord_ng_cs_prev_adtl_orders
   * @property {string} custrecord_ng_cs_prev_bo_redir_alert
   * @property {boolean} custrecord_ng_cs_prompt_exclusion_roles
   * @property {boolean} custrecord_ng_cs_prompt_for_new_line
   * @property {string} custrecord_ng_cs_pymnt_fail_eml_template
   * @property {string} custrecord_ng_cs_pymt_rcpt_template
   * @property {string} custrecord_ng_cs_rand_email_domain
   * @property {string} custrecord_ng_cs_rand_email_prefix
   * @property {string} custrecord_ng_cs_rental_forms
   * @property {string} custrecord_ng_cs_report_item_display
   * @property {string} custrecord_ng_cs_report_line_shade_hex
   * @property {string} custrecord_ng_cs_report_xml_folder_id
   * @property {boolean} custrecord_ng_cs_retain_last_item_cat
   * @property {boolean} custrecord_ng_cs_retain_last_show
   * @property {boolean} custrecord_ng_cs_sales_tax_is_on_lines
   * @property {boolean} custrecord_ng_cs_send_exhib_invoice
   * @property {boolean} custrecord_ng_cs_send_invoice_fail_email
   * @property {boolean} custrecord_ng_cs_send_web_pymnt_email
   * @property {string} custrecord_ng_cs_settings_access
   * @property {string} custrecord_ng_cs_shade_alt_report_lines
   * @property {string} custrecord_ng_cs_show_audit_form
   * @property {string} custrecord_ng_cs_show_calendar_id
   * @property {string} custrecord_ng_cs_show_mgmt_ord_types
   * @property {string} custrecord_ng_cs_show_mgt_forms
   * @property {boolean} custrecord_ng_cs_simple_job_numbering
   * @property {string} custrecord_ng_cs_supervisor_item
   * @property {string} custrecord_ng_cs_tax_auto_sel_txt_filter
   * @property {string} custrecord_ng_cs_transfer_count_markup
   * @property {string} custrecord_ng_cs_trnsfr_exmpt_cats
   * @property {boolean} custrecord_ng_cs_use_alt_forms
   * @property {boolean} custrecord_ng_cs_use_avalara_tax_message
   * @property {boolean} custrecord_ng_cs_use_canadian_sales_tax
   * @property {boolean} custrecord_ng_cs_use_cancl_charge
   * @property {boolean} custrecord_ng_cs_use_cc_conv_fee
   * @property {boolean} custrecord_ng_cs_use_custom_job
   * @property {boolean} custrecord_ng_cs_use_job_numbering
   * @property {boolean} custrecord_ng_cs_use_multi_cc_proc
   * @property {boolean} custrecord_ng_cs_use_pre_invoicing
   * @property {boolean} custrecord_ng_cs_use_scripted_pynt_frm
   * @property {boolean} custrecord_ng_cs_use_show_auditing
   * @property {boolean} custrecord_ng_cs_use_show_tax
   * @property {boolean} custrecord_ng_cs_use_undep_funds
   * @property {string} custrecord_ng_cs_visa
   * @property {string} custrecord_ng_cs_web_img_folder_id
   * @property {string} custrecord_ng_cs_web_pymnt_fail_cc
   * @property {string} custrecord_ng_cs_web_pymnt_fail_recip
   * @property {string} custrecord_ng_cs_web_pymnt_notice_sender
   * @property {string} custrecord_ng_cs_web_welcome_blurb
   * @property {string} custrecord_ng_cs_wo_img
   * @property {string} custrecord_ng_cs_wo_logo_img_url
   * @property {string} custrecord_ng_cs_wrhs_addy_template
   */

  return {
    settings: _cs_settings, // CS_Settings
    func: _csFunc,
    trigger: _trigger,
  };
});
