import axios from "axios";
import <PERSON><PERSON><PERSON> from "oauth-1.0a";
import crypto from "crypto";
import pRetry from "p-retry";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

// Request deduplication map to prevent multiple simultaneous requests to the same event
const pendingRequests = new Map();

// Cleanup old requests every 5 minutes to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  for (const [key, promise] of pendingRequests.entries()) {
    // Check if promise has been pending for more than 2 minutes
    if (promise._startTime && (now - promise._startTime) > 120000) {
      console.log(`[Cleanup] Removing stale request: ${key}`);
      pendingRequests.delete(key);
    }
  }
}, 300000); // Run every 5 minutes

// OAuth configuration
const oauth = OAuth({
  consumer: {
    key: process.env.OAUTH1_CONSUMER_KEY,
    secret: process.env.OAUTH1_CONSUMER_SECRET,
  },
  signature_method: "HMAC-SHA256",
  hash_function(base_string, key) {
    return crypto
      .createHmac("sha256", key)
      .update(base_string)
      .digest("base64");
  },
  realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
});

// Create axios instance with optimized configuration
const axiosInstance = axios.create({
  timeout: 15000, // 15 second timeout
  headers: {
    "Content-Type": "application/json",
    "Accept-Encoding": "gzip, deflate", // Enable compression
  },
  // Keep connections alive for better performance
  httpAgent: new (require("http").Agent)({ keepAlive: true }),
  httpsAgent: new (require("https").Agent)({ keepAlive: true }),
  maxRedirects: 5,
  validateStatus: (status) => status < 500, // Don't throw on 4xx errors
});

// Performance monitoring helper
const measurePerformance = async (operation, fn) => {
  const startTime = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    
    // Log performance metrics in development
    if (process.env.NODE_ENV === "development") {
      console.log(`[Performance] ${operation} completed in ${duration}ms`);
    }
    
    return { result, duration };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Performance] ${operation} failed after ${duration}ms`);
    throw error;
  }
};

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const { eid } = req.query;
    
    // Validate event ID
    if (!eid) {
      return res.status(400).json({ error: "Event ID is required" });
    }

    // Create a unique key for request deduplication
    const requestKey = `${session.user.id}-${eid}`;
    
    // Check if there's already a pending request for this user/event combination
    if (pendingRequests.has(requestKey)) {
      console.log(`[Dedup] Waiting for existing request: ${requestKey}`);
      
      try {
        // Wait for the existing request to complete and return its result
        const existingResult = await pendingRequests.get(requestKey);
        
        // Add cache headers for deduplicated response
        res.setHeader(
          "Cache-Control",
          "private, max-age=300, stale-while-revalidate=600"
        );
        res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
        res.setHeader("X-Request-Source", "deduplicated");
        
        return res.status(200).json(existingResult);
      } catch (error) {
        // If the existing request failed, fall through to make a new request
        console.log(`[Dedup] Existing request failed, making new request: ${requestKey}`);
        pendingRequests.delete(requestKey);
      }
    }

    // Build request URL
    const url = `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl`;
    const queryParams = {
      script: "customscript_get_event_ssr_details",
      deploy: "customdeploy_get_event_ssr_details",
      id: eid,
    };

    // Create full URL with query parameters
    const fullUrl = `${url}?${new URLSearchParams(queryParams).toString()}`;

    // Generate OAuth authorization header
    const request_data = {
      url: fullUrl,
      method: "GET",
    };

    const token = {
      key: process.env.OAUTH1_ACCESS_TOKEN,
      secret: process.env.OAUTH1_TOKEN_SECRET,
    };

    const authHeader = oauth.toHeader(oauth.authorize(request_data, token));

    // Create the request promise and store it for deduplication
    const requestPromise = (async () => {
      try {
        console.log(`[Request] Starting new request: ${requestKey}`);
        
        // Make the request to NetSuite with retry logic
        const fetchEventData = async () => {
          const response = await axiosInstance.get(fullUrl, {
            headers: {
              ...authHeader,
              Authorization: authHeader.Authorization,
            },
          });

          // Only accept 200 responses as successful
          if (response.status !== 200) {
            const error = new Error(`NetSuite API returned status ${response.status}`);
            error.response = response;
            throw error;
          }

          return response;
        };

        // Use p-retry for automatic retries on transient failures
        const { result: response, duration } = await measurePerformance(
          "NetSuite API call",
          () => pRetry(fetchEventData, {
            retries: 2, // Reduced retries to prevent long hangs
            minTimeout: 500, // Faster initial retry
            maxTimeout: 2000, // Reduced max timeout
            onFailedAttempt: (error) => {
              console.log(
                `NetSuite API attempt ${error.attemptNumber} failed. ${error.retriesLeft} retries left. Request: ${requestKey}`
              );
            },
            // Don't retry on 4xx errors (client errors)
            shouldRetry: (error) => {
              if (error.response && error.response.status >= 400 && error.response.status < 500) {
                return false;
              }
              return true;
            },
          })
        );

        console.log(`[Request] Completed successfully: ${requestKey}, Duration: ${duration}ms`);
        return { response, duration };
      } finally {
        // Always clean up the pending request
        pendingRequests.delete(requestKey);
      }
    })();

    // Store the request promise for deduplication with timestamp
    requestPromise._startTime = Date.now();
    pendingRequests.set(requestKey, requestPromise);

    // Wait for the request to complete
    const { response, duration } = await requestPromise;

    // Set cache headers for successful responses
    res.setHeader(
      "Cache-Control",
      "private, max-age=300, stale-while-revalidate=600"
    ); // Cache for 5 minutes, serve stale for 10
    res.setHeader("X-Content-Type-Options", "nosniff");
    
    // Add performance header
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
    res.setHeader("X-NetSuite-Time", `${duration}ms`);
    
    return res.status(200).json(response.data);
  } catch (error) {
    // Clean up pending request on error
    if (req.query.eid) {
      const requestKey = `${session?.user?.id}-${req.query.eid}`;
      pendingRequests.delete(requestKey);
    }

    // Log error for debugging
    console.error(`Event API Error [${req.query.eid}]:`, error.message);

    // Add performance header even on errors
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);

    // Handle specific error types
    if (error.code === "ECONNABORTED" || error.code === "ETIMEDOUT") {
      return res.status(504).json({
        error: "Request timeout - NetSuite API is taking too long to respond",
      });
    }

    if (error.response) {
      // NetSuite returned an error response
      return res.status(error.response.status || 500).json({
        error: "Error retrieving event from NetSuite",
        details: error.response.data,
      });
    }

    // Generic error response
    return res.status(500).json({
      error: "Internal server error while processing event request",
      message: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
}

// Disable body parsing for this API route as we don't need it
export const config = {
  api: {
    bodyParser: false,
  },
};
