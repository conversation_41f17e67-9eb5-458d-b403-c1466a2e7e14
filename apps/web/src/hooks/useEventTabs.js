import { useState, useEffect } from 'react';
import {
    InfoOutlined,
    ShoppingBasketOutlined,
    ExploreOutlined,
    MapOutlined,
    LocalShippingOutlined,
} from "@mui/icons-material";

const initialTabs = [
    { id: "info", label: "Information", icon: <InfoOutlined />, disabled: false },
    { id: "order", label: "Order", icon: <ShoppingBasketOutlined />, disabled: false },
    { id: "addresses", label: "Event Addresses", icon: <ExploreOutlined />, disabled: false },
    { id: "map", label: "Map View", icon: <MapOutlined />, disabled: true }, // API is not returning the map data
];

const bolTab = {
    id: "material",
    label: "Outbound Shipment Requests",
    icon: <LocalShippingOutlined />,
    disabled: false,
};

export const useEventTabs = (eventData) => {
    const [currentTab, setCurrentTab] = useState("info");
    const [tabs, setTabs] = useState(initialTabs);

    useEffect(() => {
        if (eventData?.details?.bolEnabled) {
            const bolTabExists = tabs.some((tab) => tab.id === "material");
            if (!bolTabExists) {
                console.log('Adding Bol Tab')
                tabs.push(bolTab)
                setTabs(tabs)
            }
        }
    }, [eventData]);

    return { currentTab, setCurrentTab, tabs };
}; 