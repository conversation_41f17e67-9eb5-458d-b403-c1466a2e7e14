<clientscript scriptid="customscript_ng_cs_cl_dlvry_by_prod_rprt">
  <description></description>
  <fieldchangedfunction>clientFieldChanged</fieldchangedfunction>
  <isinactive>F</isinactive>
  <lineinitfunction>clientLineInit</lineinitfunction>
  <name>NG CS Client Delivery By Product Report</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>F</notifyowner>
  <notifyuser>F</notifyuser>
  <pageinitfunction>clientPageInit</pageinitfunction>
  <postsourcingfunction>clientPostSourcing</postsourcingfunction>
  <recalcfunction>clientRecalc</recalcfunction>
  <saverecordfunction>clientSaveRecord</saverecordfunction>
  <scriptfile>[/SuiteScripts/CS UI Script Files/client/ng_cs_clientDeliveryByProductReport.js]</scriptfile>
  <validatedeletefunction>clientValidateDelete</validatedeletefunction>
  <validatefieldfunction>clientValidateField</validatefieldfunction>
  <validateinsertfunction>clientValidateInsert</validateinsertfunction>
  <validatelinefunction>clientValidateLine</validatelinefunction>
  <libraries>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.sc.library.js]</scriptfile>
    </library>
    <library>
      <scriptfile>[/SuiteScripts/CS UI Script Files/lib/newgen.cs.report.library.js]</scriptfile>
    </library>
  </libraries>
</clientscript>