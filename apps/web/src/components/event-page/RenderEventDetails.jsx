import React from "react";
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CircularProgress,
  Container,
  Grid,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import { motion } from "motion/react";
import EventDetailsCard from "components/containers/EventDetailsCard";

const RenderEventDetails = ({
  mobile,
  data,
  setSiteModalVisible,
  siteModalVisible,
  setWarehouseModalVisible,
  warehouseModalVisible,
  startDate,
  endDate,
}) => {
  if (!mobile) {
    return (
      <Grid
        container
        display="flex"
        alignItems="center"
        justifyContent="center"
        sx={{ paddingTop: "1em", paddingBottom: "2vh" }}
      >
        <Grid item xs={12}>
          <motion.div
            initial={{ y: -60, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {!data ? (
              <Card className="text-center">
                <CardContent>
                  <CardHeader
                    title={<Typography>Loading Event Details</Typography>}
                    className="text-center"
                    color="primary"
                    variant="h3"
                    sx={{ paddingTop: mobile ? "3vh" : null }}
                  />
                  <Container>
                    <Box
                      alignItems="center"
                      justifyContent="center"
                      display="flex"
                      className="margin-nav"
                      style={{ paddingTop: "1rem" }}
                    >
                      <CircularProgress color="secondary" size={41} />
                    </Box>
                  </Container>
                </CardContent>
              </Card>
            ) : (
              <Container>
                <EventDetailsCard
                  data={data}
                  siteAddress={data.details.address.facility}
                  warehouseAddress={data.details.address.warehouse}
                  toggleSiteAddressModal={() =>
                    setSiteModalVisible(!siteModalVisible)
                  }
                  toggleWarehouseModal={() =>
                    setWarehouseModalVisible(!warehouseModalVisible)
                  }
                  warehouseAddressModalVisible={warehouseModalVisible}
                  siteAddressModalVisible={siteModalVisible}
                  title={data.details.displayName || data.details.name}
                  date={`${startDate} - ${endDate}`}
                />
              </Container>
            )}
          </motion.div>
        </Grid>
      </Grid>
    );
  }

  return (
    <Grid
      container
      spacing={3}
      style={{ paddingTop: "1em", paddingBottom: "1vh" }}
    >
      <Grid item xs={12} display="flex">
        <motion.div
          style={{ display: "contents" }}
          initial={{ y: 60, opacity: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {!data ? (
            <Container>
              <Card className="text-center">
                <CardContent>
                  <CardHeader
                    className="text-center"
                    sx={{ paddingTop: mobile ? "3vh" : null }}
                    title={<Typography variant="h3">Event Details</Typography>}
                  />
                  <Container>
                    <Stack spacing={2}>
                      <Skeleton variant="text" height={40} />
                      <Skeleton variant="text" height={40} />
                      <Skeleton variant="text" height={40} />
                    </Stack>
                  </Container>
                </CardContent>
              </Card>
            </Container>
          ) : (
            <Container>
              <EventDetailsCard
                data={data}
                sx={{ marginTop: "3vh" }}
                siteAddress={data.details?.address.facility}
                warehouseAddress={data.details?.address.warehouse}
                toggleSiteAddressModal={() =>
                  setSiteModalVisible(!siteModalVisible)
                }
                toggleWarehouseModal={() =>
                  setWarehouseModalVisible(!warehouseModalVisible)
                }
                warehouseAddressModalVisible={warehouseModalVisible}
                siteAddressModalVisible={siteModalVisible}
                title={data.details?.name}
                date={`${startDate} - ${endDate}`}
              />
            </Container>
          )}
        </motion.div>
      </Grid>
    </Grid>
  );
};

export default RenderEventDetails; 