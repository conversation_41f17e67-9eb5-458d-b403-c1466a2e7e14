import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID;
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY;
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET;
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN;
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET;

export default async (req, res) => {
  const session = await getServerSession(req, res, authOptions);
  const { id: country, searchQuery } = req.query;

  if (session) {
    const currRequest = {
      url: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_get_countries&deploy=customdeploy_ng_cses_rl_get_countries&mode=province&country=${country}&searchQuery=${searchQuery}`,
      method: "GET",
    };

    await request({
      uri: currRequest.url,
      oauth: {
        consumer_key: CONSUMER_KEY,
        consumer_secret: CONSUMER_SECRET,
        token: TOKEN,
        token_secret: TOKEN_SECRET,
        signature_method: "HMAC-SHA256",
        realm: REALM,
        version: "1.0",
      },
      json: true,
      resolveWithFullResponse: true,
    })
      .then((response) => {
        if (response.status !== 200) {
          if (response.body?.error) {
            res.status(500).json({
              error: `Error getting states`,
              details: response.body,
            });
          }
        }

        res.status(200).json(response.body);
      })
      .catch((error) => {
        let body = error?.error;

        res.status(500).json({
          error: `Error getting states: ${error}`,
          data: body,
        });
      });
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
  }
};
