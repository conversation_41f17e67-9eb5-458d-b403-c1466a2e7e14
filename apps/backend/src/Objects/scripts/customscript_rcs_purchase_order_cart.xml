<restlet scriptid="customscript_rcs_purchase_order_cart">
  <description>RCS Payment restlet handles cart attributes needed at checkout for tax, event details, creating the sales order after purchase, and item price levels.</description>
  <isinactive>F</isinactive>
  <name>RCS RL Handle Payment Order</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteScripts/React ConventionSuite/rcs_handle_payment_order.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_rcs_purchase_order_cart">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>RELEASED</status>
      <title>RCS RL handle payment order</title>
    </scriptdeployment>
    <scriptdeployment scriptid="customdeploy_rcs_rl_order_processing">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole></audslctrole>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>RELEASED</status>
      <title>RCS RL handle payment order processing</title>
    </scriptdeployment>
  </scriptdeployments>
</restlet>