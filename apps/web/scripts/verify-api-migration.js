#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const API_DIR = path.join(__dirname, '../pages/api');

// List of all API files that need migration
const API_FILES = [
  'collection/items.js',
  'collection/[cid].js',
  'countries.js',
  'customer/account.js',
  'customer/cart/item_check.js',
  'customer/cart/[ceid].js',
  'customer/delete/address/[id].js',
  'customer/delete/card/[id].js',
  'customer/delete/delete-bill-of-lading.js',
  'customer/event/[eid].js',
  'customer/order.js',
  'customer/pay/paytracekey.js',
  'customer/payment/[slug].js',
  'customer/post/address.js',
  'customer/post/bill-of-lading.js',
  'customer/post/card.js',
  'customer/post/forgot-password.js',
  'customer/post/order.js',
  'customer/post/register.js',
  'customer/put/address.js',
  'customer/put/cancel-bill-of-lading.js',
  'customer/put/password.js',
  'customer/put/update-bill-of-lading.js',
  'customer/settings.js',
  'customer/shows.js',
  'customer/tracking.js',
  'product/search/[slug].js',
  'product/search.js',
  'product/[pid]/index.js',
  'product/[pid]/upload.js',
  'states/[id].js',
  'users/id-list.js',
  'users/[id].js'
];

function checkFileStatus(filePath) {
  const fullPath = path.join(API_DIR, filePath);
  
  if (!fs.existsSync(fullPath)) {
    return { status: 'missing', details: 'File does not exist' };
  }

  const content = fs.readFileSync(fullPath, 'utf8');
  
  // Check for old pattern
  const hasOldImport = content.includes('request-promise-native');
  const hasOldConstants = content.includes('const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID');
  
  // Check for new patterns (both shared utility and direct implementation)
  const hasSharedUtilityImport = content.includes('makeNetSuiteRequest');
  const hasDirectAxiosImport = content.includes('import axios from "axios"');
  const hasPerformanceHeaders = content.includes('X-Response-Time');
  const hasProperErrorHandling = content.includes('handleApiError') || content.includes('catch (error)');
  const hasNamedFunction = content.includes('export default async function');
  const hasTimeout = content.includes('timeout:') || content.includes('15000');
  const hasRetryLogic = content.includes('pRetry') || content.includes('retries');
  
  // Consider it migrated if it uses either pattern and has modern optimizations
  const hasMigrationPattern = hasSharedUtilityImport || hasDirectAxiosImport;
  
  if (hasOldImport) {
    return { 
      status: 'needs_migration', 
      details: 'Still using request-promise-native',
      patterns: {
        oldImport: hasOldImport,
        oldConstants: hasOldConstants,
        sharedUtility: hasSharedUtilityImport,
        directAxios: hasDirectAxiosImport,
        performanceHeaders: hasPerformanceHeaders,
        errorHandling: hasProperErrorHandling,
        namedFunction: hasNamedFunction,
        timeout: hasTimeout,
        retryLogic: hasRetryLogic
      }
    };
  }
  
  if (hasMigrationPattern && hasPerformanceHeaders && hasProperErrorHandling) {
    const implementation = hasSharedUtilityImport ? 'shared utility' : 'direct axios';
    return { 
      status: 'migrated', 
      details: `Successfully migrated using ${implementation}`,
      patterns: {
        oldImport: hasOldImport,
        oldConstants: hasOldConstants,
        sharedUtility: hasSharedUtilityImport,
        directAxios: hasDirectAxiosImport,
        performanceHeaders: hasPerformanceHeaders,
        errorHandling: hasProperErrorHandling,
        namedFunction: hasNamedFunction,
        timeout: hasTimeout,
        retryLogic: hasRetryLogic
      }
    };
  }
  
  if (hasMigrationPattern) {
    return { 
      status: 'partial', 
      details: 'Has new HTTP library but missing some optimizations',
      patterns: {
        oldImport: hasOldImport,
        oldConstants: hasOldConstants,
        sharedUtility: hasSharedUtilityImport,
        directAxios: hasDirectAxiosImport,
        performanceHeaders: hasPerformanceHeaders,
        errorHandling: hasProperErrorHandling,
        namedFunction: hasNamedFunction,
        timeout: hasTimeout,
        retryLogic: hasRetryLogic
      }
    };
  }
  
  return { 
    status: 'unknown', 
    details: 'Unknown migration state',
    patterns: {
      oldImport: hasOldImport,
      oldConstants: hasOldConstants,
      sharedUtility: hasSharedUtilityImport,
      directAxios: hasDirectAxiosImport,
      performanceHeaders: hasPerformanceHeaders,
      errorHandling: hasProperErrorHandling,
      namedFunction: hasNamedFunction,
      timeout: hasTimeout,
      retryLogic: hasRetryLogic
    }
  };
}

function generateReport() {
  console.log('🔍 API Migration Status Report');
  console.log('================================\n');
  
  const results = {
    migrated: [],
    needs_migration: [],
    partial: [],
    missing: [],
    unknown: []
  };
  
  API_FILES.forEach(filePath => {
    const status = checkFileStatus(filePath);
    results[status.status].push({ path: filePath, ...status });
  });
  
  // Summary
  console.log('📊 Summary:');
  console.log(`✅ Migrated: ${results.migrated.length}`);
  console.log(`⚠️  Needs Migration: ${results.needs_migration.length}`);
  console.log(`🔄 Partial: ${results.partial.length}`);
  console.log(`❌ Missing: ${results.missing.length}`);
  console.log(`❓ Unknown: ${results.unknown.length}`);
  console.log(`📈 Progress: ${((results.migrated.length / API_FILES.length) * 100).toFixed(1)}%\n`);
  
  // Detailed results
  if (results.migrated.length > 0) {
    console.log('✅ Successfully Migrated Files:');
    results.migrated.forEach(file => {
      console.log(`   ${file.path} - ${file.details}`);
    });
    console.log('');
  }
  
  if (results.needs_migration.length > 0) {
    console.log('⚠️  Files Needing Migration:');
    results.needs_migration.forEach(file => {
      console.log(`   ${file.path} - ${file.details}`);
    });
    console.log('');
  }
  
  if (results.partial.length > 0) {
    console.log('🔄 Partially Migrated Files:');
    results.partial.forEach(file => {
      console.log(`   ${file.path} - ${file.details}`);
      if (file.patterns) {
        console.log(`      Shared Utility: ${file.patterns.sharedUtility ? '✅' : '❌'}`);
        console.log(`      Direct Axios: ${file.patterns.directAxios ? '✅' : '❌'}`);
        console.log(`      Performance Headers: ${file.patterns.performanceHeaders ? '✅' : '❌'}`);
        console.log(`      Error Handling: ${file.patterns.errorHandling ? '✅' : '❌'}`);
        console.log(`      Timeout: ${file.patterns.timeout ? '✅' : '❌'}`);
        console.log(`      Retry Logic: ${file.patterns.retryLogic ? '✅' : '❌'}`);
      }
    });
    console.log('');
  }
  
  if (results.missing.length > 0) {
    console.log('❌ Missing Files:');
    results.missing.forEach(file => {
      console.log(`   ${file.path}`);
    });
    console.log('');
  }
  
  if (results.unknown.length > 0) {
    console.log('❓ Unknown Status Files:');
    results.unknown.forEach(file => {
      console.log(`   ${file.path} - ${file.details}`);
    });
    console.log('');
  }
  
  // Next steps
  console.log('🎯 Next Steps:');
  if (results.needs_migration.length > 0) {
    console.log('1. Start with high-priority files from MIGRATION_GUIDE.md');
    console.log('2. Follow the migration pattern in the guide');
    console.log('3. Test each endpoint after migration');
  }
  if (results.partial.length > 0) {
    console.log('4. Complete partial migrations');
  }
  if (results.migrated.length === API_FILES.length) {
    console.log('🎉 All files have been migrated! Consider running performance tests.');
  }
  
  return results;
}

// Performance check function
function checkPerformanceFeatures() {
  console.log('\n🚀 Performance Features Check');
  console.log('==============================\n');
  
  const libPath = path.join(__dirname, '../lib/netsuite-api.js');
  if (!fs.existsSync(libPath)) {
    console.log('❌ NetSuite API utility not found at lib/netsuite-api.js');
    return;
  }
  
  const libContent = fs.readFileSync(libPath, 'utf8');
  
  const features = {
    'Axios with keep-alive': libContent.includes('keepAlive: true'),
    'Request timeout': libContent.includes('timeout: 15000'),
    'Compression support': libContent.includes('Accept-Encoding'),
    'Retry logic': libContent.includes('pRetry'),
    'Performance monitoring': libContent.includes('measurePerformance'),
    'OAuth integration': libContent.includes('oauth.authorize'),
    'Error handling': libContent.includes('handleApiError'),
    'Cache headers': libContent.includes('setOptimizedHeaders')
  };
  
  Object.entries(features).forEach(([feature, enabled]) => {
    console.log(`${enabled ? '✅' : '❌'} ${feature}`);
  });
  
  const enabledCount = Object.values(features).filter(Boolean).length;
  console.log(`\n📈 Performance Features: ${enabledCount}/${Object.keys(features).length} enabled`);
}

if (require.main === module) {
  try {
    generateReport();
    checkPerformanceFeatures();
  } catch (error) {
    console.error('Error running migration check:', error.message);
    process.exit(1);
  }
}

module.exports = { checkFileStatus, generateReport }; 