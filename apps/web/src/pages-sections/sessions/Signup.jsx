import React, { useCallback, useState } from "react";
import {
  <PERSON>ert,
  AlertTitle,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
} from "@mui/material";
import Link from "next/link";
import * as yup from "yup";
import Image from "next/image";
import { useFormik } from "formik";
import { H1, H6 } from "components/Typography";
import BazaarTextField from "components/BazaarTextField";
import { FlexRowCenter } from "components/flex-box";
import { Wrapper } from "./Login";
import TermsDisplayModal from "../../components/modals/TermsDisplayModal";
import MuiLink from "@mui/material/Link";

const Signup = ({ settings, pathname }) => {
  const [termsModalOpen, setTermsModalOpen] = useState(false);
  const [passwordVisibility, setPasswordVisibility] = useState(false);
  const togglePasswordVisibility = useCallback(() => {
    setPasswordVisibility((visible) => !visible);
  }, []);
  const [emailSent, setEmailSent] = useState(false);
  const [emailFound, setEmailFound] = useState(null);

  const getNavigationLink = (path) => {
    if (
      path.includes("auth") &&
      (pathname === "/" || pathname === "/register")
    ) {
      // Remove auth out of the path if it's the first page
      return path.replace("auth/", "");
    } else {
      return path;
    }
  };

  const handleFormSubmit = async (values, setSubmitting, resetForm) => {
    setSubmitting(true);

    console.log("");

    await fetch("/api/customer/post/register", {
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({
        ...values,
        redirect: `${window.location.origin}/auth/login`,
      }),
    })
      .then(async (res) => {
        console.log("Register Data: ", res);

        if (res.status === 200) {
          const newRes = await res.body;

          console.log("Register Data json: ", newRes);

          return await res.json();
        } else {
          setSubmitting(false);
          setEmailSent(false);
          return await res.json();
        }
      })
      .then((data) => {
        console.log("Email response: ", data);
        if (data.status === 200) {
          if (data.type === "EMAIL_FOUND_PASSWORD_RESET") {
            setEmailSent(true);
            setEmailFound("yes-netsuite-only");
          } else if (data.type === "EMAIL_FOUND_NO_ROLE") {
            setEmailSent(true);
            setEmailFound("yes-custom-template-no-role");
          } else if (data.type === "EMAIL_FOUND_AND_ACCOUNT_STARTED") {
            setEmailFound("yes-custom-template-start");
            setEmailSent(true);
          } else if (data.type === "EMAIL_FOUND_ACCOUNT_GRANTED") {
            setEmailFound("yes-custom-template-granted-already");
            setEmailSent(true);
          }
          setSubmitting(false);
        } else if (data.status === 404) {
          setEmailFound("no");
          setSubmitting(false);
          setEmailSent(false);
        }
      })
      .catch((err) => {
        console.error(err);
        setSubmitting(false);
        setEmailSent(false);
      });
  };
  const {
    values,
    errors,
    touched,
    handleBlur,
    handleChange,
    handleSubmit,
    resetForm,
    isSubmitting,
    setSubmitting,
  } = useFormik({
    initialValues,
    onSubmit: async () => handleFormSubmit(values, setSubmitting, resetForm),
    validationSchema: formSchema,
  });

  return (
    <Wrapper elevation={3} passwordVisibility={passwordVisibility}>
      <form onSubmit={handleSubmit}>
        <Box
          sx={{
            position: "relative",
            display: "block",
            maxHeight: 180,
            width: "100%",
            m: "auto",
          }}
        >
          <Image
            priority
            height={180}
            width={500}
            style={{ objectFit: "contain", width: "100%" }}
            alt="Login company image"
            src={settings?.custrecord_ng_cs_header_logo_url}
          />
        </Box>

        <H1 textAlign="center" mt={1} mb={4} fontSize={16}>
          Registration
        </H1>

        {/* <BazaarTextField */}
        {/*  mb={1.5} */}
        {/*  fullWidth */}
        {/*  name="name" */}
        {/*  size="small" */}
        {/*  label="Full Name" */}
        {/*  variant="outlined" */}
        {/*  onBlur={handleBlur} */}
        {/*  value={values.name} */}
        {/*  onChange={handleChange} */}
        {/*  placeholder="Ralph Edwards" */}
        {/*  error={!!touched.name && !!errors.name} */}
        {/*  helperText={touched.name && errors.name} */}
        {/* /> */}

        {emailSent && emailFound === "yes-netsuite-only" && (
          <Alert severity="success" sx={{ mb: 2 }}>
            <AlertTitle>
              <b>Check your inbox!</b>
            </AlertTitle>
            We sent you an email to complete your registration coming from{" "}
            <b>NetSuite</b>. This will allow you to set your password and login.
            Please reach out to <b>exhibitor services</b> if you do not receive
            the email within 3-5 minutes.
          </Alert>
        )}

        {emailSent && emailFound === "yes-custom-template-no-role" && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <AlertTitle>
              <b>Check your inbox!</b>
            </AlertTitle>
            We sent you an email to complete your registration. This will allow
            you to login with your <b>temporary password</b>. We noticed your
            permissions are not set up with the correct role. Please reach out
            to <b>exhibitor services</b> if you do not receive the email within
            3-5 minutes or experience login issues.
          </Alert>
        )}

        {emailSent && emailFound === "yes-custom-template-start" && (
          <Alert severity="success" sx={{ mb: 2 }}>
            <AlertTitle>
              <b>Check your inbox!</b>
            </AlertTitle>
            We sent you an email to <b>complete your registration</b>. This will
            allow you to login with your <b>temporary password</b>. Please reach
            out to <b>exhibitor services</b> if you do not receive the email
            within 3-5 minutes.
          </Alert>
        )}

        {emailFound === "yes-custom-template-granted-already" && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <AlertTitle>
              <b>You're already registered!</b>
            </AlertTitle>
            Seems your email is already registered. Please go to the{" "}
            <Link href={getNavigationLink("/auth/login")}>
              <MuiLink>login</MuiLink>
            </Link>{" "}
            page to sign in. If you simply forgot your password, please reset it
            on the login page. If you have any issues, please reach out to{" "}
            <b>exhibitor services</b>.
          </Alert>
        )}

        {emailFound === "no" && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <AlertTitle>
              <b>Email not found!</b>
            </AlertTitle>
            We could not find your email in our system. Please reach out to{" "}
            <b>exhibitor services</b> for assistance with registration.
          </Alert>
        )}

        <BazaarTextField
          mb={1.5}
          fullWidth
          name="email"
          size="small"
          type="email"
          variant="outlined"
          onBlur={handleBlur}
          value={values.email}
          onChange={handleChange}
          label="Email"
          placeholder="<EMAIL>"
          error={!!touched.email && !!errors.email}
          helperText={touched.email && errors.email}
        />

        {/* <BazaarTextField */}
        {/*  mb={1.5} */}
        {/*  fullWidth */}
        {/*  size="small" */}
        {/*  name="password" */}
        {/*  label="Password" */}
        {/*  variant="outlined" */}
        {/*  autoComplete="on" */}
        {/*  placeholder="*********" */}
        {/*  onBlur={handleBlur} */}
        {/*  onChange={handleChange} */}
        {/*  value={values.password} */}
        {/*  type={passwordVisibility ? "text" : "password"} */}
        {/*  error={!!touched.password && !!errors.password} */}
        {/*  helperText={touched.password && errors.password} */}
        {/*  InputProps={{ */}
        {/*    endAdornment: ( */}
        {/*      <EyeToggleButton */}
        {/*        show={passwordVisibility} */}
        {/*        click={togglePasswordVisibility} */}
        {/*      /> */}
        {/*    ), */}
        {/*  }} */}
        {/* /> */}

        {/* <BazaarTextField */}
        {/*  fullWidth */}
        {/*  size="small" */}
        {/*  autoComplete="on" */}
        {/*  name="re_password" */}
        {/*  variant="outlined" */}
        {/*  label="Retype Password" */}
        {/*  placeholder="*********" */}
        {/*  onBlur={handleBlur} */}
        {/*  onChange={handleChange} */}
        {/*  value={values.re_password} */}
        {/*  type={passwordVisibility ? "text" : "password"} */}
        {/*  error={!!touched.re_password && !!errors.re_password} */}
        {/*  helperText={touched.re_password && errors.re_password} */}
        {/*  InputProps={{ */}
        {/*    endAdornment: ( */}
        {/*      <EyeToggleButton */}
        {/*        show={passwordVisibility} */}
        {/*        click={togglePasswordVisibility} */}
        {/*      /> */}
        {/*    ), */}
        {/*  }} */}
        {/* /> */}

        <FormControlLabel
          required
          name="agreement"
          className="agreement"
          onChange={handleChange}
          control={
            <Checkbox
              size="small"
              color="secondary"
              checked={values.agreement || false}
            />
          }
          label={
            <Box sx={{ display: "inline-flex" }}>
              By registering, you agree to the
              <a href="#" onClick={() => setTermsModalOpen(!termsModalOpen)}>
                <H6 ml={1} borderBottom="1px solid" borderColor="grey.900">
                  Terms & Conditions
                </H6>
              </a>
            </Box>
          }
        />

        <TermsDisplayModal open={termsModalOpen} setOpen={setTermsModalOpen} />

        <Button
          fullWidth
          type="submit"
          color="primary"
          variant="contained"
          sx={{
            height: 44,
          }}
          disabled={isSubmitting}
          endIcon={
            isSubmitting ? (
              <CircularProgress color="secondary" size="1rem" />
            ) : null
          }
        >
          Create Account
        </Button>
      </form>
      <FlexRowCenter mt="1.25rem">
        <Box>Already have an account?</Box>
        <Link href={getNavigationLink("/auth/login")}>
          <H6 ml={1} borderBottom="1px solid" borderColor="grey.900">
            Login
          </H6>
        </Link>
      </FlexRowCenter>
    </Wrapper>
  );
};

const initialValues = {
  compid: process.env.NEXT_PUBLIC_ACCOUNT_URL_ID,
  email: "",
  Submit: "Continue",
  private: "T",
  agreement: false,
};
const formSchema = yup.object().shape({
  email: yup.string().email("invalid email").required("Email is required"),
  agreement: yup
    .bool()
    .test(
      "agreement",
      "You have to agree with our Terms and Conditions!",
      (value) => value === true,
    )
    .required("You have to agree with our Terms and Conditions!"),
});
export default Signup;
