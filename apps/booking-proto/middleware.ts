import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { decrypt } from '@event-services/auth'

export async function middleware(req: NextRequest) {
	try {
		console.log('Middleware started')

		const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET })

		if (!token) {
			console.log('No token found, redirecting to sign in')
			const signInUrl = new URL('/auth/signin', req.url)
			signInUrl.searchParams.set('returnUrl', req.url)
			return NextResponse.redirect(signInUrl)
		}

		console.log('Token found:', token)

		const exhibitorPortalUrl = token.exhibitorUrl as string

		if (!exhibitorPortalUrl) {
			console.log('Exhibitor portal URL not found')
			return new NextResponse('Exhibitor portal URL not found', { status: 400 })
		}

		console.log('Exhibitor portal URL:', exhibitorPortalUrl)

		// Fetch shared environment variables
		const sharedEnvUrl = new URL('/api/shared-env-token', exhibitorPortalUrl)
		console.log('Fetching shared environment variables from:', sharedEnvUrl.toString())

		const response = await fetch(sharedEnvUrl.toString(), {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token.accessToken}`,
			},
		})

		if (!response.ok) {
			console.error('Failed to fetch shared environment variables:', response.status, response.statusText)
			return new NextResponse('Failed to fetch shared environment variables', { status: 500 })
		}

		const responseData = await response.json()
		console.log('Response data:', responseData)

		if (!responseData.token) {
			console.error('No token in response data')
			return new NextResponse('No token in shared environment response', { status: 500 })
		}

		const decryptedData = await decrypt(responseData.token)
		console.log('Decrypted data:', decryptedData)

		const sharedEnvVars = JSON.parse(decryptedData)
		console.log('Parsed shared environment variables:', sharedEnvVars)

		const requestHeaders = new Headers(req.headers)
		requestHeaders.set('x-shared-env', JSON.stringify(sharedEnvVars))
		requestHeaders.set('x-exhibitor-portal-url', exhibitorPortalUrl)

		const res = NextResponse.next({
			request: {
				headers: requestHeaders,
			},
		})

		res.cookies.set('exhibitorPortalUrl', exhibitorPortalUrl, {
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'strict',
			maxAge: 60 * 60 * 24 * 7, // 1 week
		})

		return res
	} catch (error) {
		console.error('Middleware error:', error)
		return new NextResponse('Internal Server Error', { status: 500 })
	}
}

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - api (API routes)
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * - auth (auth pages)
		 */
		'/((?!api|_next/static|_next/image|favicon.ico|auth).*)',
	],
}
